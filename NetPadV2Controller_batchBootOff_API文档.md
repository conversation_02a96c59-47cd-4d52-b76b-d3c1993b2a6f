#### [**网存实例V2批量关机**](https://docs.armcloud.net/cn/server/OpenAPI.html#%E7%BD%91%E5%AD%98%E5%AE%9E%E4%BE%8BV2%E6%89%B9%E9%87%8F%E5%85%B3%E6%9C%BA)

用于对网存实例V2进行批量关机操作。支持同时关机多个实例，只有运行中或关机失败状态的实例才能进行关机操作。

**接口地址**

> /openapi/net/storage/batch/off

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名|示例值|参数类型|是否必填|参数描述|
|---|---|---|---|---|
|padCodes|["ACN250321HRKNE3F", "ACN250321HRKNE3G"]|String[]|是|需要关机的实例编码列表，最多允许同时关机200个实例|
|timeout|1800|Integer|否|超时时间（秒），范围：300-7200秒（5分钟-120分钟），默认30分钟|

**响应参数**

| 参数名         | 示例值              | 参数类型     | 参数描述          |
| ----------- | ---------------- | -------- | ------------- |
| code        | 200              | Integer  | 状态码（200表示成功）  |
| msg         | success          | String   | 接口请求状态信息      |
| ts          | 1742536327373    | Long     | 时间戳           |
| data        | [ {...} ]        | Object[] | 任务信息列表        |
| ├─ padCode  | ACN250321HRKNE3F | String   | 实例编码          |
| ├─ taskId   | 13024            | Long     | 后台任务ID        |
| ├─ subTaskId| 130241           | Long     | 子任务ID         |

**请求示例**

```json
{
  "padCodes": ["ACN250321HRKNE3F", "ACN250321HRKNE3G"],
  "timeout": 1800
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN250321HRKNE3F",
      "taskId": 13024,
      "subTaskId": 130241
    },
    {
      "padCode": "ACN250321HRKNE3G", 
      "taskId": 13024,
      "subTaskId": 130242
    }
  ],
  "ts": 1742536327373
}
```

**错误码**

| 错误码    | 错误说明                      | 操作建议                 |
| ------ | ------------------------- |----------------------|
| 100000 | 请求参数不能为空 | 检查请求参数是否正确传递         |
| 100000 | 实例编码列表不能为空 | 确保padCodes参数不为空      |
| 100000 | 实例数量范围1-200 | 确保实例数量在1-200范围内      |
| 100000 | 超时时间必须在5分钟-120分钟之间 | 设置合理的超时时间（300-7200秒） |
| 110028 | 实例不存在 | 检查实例编码是否正确           |
| 110071 | 存在非网存实例 | 确保操作的都是网存实例          |
| 110042 | 存在不属于当前用户的实例 | 检查实例归属权限             |
| 111071 | 非开机失败且未关机的实例不允许操作开机 | 确认实例状态为运行中或关机失败      |
| 111072 | 实例未绑定算力，无法关机 | 确保实例已绑定算力资源          |
| 111073 | 网存实例连续关机失败，请移除后重试 | 检查实例状态，联系管理员处理       |
| 111070 | 存在正在开机的实例，无法重复操作 | 等待当前操作完成后重试          |
| 111076 | 存在正在关机的实例，无法重复操作 | 等待当前操作完成后重试          |

