 package net.armcloud.paascenter.bmc.mapper;
 
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.List;
 
 @Mapper
 public interface CardTaskSubMapper extends BaseMapper<CardTaskSub> {
     /**
      * 插入任务
      *
      * @param cardTaskSub
      * @return
      */
     int insertCardTaskSub(CardTaskSub cardTaskSub);
 
     /**
      * 批量插入任务
      * @param list
      * @return
      */
     int insertBatch(@Param("list")  List<CardTaskSub> list);
 
     /**
      * 查询待调度的服务列表
      *
      * @return
      */
     List<String> selectExecutedServerSn(@Param("type") Integer type);
 }