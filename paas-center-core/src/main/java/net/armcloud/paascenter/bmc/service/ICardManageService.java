 package net.armcloud.paascenter.bmc.service;
 
 import net.armcloud.paascenter.common.lingdian.model.vo.*;
 import net.armcloud.paascenter.bmc.model.dto.*;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 
 import java.time.LocalDateTime;
 import java.util.List;
 
 public interface ICardManageService {
 
     /**
      * 根据节点ID查询soc url
      *
      * @param cardId node id
      * @return String
      */
     String getSocUrlByCardId(String cardId);
 
     /**
      * 查询arm server 所有板卡信息
      *
      * @param socApiUrl arm server api
      * @return List<ArmCardVO>
      */
     List<ArmCardVO> getCardsInfoService(String socApiUrl);
 
     /**
      * 获取所有节点网络信息
      *
      * @param socApiUrl arm server api
      * @return List<ArmCardNetworkVO>
      */
     List<ArmCardNetworkVO> getArmCardNetworkService(String socApiUrl);
 
     /**
      * 上传镜像
      *
      * @param socApiUrl
      * @return
      */
     Boolean uploadImagesService(String socApiUrl, Integer serverType, UploadImagesDTO uploadImagesDTO);
 
     /**
      * 删除镜像
      *
      * @param socApiUrl
      * @return
      */
     Boolean deleteImagesService(String socApiUrl, Integer serverType, String romName);
 
     /**
      * 查询ARM SERVER 节点信息
      *
      * @param socApiUrl
      * @return
      */
     List<NodeInfoVO> getNodesInfoService(String socApiUrl, String nodeId);
 
     /**
      * 节点下电并添加下电结果查询任务
      *
      * @return Boolean
      */
     Boolean powerOffAndAddResultTaskService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 节点下电结果并添加上电任务
      *
      * @return Boolean
      */
     Boolean powerOffResultAndPowerOnTaskService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 节点上电并添加上电结果查询任务
      *
      * @param
      * @return
      */
     Boolean powerOnAndAddResultTaskService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 节点上电结果并回调主任务结果
      *
      * @param
      * @return
      */
     Boolean powerOnResultAndCallbackTaskService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 设置节点网络设置任务
      *
      * @param serverSnList 待设置网络的SOC服务器
      * @return Boolean
      */
     Boolean setCardNetworkTaskService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 节点下电执行任务
      *
      * @param armCardId
      * @return
      */
     Boolean addCardPowerOffService(Long mainTaskId, String sn, String armCardId, LocalDateTime timeOut);
 
     /**
      * 添加节点重置任务
      *
      * @param parma 节点任务信息
      * @return Boolean
      */
     List<TaskVO> addCardResetService(ResetCardDTO parma);
 
     /**
      * 节点执行重置并添加节点重置结果查询任务
      *
      * @return Boolean
      */
     Boolean cardResetAndAddResultTaskService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 获取节点重置任务结果
      *
      * @return Boolean
      */
     Boolean getCardResetResultService(List<String> serverSnList, Integer limitTaskSize);
 
     /**
      * 节点执行恢复出厂设置任务
      *
      * @param serverSnList
      * @return
      */
     Boolean executeReCardInitService(List<String> serverSnList, Integer executeSize);
 
     /**
      * 节点执行刷机任务
      *
      * @param serverSnList
      * @return
      */
     Boolean executeReinstallService(List<String> serverSnList, Integer executeSize);
 
     /**
      * 获取节点网络信息
      * @param parma
      * @return List<CardNetworkInfoVO>
      */
     List<ArmCardNetworkVO> getCardNetworkInfo(CardNetworkInfoDTO parma);
     /**
      * 获取节点状态信息
      * @param parma
      * @return List<CardNetworkInfoVO>
      */
     List<ArmCardStatusVO> getCardStatusInfo(CardNetworkInfoDTO parma);
 }