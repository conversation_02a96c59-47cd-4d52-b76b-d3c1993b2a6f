 package net.armcloud.paascenter.bmc.constant;
 
 /**
  * 节点任务类型
  */
 public class CardTaskType {
     /**
      * 断电重启
      */
     public static final Integer POWER_RESTART = 1;
     /**
      * 初始化板卡网络信息网络
      */
     public static final Integer SET_NETWORK = 2;
     /**
      * 重置
      */
     public static final Integer RESET = 3;
     /**
      * 恢复出厂设置
      */
     public static final Integer REINIT = 4;
     /**
      * 上传镜像
      */
     public static final Integer UPLOAD_IMAGES = 5;
     /**
      * 板卡刷 Debian 系统
      */
     public static final Integer REINSTALL = 6;
 
     /**
      * 板卡刷 boot
      */
     public static final Integer FLASH_BOOT = 7;
 
 }