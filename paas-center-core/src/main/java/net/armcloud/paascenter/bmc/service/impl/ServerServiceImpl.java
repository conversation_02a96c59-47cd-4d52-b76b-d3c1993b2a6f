 package net.armcloud.paascenter.bmc.service.impl;
 
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.fastjson2.TypeReference;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.utils.StringUtils;
 import net.armcloud.paascenter.common.lingdian.model.dto.ImageDTO;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmCardVO;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmServerInfoVO;
 import net.armcloud.paascenter.common.lingdian.service.ILinDianApiService;
 import net.armcloud.paascenter.common.model.entity.bmc.Card;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 import net.armcloud.paascenter.common.model.entity.bmc.Server;
 import net.armcloud.paascenter.common.redis.contstant.KeyPrefix;
 import net.armcloud.paascenter.common.redis.contstant.KeyTime;
 import net.armcloud.paascenter.bmc.utils.BmcRedisService;
 import net.armcloud.paascenter.bmc.constant.CardTaskStatus;
 import net.armcloud.paascenter.bmc.constant.CardTaskSubType;
 import net.armcloud.paascenter.bmc.constant.CardTaskType;
 import net.armcloud.paascenter.bmc.exception.ManageExceptionCode;
 import net.armcloud.paascenter.bmc.mapper.CardMapper;
 import net.armcloud.paascenter.bmc.mapper.CardTaskMapper;
 import net.armcloud.paascenter.bmc.mapper.CardTaskSubMapper;
 import net.armcloud.paascenter.bmc.mapper.BmcServerMapper;
 import net.armcloud.paascenter.bmc.model.dto.DeleteArmServerDTO;
 import net.armcloud.paascenter.bmc.model.dto.DeleteImagesDTO;
 import net.armcloud.paascenter.bmc.model.dto.InitArmServerDTO;
 import net.armcloud.paascenter.bmc.model.dto.UploadImagesDTO;
 import net.armcloud.paascenter.bmc.model.vo.ArmServerInitVO;
 import net.armcloud.paascenter.bmc.model.vo.CardVO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 import net.armcloud.paascenter.bmc.service.ICardManageService;
 import net.armcloud.paascenter.bmc.service.ICardService;
 import net.armcloud.paascenter.bmc.service.ICardTaskService;
 import net.armcloud.paascenter.bmc.service.IServerService;
 import net.armcloud.paascenter.bmc.service.callback.CardTaskCallbackService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 import org.springframework.util.CollectionUtils;
 import org.springframework.util.ObjectUtils;
 
 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Map;
 import java.util.concurrent.TimeUnit;
 import java.util.function.Function;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.common.utils.DateUtils.plusMinutesDateTime;
 import static net.armcloud.paascenter.common.redis.contstant.KeyPrefix.*;
 import static net.armcloud.paascenter.bmc.configure.ArmCloudApiUrls.SERVER_STATUS_CALLBACK;
 import static net.armcloud.paascenter.bmc.constant.ArmServerType.*;
 
 @Slf4j
 @Service
 public class ServerServiceImpl extends ServiceImpl<BmcServerMapper, Server> implements IServerService {
 
     private @Autowired ICardManageService cardManageService;
     private @Autowired BmcRedisService bmcRedisService;
     private @Autowired ICardService cardService;
     private @Autowired ICardTaskService cardTaskService;
     private @Autowired CardTaskCallbackService cardTaskCallbackService;
     private @Autowired BmcServerMapper bmcServerMapper;
     private @Autowired CardTaskMapper cardTaskMapper;
     private @Autowired CardTaskSubMapper cardTaskSubMapper;
     private @Autowired ILinDianApiService linDianApiService;
 
     @Autowired
     private  CardMapper cardMapper;
 
     private void setSocUrlAndCardId(String socApiUrl, List<String> cardIds) {
         if (!CollectionUtils.isEmpty(cardIds)) {
             cardIds.forEach(cardId -> {
                 String nodeSocKey = KeyPrefix.LING_DIAN_NODE_SOC_URL + cardId;
                 bmcRedisService.setCacheObject(nodeSocKey, socApiUrl, KeyTime.minute_10, TimeUnit.MINUTES);
             });
         }
     }
 
     @Override
     public List<Server> getServerList() {
         Object serverListObject = bmcRedisService.getCacheObject(KeyPrefix.BMC_SERVER_LIST);
         if (serverListObject != null) {
             return JSON.parseObject(serverListObject.toString(), new TypeReference<List<Server>>() {
             });
         }
         List<Server> serverList = bmcServerMapper.selectList(new QueryWrapper<>());
         if (CollectionUtils.isEmpty(serverList)) {
             return null;
         }
         String jsonString = JSON.toJSONString(serverList);
 
         bmcRedisService.setCacheObject(KeyPrefix.BMC_SERVER_LIST, jsonString, KeyTime.minute_30, TimeUnit.MINUTES);
         return serverList;
     }
 
     @Override
     public void checkArmHeartbeatService() {
         List<Server> serverList = getServerList();
         if (CollectionUtils.isEmpty(serverList)) {
             return;
         }
         serverList.forEach(server -> {
             ArmServerInfoVO armServerInfoVO = null;
             if (LINGDIAN.equals(server.getServerType())) {
                 armServerInfoVO = linDianApiService.getServerInfoService(server.getServerUrl());
             }
             if (!ObjectUtils.isEmpty(armServerInfoVO)) {
                 cardTaskCallbackService.sendServerStatusCallback(SERVER_STATUS_CALLBACK, server.getSn(), 1, "on-line");
                 bmcRedisService.deleteObject(BMC_SERVER_HEARTBEAT_ERROR_COUNT + server.getId());
             }
 
             //累计心跳错误次数
             String errorCount = bmcRedisService.getCacheObject(BMC_SERVER_HEARTBEAT_ERROR_COUNT + server.getId());
             if (ObjectUtils.isEmpty(errorCount)) {
                 errorCount = "0";
             }
             int errorNum = Integer.parseInt(errorCount) + 1;
             bmcRedisService.setCacheObject(BMC_SERVER_HEARTBEAT_ERROR_COUNT + server.getId(), String.valueOf(errorNum));
             //大于三次上报离线
             if (Integer.parseInt(errorCount) >= 3) {
                 Boolean result = cardTaskCallbackService.sendServerStatusCallback(SERVER_STATUS_CALLBACK, server.getSn(), 0, "off-line");
                 if (!result) {
                     return;
                 }
                 bmcRedisService.deleteObject(BMC_SERVER_HEARTBEAT_ERROR_COUNT + server.getId());
             }
         });
     }
 
     @Override
     public ArmServerInitVO addArmServerAndCardInfo(InitArmServerDTO parma) {
         log.info("initArmServer start get server info time:{},parma={}", LocalDateTime.now(), parma);
         ArmServerInfoVO armServerInfoVO = linDianApiService.getServerInfoService(parma.getSocApiUrl());
         log.info("initArmServer end get server info time:{},socApiUrl={},armServerInfoVO={}", LocalDateTime.now(), parma.getSocApiUrl(), armServerInfoVO);
         if (ObjectUtils.isEmpty(armServerInfoVO)) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_OBTAIN_SERVER_INFORMATION);
         }
         //添加服务器信息
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", parma.getSocApiUrl()).last("limit 1"));
 
         Server saveOrUpdate = new Server();
         saveOrUpdate.setIp(StringUtils.getSocIp(parma.getSocApiUrl()));
         saveOrUpdate.setModel(armServerInfoVO.getModel());
         saveOrUpdate.setOsVersion(armServerInfoVO.getOsVersion());
         saveOrUpdate.setSn(armServerInfoVO.getSn());
         saveOrUpdate.setServerUrl(parma.getSocApiUrl());
         saveOrUpdate.setServerType(parma.getArmServerType());
         if (ObjectUtils.isEmpty(server)) {

             bmcServerMapper.insert(saveOrUpdate);
         } else {
             saveOrUpdate.setId(server.getId());
             bmcServerMapper.updateById(saveOrUpdate);
         }
         log.info("initArmServer start get card info time:{}", LocalDateTime.now());
         List<ArmCardVO> armCardVOS = linDianApiService.getCardsInfoService(parma.getSocApiUrl());
         log.info("initArmServer end get card info time:{},armCardVOS={}", LocalDateTime.now(), armCardVOS);
         if (CollectionUtils.isEmpty(armCardVOS)) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_OBTAIN_CARD_INFORMATION);
         }
 
         List<CardVO> cardVOS = armCardVOS.stream().map(armCardVO -> {
             CardVO cardVO = new CardVO();
             cardVO.setCardId(armCardVO.getId());
             cardVO.setMac(armCardVO.getMac());
 
             String[] positionArr = armCardVO.getPosition().split("-");
             cardVO.setNodeId(positionArr[0]);
             cardVO.setPosition(positionArr[1]);
             return cardVO;
         }).collect(Collectors.toList());
 
         List<String> cardIds = armCardVOS.stream().map(ArmCardVO::getId).collect(Collectors.toList());
         setSocUrlAndCardId(parma.getSocApiUrl(), cardIds);
 
         log.info("initArmServer start add card info time:{}", LocalDateTime.now());
         cardTaskService.addInitCardTaskService(parma, armCardVOS, saveOrUpdate.getId(), saveOrUpdate.getSn());
         log.info("initArmServer end add card info time:{}", LocalDateTime.now());
 
         ArmServerInitVO armServerInitVO = new ArmServerInitVO();
         armServerInitVO.setSn(armServerInfoVO.getSn());
         armServerInitVO.setCards(cardVOS);
 
         return armServerInitVO;
     }
 
     @Override
     public ArmServerInitVO pullArmServerAndCardInfo(InitArmServerDTO parma) {
         log.info("serverServiceImpl.pullArmServerAndCardInfo-请求入参:{}",JSON.toJSONString(parma));
         ArmServerInfoVO armServerInfoVO = linDianApiService.getServerInfoService(parma.getSocApiUrl());
         if (ObjectUtils.isEmpty(armServerInfoVO)) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_OBTAIN_SERVER_INFORMATION);
         }
 
         //添加服务器信息
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", parma.getSocApiUrl()).last("limit 1"));
 
         log.info("serverServiceImpl.pullArmServerAndCardInfo-获取ArmServer信息:{}",JSON.toJSONString(armServerInfoVO));
 
         List<ArmCardVO> armCardVOS = linDianApiService.getCardsInfoService(parma.getSocApiUrl());
         log.info("serverServiceImpl.pullArmServerAndCardInfo-获取板卡列表信息:{}",JSON.toJSONString(armCardVOS));
         if (CollectionUtils.isEmpty(armCardVOS)) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_OBTAIN_CARD_INFORMATION);
         }
         //查询出板卡之后与管理后台的板卡进行diff，保留管理后台不存在的绑卡信息,如果管理后台存在的绑卡，但是
         List<CardVO> cardVOS = armCardVOS.stream().map(armCardVO -> {
             CardVO cardVO = new CardVO();
             cardVO.setCardId(armCardVO.getId());
             cardVO.setMac(armCardVO.getMac());
 
             String[] positionArr = armCardVO.getPosition().split("-");
             cardVO.setNodeId(positionArr[0]);
             cardVO.setPosition(positionArr[1]);
             return cardVO;
         }).collect(Collectors.toList());
 
         List<String> cardIds = armCardVOS.stream().map(ArmCardVO::getId).collect(Collectors.toList());
         setSocUrlAndCardId(parma.getSocApiUrl(), cardIds);
 
         //查询已存在的板卡列表
         List<Card> cards = cardMapper.selectCardByServerId(server.getId());
         Map<String, Card> erpCodeMap = cards.stream().collect(Collectors.toMap(Card::getCardId, Function.identity(), (a, b) -> a));
         if (cards.size() >= cardVOS.size()){
             log.info("ARM服务器板卡数量:{},ARMCLOUD管理系统板卡数量:{}",cardVOS.size(),cards.size());
             return null;
         }
         //移除已存在的板卡列表
         armCardVOS.removeIf(armCardVO -> erpCodeMap.containsKey(armCardVO.getId()));
         cardTaskService.pullInitCardTaskService(parma, armCardVOS, server.getId(), armServerInfoVO.getSn());
         //存在则移除
         cardVOS.removeIf(card -> erpCodeMap.containsKey(card.getCardId()));
         ArmServerInitVO armServerInitVO = new ArmServerInitVO();
         armServerInitVO.setSn(armServerInfoVO.getSn());
         armServerInitVO.setCards(cardVOS);
         return armServerInitVO;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public Server getServerBySocUrl(String socApiUrl) {
         log.info("getServerBySocUrl start get server info time:{}", LocalDateTime.now());
         ArmServerInfoVO armServerInfoVO = linDianApiService.getServerInfoService(socApiUrl);
         log.info("getServerBySocUrl end get server info time:{}", LocalDateTime.now());
         if (ObjectUtils.isEmpty(armServerInfoVO)) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_OBTAIN_SERVER_INFORMATION);
         }
         //添加服务器信息
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", socApiUrl).last("limit 1"));
 
         Server saveOrUpdate = new Server();
         saveOrUpdate.setIp(StringUtils.getSocIp(socApiUrl));
         saveOrUpdate.setModel(armServerInfoVO.getModel());
         saveOrUpdate.setOsVersion(armServerInfoVO.getOsVersion());
         saveOrUpdate.setSn(armServerInfoVO.getSn());
         saveOrUpdate.setServerUrl(socApiUrl);
 
         if (ObjectUtils.isEmpty(server)) {
             bmcServerMapper.insert(saveOrUpdate);
         } else {
             saveOrUpdate.setId(server.getId());
             bmcServerMapper.updateById(saveOrUpdate);
         }
         return saveOrUpdate;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public Boolean deleteArmServerService(DeleteArmServerDTO parma) {
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", parma.getSocApiUrl()).eq("sn", parma.getArmSn()).last("limit 1"));
         if (ObjectUtils.isEmpty(server)) {
             throw new BasicException(ManageExceptionCode.ARM_SERVER_DOES_NOT_EXIST);
         }
         List<Card> cards = cardService.list(new QueryWrapper<Card>().eq("server_id", server.getId()));
         cards.forEach(card -> {
             bmcRedisService.deleteObject(KeyPrefix.BMC_CARD_LIST + card.getCardId());
         });
         bmcRedisService.deleteObject(BMC_SERVER_HEARTBEAT_ERROR_COUNT + server.getId());
         bmcRedisService.deleteObject(LING_DIAN_AUTH_TOKEN + server.getServerUrl());
         bmcRedisService.deleteObject(SERVER_DEVICE_IP + server.getIp());
         bmcRedisService.deleteObject(BMC_SERVER_LIST);
 
         bmcServerMapper.deleteById(server.getId());
         cardService.remove(new QueryWrapper<Card>().eq("server_id", server.getId()));
         return true;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public TaskVO uploadImagesService(UploadImagesDTO parma) {
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", parma.getSocApiUrl()).last("limit 1"));
         if (ObjectUtils.isEmpty(server)) {
             throw new BasicException(ManageExceptionCode.ARM_SERVER_NOT_EXIST);
         }
 
         Boolean isTrue = cardManageService.uploadImagesService(server.getServerUrl(), server.getServerType(), parma);
         if (!isTrue) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_ADD_IMAGE_UPLOAD_TASK);
         }
         CardTask addCardTask = new CardTask();
         addCardTask.setServerSn(server.getSn());
         addCardTask.setType(CardTaskType.UPLOAD_IMAGES);
         addCardTask.setTimeout(plusMinutesDateTime(parma.getTimeOut()));
         addCardTask.setTaskContent(JSON.toJSONString(parma));
         cardTaskMapper.insertCardTask(addCardTask);
 
         CardTaskSub addCardTaskSub = new CardTaskSub();
         addCardTaskSub.setMainTask(addCardTask.getId());
         addCardTaskSub.setTimeout(addCardTask.getTimeout());
         addCardTaskSub.setType(CardTaskSubType.UPLOAD_IMAGES);
         addCardTaskSub.setServerSn(server.getSn());
         cardTaskSubMapper.insertCardTaskSub(addCardTaskSub);
         return new TaskVO(addCardTask.getId(), null);
     }
 
     @Override
     public void handleUploadImagesResult() {
         List<CardTask> cardTasks = cardTaskService.list(new QueryWrapper<CardTask>().eq("type", CardTaskType.UPLOAD_IMAGES)
                 .eq("status", CardTaskStatus.PROCESSING).orderByDesc("create_time"));
 
         cardTasks.forEach(cardTask -> {
             if (cardTask.getTimeout().isBefore(LocalDateTime.now())) {
                 return;
             }
             if (ObjectUtils.isEmpty(cardTask.getTaskContent())) {
                 return;
             }
             UploadImagesDTO uploadImagesDTO = JSON.parseObject(cardTask.getTaskContent(), UploadImagesDTO.class);
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", cardTask.getServerSn()));
 
             if (LINGDIAN.equals(server.getServerType())) {
                 ImageDTO imageDTO = linDianApiService.getImagesByRoomNameService(server.getServerUrl(), uploadImagesDTO.getName());
                 if (ObjectUtils.isEmpty(imageDTO)) {
                     return;
                 }
             }
 
             CardTask updateCardTask = new CardTask();
             updateCardTask.setId(cardTask.getId());
             updateCardTask.setStatus(CardTaskStatus.SUCCESS);
             cardTaskMapper.updateById(updateCardTask);
 
             CardTaskSub cardTaskSub = cardTaskSubMapper.selectOne(new QueryWrapper<CardTaskSub>().eq("main_task", cardTask.getId()));
             CardTaskSub updateCardTaskSub = new CardTaskSub();
             updateCardTaskSub.setId(cardTaskSub.getId());
             updateCardTaskSub.setStatus(CardTaskStatus.SUCCESS);
             cardTaskSubMapper.updateById(updateCardTaskSub);
             //回调任务结果
         });
     }
 
     @Override
     public Boolean deleteImagesService(DeleteImagesDTO parma) {
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", parma.getSocApiUrl()).last("limit 1"));
         if (ObjectUtils.isEmpty(server)) {
             throw new BasicException(ManageExceptionCode.ARM_SERVER_NOT_EXIST);
         }
 
         Boolean isTrue = cardManageService.deleteImagesService(server.getServerUrl(), server.getServerType(), parma.getName());
         if (!isTrue) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_DELETE_IMAGE);
         }
         return true;
     }
 
 
 }