 package net.armcloud.paascenter.bmc.model.dto;
 
 import lombok.Data;
 
 import javax.validation.constraints.NotNull;
 
 @Data
 public class CardNetworkDTO {
     /**
      * 外部任务id
      */
     private String outTaskId;
     /**
      * 任务超时时间 (单位：分钟)
      */
     @NotNull(message = "timeOut cannot null")
     private Integer timeOut;
 
     @NotNull(message = "cardId cannot null")
     private String cardId;
     /**
      * ip
      */
     @NotNull(message = "ip cannot null")
     private String ip;
     /**
      * netmask
      */
     @NotNull(message = "netmask cannot null")
     private String netmask;
     /**
      * gateway
      */
     @NotNull(message = "gateway cannot null")
     private String gateway;
     /**
      * dns
      */
     @NotNull(message = "dns cannot null")
     private String dns;
     /**
      * arm server 机器序号
      */
     private String serverSn;
 
 }