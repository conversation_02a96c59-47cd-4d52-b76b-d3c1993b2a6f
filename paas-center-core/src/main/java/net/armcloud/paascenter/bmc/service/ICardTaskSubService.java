 package net.armcloud.paascenter.bmc.service;
 
 import com.baomidou.mybatisplus.extension.service.IService;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 
 import java.time.LocalDateTime;
 import java.util.List;
 
 public interface ICardTaskSubService extends IService<CardTaskSub> {
     /**
      * 查询子任务信息
      *
      * @param subId 子任务ID
      * @return CardTask
      */
     CardTaskSub getCardSubTaskById(Long subId);
 
     /**
      * 添加节点下电任务 TODO
      *
      * @param mainTaskId paas 任务ID
      * @param cardId     节点ID
      * @param timeout    任务超时时间
      * @return Boolean
      */
     CardTaskSub addCardSubTaskService(Long mainTaskId, String serverSn, Integer cardId, LocalDateTime timeout);
 
     /**
      * 修改任务状态
      *
      * @param id     主键ID
      * @param status 状态
      * @return Boolean
      */
     Boolean updateCardTaskSubService(Long id, Integer status);
 
 
     /**
      * 添加节点重置结果查询任务
      *
      * @param mainTask 节点重置主任务
      * @return CardTaskSub
      */
     CardTaskSub addCardResetResultTaskService(CardTask mainTask);
 
     /**
      * 获取未调度ARMServer
      *
      * @param subType 任务类型
      * @return
      */
     List<String> getExecutedServerSn(Integer subType);
 }