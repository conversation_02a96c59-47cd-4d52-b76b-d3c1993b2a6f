 package net.armcloud.paascenter.bmc.service;
 
 import com.baomidou.mybatisplus.extension.service.IService;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmCardVO;
 import net.armcloud.paascenter.bmc.model.dto.*;
 import net.armcloud.paascenter.bmc.model.vo.CardTaskInfoVO;
 import net.armcloud.paascenter.bmc.model.vo.PowerRestartTaskVO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 import net.armcloud.paascenter.common.model.entity.bmc.Server;
 
 import java.util.List;
 
 public interface ICardTaskService extends IService<CardTask> {
     /**
      * 检查节点是否正在执行任务
      *
      * @param cardIds cardIds
      * @return fail List
      */
     List<String> checkCardRunningTask(List<String> cardIds);
 
     /**
      * 添加节点断电重启任务
      *
      * @param tasks   paas 任务
      * @param timeOut 任务超时时间
      * @return Boolean
      */
     List<PowerRestartTaskVO> addCardPowerRestartTaskService(List<PowerRestartTaskDTO> tasks, Integer timeOut);
 
     /**
      * 修改任务状态
      *
      * @param id       主键ID
      * @param status   状态
      * @param syncFlag 是否同步paas
      * @return Boolean
      */
     Boolean updateCardTaskService(Long id, Integer status, Boolean syncFlag);
 
     /**
      * 修改子任务状态
      *
      * @param id     主键ID
      * @param status 状态
      * @return Boolean
      */
     Boolean updateCardTaskSubService(Long id, Integer status);
 
     /**
      * 添加节点开机子任务
      *
      * @param cardTasksub CardTaskSub
      * @return CardTaskSub
      */
     CardTaskSub addCardPowerOnTaskService(CardTaskSub cardTasksub);
 
     /**
      * 修改节点断电重启任务状态完成
      *
      * @param cardTasksub CardTaskSub
      * @param syncFlag    是否同步paas
      * @return CardTaskSub
      */
     Boolean updateCardPowerRestartTaskService(CardTask mainTask, CardTaskSub cardTasksub, Boolean syncFlag);
 
     /**
      * 添加节点设置网络任务
      *
      * @param cardNetworkInfos 节点网络信息
      * @return Boolean
      */
     List<TaskVO> addSetCardNetWorkTaskService(List<CardNetworkDTO> cardNetworkInfos);
 
     /**
      * @param taskId 任务ID
      * @return CardTaskInfoVO
      */
     CardTaskInfoVO getTaskInfoService(String taskId);
 
     /**
      * 添加节点重置任务
      *
      * @param cardTasks 节点任务信息
      * @param timeOut   任务超时时间
      * @return List<CardTaskSub>
      */
     List<TaskVO> addCardResetTaskService(List<CardTaskDTO> cardTasks, Integer timeOut);
 
     /**
      * 根据主任务ID获取任务信息
      *
      * @param mainId 主任务ID
      * @return CardTask
      */
     CardTask getCardTaskById(Long mainId);
 
     /**
      * 根据子任务ID获取任务信息
      *
      * @param subId 子任务ID
      * @return CardTask
      */
     CardTask getCardTaskBySubId(Long subId);
 
     /**
      * 根据子任务ID获取子任务信息
      *
      * @param subId
      * @return
      */
     CardTaskSub getCardTaskSubBySubId(Long subId);
 
     /**
      * 节点初始化任务
      *
      * @param parma      初始化参数
      * @param armCardVOS arm节点信息
      * @param serverId   arm server id
      * @return List<CardTaskSub>
      */
     List<TaskVO> addInitCardTaskService(InitArmServerDTO parma, List<ArmCardVO> armCardVOS, Long serverId, String sn);
 
     /**
      * 增量节点初始化任务
      *
      * @param parma      初始化参数
      * @param armCardVOS arm节点信息
      * @param serverId   arm server id
      * @return List<CardTaskSub>
      */
     List<TaskVO> pullInitCardTaskService(InitArmServerDTO parma, List<ArmCardVO> armCardVOS, Long serverId, String sn);
 
 
     /**
      * 处理超时任务
      *
      * @param cardTaskSub 超时子任务
      * @return Boolean
      */
     Boolean handleTimeoutTasksService(CardTaskSub cardTaskSub);
 
     /**
      * 处理超时任务
      *
      * @param cardTask
      * @param cardTaskSub
      * @return
      */
     Boolean handleTimeoutTaskDatabase(CardTask cardTask, CardTaskSub cardTaskSub);
 
     /**
      * 添加节点恢复出厂设置任务
      *
      * @return Boolean
      */
     List<TaskVO> addReCardInitTaskService(Server server, List<ArmCardVO> armCardVOS, Integer timeOut);
 
     /**
      * 获取未调度ARMServer
      *
      * @param type 任务类型
      * @return
      */
     List<String> getExecutedServerSn(Integer type);
 
     /**
      * 根据板卡任务类型集合 获取未调度ARMServer
      * @param types
      * @return
      */
     List<String> getExecutedServerByTypes(List<Integer> types);
 
     /**
      * 更新任务ID
      *
      * @param id
      * @return
      */
     Boolean updateCardTaskIdToNullService(Long id);
 
     /**
      * @return
      */
     Boolean syncCardInitTaskService();
 
     /**
      * 添加节点刷机任务
      *
      * @return
      */
     List<TaskVO> addReinstallTaskService(ReinstallDTO parma);
 
     /**
      * 添加paas节点设置网络任务
      *
      * @param cardNetworkInfos 节点网络信息
      * @return Boolean
      */
     List<PowerRestartTaskVO> addPaasSetCardNetworkTaskService(List<CardNetworkDTO> cardNetworkInfos);
 }