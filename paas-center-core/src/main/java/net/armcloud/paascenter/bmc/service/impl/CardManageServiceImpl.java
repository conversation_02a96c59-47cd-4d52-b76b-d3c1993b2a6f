 package net.armcloud.paascenter.bmc.service.impl;
 
 import com.alibaba.fastjson.JSONObject;
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.lingdian.model.dto.ImageDTO;
 import net.armcloud.paascenter.common.lingdian.model.dto.UpImageDTO;
 import net.armcloud.paascenter.common.lingdian.model.vo.*;
 import net.armcloud.paascenter.common.lingdian.service.ILinDianApiService;
 import net.armcloud.paascenter.common.model.entity.bmc.Card;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 import net.armcloud.paascenter.common.model.entity.bmc.Server;
 import net.armcloud.paascenter.common.redis.contstant.KeyPrefix;
 import net.armcloud.paascenter.common.redis.contstant.KeyTime;
 import net.armcloud.paascenter.bmc.utils.BmcRedisService;
 import net.armcloud.paascenter.bmc.constant.CardTaskStatus;
 import net.armcloud.paascenter.bmc.constant.CardTaskSubType;
 import net.armcloud.paascenter.bmc.constant.CardTaskType;
 import net.armcloud.paascenter.bmc.exception.ManageExceptionCode;
 import net.armcloud.paascenter.bmc.mapper.BmcServerMapper;
 import net.armcloud.paascenter.bmc.model.dto.CardNetworkDTO;
 import net.armcloud.paascenter.bmc.model.dto.CardNetworkInfoDTO;
 import net.armcloud.paascenter.bmc.model.dto.ResetCardDTO;
 import net.armcloud.paascenter.bmc.model.dto.UploadImagesDTO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 import net.armcloud.paascenter.bmc.service.ICardManageService;
 import net.armcloud.paascenter.bmc.service.ICardService;
 import net.armcloud.paascenter.bmc.service.ICardTaskService;
 import net.armcloud.paascenter.bmc.service.ICardTaskSubService;
 import net.armcloud.paascenter.bmc.service.callback.CardTaskCallbackService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
 import org.springframework.util.CollectionUtils;
 import org.springframework.util.ObjectUtils;
 
 import java.time.LocalDateTime;
 import java.time.ZoneId;
 import java.util.ArrayList;
 import java.util.Date;
 import java.util.List;
 import java.util.Objects;
 import java.util.concurrent.TimeUnit;
 import java.util.concurrent.atomic.AtomicReference;
 
 import static net.armcloud.paascenter.common.core.constant.Constants.REQUEST_FAIL;
 import static net.armcloud.paascenter.common.core.constant.Constants.REQUEST_SUCCESS;
 import static net.armcloud.paascenter.bmc.constant.NumberConst.*;
 import static net.armcloud.paascenter.common.lingdian.constant.CardPowerStatus.POWER_OFF;
 import static net.armcloud.paascenter.common.lingdian.constant.CardPowerStatus.RUNNING;
 import static net.armcloud.paascenter.common.redis.contstant.KeyPrefix.*;
 import static net.armcloud.paascenter.bmc.configure.ArmCloudApiUrls.DEVICE_STATUS_CALLBACK;
 import static net.armcloud.paascenter.bmc.configure.ArmCloudApiUrls.DEVICE_TASK_CALLBACK;
 import static net.armcloud.paascenter.bmc.constant.ArmServerType.LINGDIAN;
 
 @Slf4j
 @Service
 public class CardManageServiceImpl implements ICardManageService {
     private @Autowired BmcServerMapper bmcServerMapper;
     private @Autowired ICardService cardService;
     private @Autowired ICardTaskService cardTaskService;
     private @Autowired ICardTaskSubService cardTaskSubService;
     private @Autowired BmcRedisService bmcRedisService;
     private @Autowired CardTaskCallbackService cardTaskCallbackService;
     private @Autowired ILinDianApiService linDianApiService;
 
 
     public String getSocUrlByCardId(String cardId) {
         String nodeSocKey = KeyPrefix.LING_DIAN_NODE_SOC_URL + cardId;
         Object obj = bmcRedisService.getCacheObject(nodeSocKey);
         if (obj != null) {
             return obj.toString();
         } else {
             Card card = cardService.getCardByCardId(cardId);
             Server server = bmcServerMapper.selectById(card.getServerId());
             bmcRedisService.setCacheObject(nodeSocKey, server.getServerUrl(), KeyTime.day_1, TimeUnit.DAYS);
 
             return server.getServerUrl();
         }
     }
 
     @Override
     public List<ArmCardVO> getCardsInfoService(String socApiUrl) {
         List<ArmCardVO> armCards = linDianApiService.getCardsInfoService(socApiUrl);
         //补全节点信息 TODO 后续删除
         Card card = cardService.getOne(new QueryWrapper<Card>().eq("card_id", armCards.get(0).getId()));
         if (!ObjectUtils.isEmpty(card)) {
             if (ObjectUtils.isEmpty(card.getNodeId())) {
                 armCards.forEach(cardVO -> {
                     String[] positionArr = cardVO.getPosition().split("-");
                     Card updateCard = new Card();
                     updateCard.setNodeId(positionArr[0]);
                     updateCard.setPosition(positionArr[1]);
                     cardService.update(updateCard, new QueryWrapper<Card>().eq("card_id", cardVO.getId()));
                 });
             }
         }
         return armCards;
     }
 
     @Override
     public List<ArmCardNetworkVO> getArmCardNetworkService(String socApiUrl) {
         List<ArmCardNetworkVO> armCardNetworkVOS = new ArrayList<>();
         List<LdCardNetworkVO> ldCardNetworkVOS = linDianApiService.getArmCardNetworkService(socApiUrl);
         ldCardNetworkVOS.forEach(ldCardNetworkVO -> {
             armCardNetworkVOS.add(ldCardNetworkVO.getEth0());
         });
         return armCardNetworkVOS;
     }
 
 
     @Override
     public Boolean uploadImagesService(String socApiUrl, Integer serverType, UploadImagesDTO param) {
         if (LINGDIAN.equals(serverType)) {
             UpImageDTO upImageDTO = getLingDianUpImageDTO(param);
             return linDianApiService.uploadImagesService(socApiUrl, upImageDTO);
         }
         return false;
     }
 
     private UpImageDTO getLingDianUpImageDTO(UploadImagesDTO param) {
         UpImageDTO upImageDTO = new UpImageDTO();
         ImageDTO imagesDTO = new ImageDTO();
         imagesDTO.setName(param.getName());
         imagesDTO.setPlatform(param.getPlatform());
         imagesDTO.setSize(param.getSize());
         imagesDTO.setMd5(param.getMd5());
 
         ImageDTO.UrlDto urlDto = new ImageDTO.UrlDto();
         urlDto.setHttp_path(param.getUrl());
         imagesDTO.setUrl(urlDto);
         upImageDTO.setImage(imagesDTO);
         return upImageDTO;
     }
 
 
     @Override
     public Boolean deleteImagesService(String socApiUrl, Integer serverType, String romName) {
         if (LINGDIAN.equals(serverType)) {
             return linDianApiService.deleteImagesService(socApiUrl, romName);
         }
         return false;
     }
 
     @Override
     public List<NodeInfoVO> getNodesInfoService(String socApiUrl, String nodeId) {
         return linDianApiService.getNodesInfoService(socApiUrl, nodeId);
     }
 
     @Override
     public Boolean powerOffAndAddResultTaskService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
 
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.POWER_OFF).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
 
             cardSubTasks.forEach(cardSubTask -> {
                 CardTask cardTask = cardTaskService.getCardTaskById(cardSubTask.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask)) {
                     return;
                 }
                 Boolean flag = false;
                 if (LINGDIAN.equals(server.getServerType())) {
                     flag = linDianApiService.armCardPowerOffService(server.getServerUrl(), cardTask.getCardId());
                 }
                 if (Boolean.FALSE.equals(flag)) {
                     return;
                 }
                 //下电状态查询任务
                 CardTaskSub cardTasksub = cardTaskSubService.addCardSubTaskService(cardTask.getId(), cardTask.getServerSn(), CardTaskSubType.POWER_OFF_QUERY, cardTask.getTimeout());
                 log.info("下电状态查询任务 id= {}", cardTasksub.getId());
                 //下电任务执行成功
                 cardTaskSubService.updateCardTaskSubService(cardSubTask.getId(), CardTaskStatus.SUCCESS);
             });
 
         });
         return true;
     }
 
     @Override
     public Boolean powerOffResultAndPowerOnTaskService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.POWER_OFF_QUERY).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
             cardSubTasks.forEach(cardSubTask -> {
                 CardTask cardTask = cardTaskService.getCardTaskById(cardSubTask.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask)) {
                     return;
                 }
                 CardTaskSub updateSub = cardTaskSubService.getCardSubTaskById(cardSubTask.getId());
                 if (ObjectUtils.isEmpty(updateSub)) {
                     return;
                 }
 
                 boolean powerOffFlag = false;
                 if (LINGDIAN.equals(server.getServerType())) {
                     ArmCardStatusVO cardStatusVO = linDianApiService.getCardStatusService(server.getServerUrl(), cardTask.getCardId());
                     if (!ObjectUtils.isEmpty(cardStatusVO) && RUNNING.equals(cardStatusVO.getStatus())) {
                         linDianApiService.armCardPowerOffService(server.getServerUrl(), cardTask.getCardId());
                         return;
                     }
                     if(Objects.nonNull(cardStatusVO)){
                         powerOffFlag = POWER_OFF.equals(cardStatusVO.getStatus());
                     }
                 }
                 if (!powerOffFlag) {
                     return;
                 }
 
                 //上电执行任务
                 CardTaskSub cardTasksub = cardTaskSubService.addCardSubTaskService(cardTask.getId(), cardTask.getServerSn(), CardTaskSubType.POWER_ON, cardTask.getTimeout());
                 log.info("上电执行任务 id= {}", cardTasksub.getId());
                 //下电成功
                 cardTaskSubService.updateCardTaskSubService(cardSubTask.getId(), CardTaskStatus.SUCCESS);
             });
         });
         return true;
     }
 
 
     @Override
     public Boolean powerOnAndAddResultTaskService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.POWER_ON).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
             cardSubTasks.forEach(cardSubTask -> {
                 CardTask cardTask = cardTaskService.getCardTaskById(cardSubTask.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask)) {
                     return;
                 }
                 CardTaskSub updateSub = cardTaskSubService.getCardSubTaskById(cardSubTask.getId());
                 if (ObjectUtils.isEmpty(updateSub)) {
                     return;
                 }
 
                 Boolean flag = false;
                 if (LINGDIAN.equals(server.getServerType())) {
                     flag = linDianApiService.armCardPowerOnService(server.getServerUrl(), cardTask.getCardId());
                 }
                 if (!flag) {
                     return;
                 }
 
                 //上电结果查询任务
                 CardTaskSub cardTasksub = cardTaskSubService.addCardSubTaskService(cardTask.getId(), cardTask.getServerSn(), CardTaskSubType.POWER_ON_QUERY, cardTask.getTimeout());
                 log.info("上电结果查询任务 id= {}", cardTasksub.getId());
                 //上电执行成功
                 cardTaskSubService.updateCardTaskSubService(cardSubTask.getId(), CardTaskStatus.SUCCESS);
             });
         });
         return true;
 
     }
 
     @Override
     public Boolean powerOnResultAndCallbackTaskService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.POWER_ON_QUERY).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
             cardSubTasks.forEach(cardSubTask -> {
                 CardTask cardTask = cardTaskService.getCardTaskById(cardSubTask.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask)) {
                     return;
                 }
                 CardTaskSub updateSub = cardTaskSubService.getCardSubTaskById(cardSubTask.getId());
                 if (ObjectUtils.isEmpty(updateSub)) {
                     return;
                 }
                 ArmCardStatusVO cardStatusVO = null;
                 if (LINGDIAN.equals(server.getServerType())) {
                     cardStatusVO = linDianApiService.getCardStatusService(server.getServerUrl(), cardTask.getCardId());
                     if (ObjectUtils.isEmpty(cardStatusVO)) {
                         return;
                     }
                     if (POWER_OFF.equals(cardStatusVO.getStatus())) {
                         linDianApiService.armCardPowerOnService(server.getServerUrl(), cardTask.getCardId());
                         return;
                     }
                 }
 
                 if (ObjectUtils.isEmpty(cardStatusVO)) {
                     return;
                 }
 
                 if (!RUNNING.equals(cardStatusVO.getStatus())) {
                     return;
                 }
 
                 //回调
                 Boolean syncFlag = false;
                 if (cardTask.getType().equals(CardTaskType.SET_NETWORK)) {
                     //回调云机信息
                     Card card = cardService.getCardByCardId(cardTask.getCardId());
                     if (!ObjectUtils.isEmpty(card)) {
                         syncFlag = cardTaskCallbackService.sendCardStatusCallback(DEVICE_STATUS_CALLBACK, card.getCardId(), card.getIp(), ONE);
                     }
                     if (!ObjectUtils.isEmpty(cardTask.getTaskId())) {
                         //更新板卡网络信息
                         CardNetworkDTO cardNetworkDTO = JSON.parseObject(cardTask.getTaskContent(), CardNetworkDTO.class);
                         Card cardUpdate = new Card();
                         cardUpdate.setId(card.getId());
                         cardUpdate.setIp(cardNetworkDTO.getIp());
                         cardUpdate.setNetmask(cardNetworkDTO.getNetmask());
                         cardUpdate.setGateway(cardNetworkDTO.getGateway());
                         cardUpdate.setDns(cardNetworkDTO.getDns());
                         cardService.updateById(cardUpdate);
                         String key = KeyPrefix.BMC_CARD_LIST + card.getCardId();
                         bmcRedisService.deleteObject(key);
                         syncFlag = cardTaskCallbackService.sendCardTaskCallback(DEVICE_TASK_CALLBACK, cardTask.getId().toString(), cardTask.getType(), CardTaskStatus.SUCCESS, REQUEST_SUCCESS);
                     }
                 } else {
                     syncFlag = cardTaskCallbackService.sendCardTaskCallback(DEVICE_TASK_CALLBACK, cardTask.getId().toString(), cardTask.getType(), CardTaskStatus.SUCCESS, REQUEST_SUCCESS);
                     if (CardTaskType.POWER_RESTART.equals(cardTask.getType())) {
                         String key = CARD_POWER_RESTART + cardTask.getCardId();
                         bmcRedisService.deleteObject(key);
                     }
                 }
                 //主任务执行成功
                 cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.SUCCESS, syncFlag);
                 //上电成功
                 cardTaskSubService.updateCardTaskSubService(cardSubTask.getId(), CardTaskStatus.SUCCESS);
             });
         });
         return true;
     }
 
     @Override
     public Boolean setCardNetworkTaskService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
 
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.SET_NETWORK_IP).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
             cardSubTasks.forEach(cardTasksub -> {
                 CardTask cardTask = cardTaskService.getById(cardTasksub.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask.getTaskContent())) {
                     return;
                 }
                 log.info("进入方法中1111111111:{}",JSON.toJSONString(cardTask));
                 CardNetworkDTO networkDTO = JSON.parseObject(cardTask.getTaskContent(), CardNetworkDTO.class);
 
                 if (LINGDIAN.equals(server.getServerType())) {
                     ArmCardStatusVO cardStatusVO = linDianApiService.getCardStatusService(server.getServerUrl(), cardTask.getCardId());
                     log.info("进入方法中networkDTO:{},serverUrl:{},cardId:{}",JSON.toJSONString(networkDTO),server.getServerUrl(),cardTask.getCardId());
                     if (ObjectUtils.isEmpty(cardStatusVO) || !RUNNING.equals(cardStatusVO.getStatus())) {
                         return;
                     }
                     Boolean ip_flag = linDianApiService.armCardIpConfigService(server.getServerUrl(), cardTask.getCardId(), networkDTO.getIp(), networkDTO.getNetmask(), networkDTO.getGateway(), networkDTO.getDns());
                     if (!ip_flag) {
                         return;
                     }
                     //添加节点下电执行任务 使设置生效
                     addCardPowerOffService(cardTasksub.getMainTask(), cardTask.getServerSn(), cardTask.getCardId(), cardTask.getTimeout());
                     //修改设置网络子任务状态
                     cardTaskService.updateCardTaskSubService(cardTasksub.getId(), CardTaskStatus.SUCCESS);
                 }
             });
         });
         return true;
     }
 
 
     @Override
     public Boolean addCardPowerOffService(Long mainTaskId, String sn, String armCardId, LocalDateTime timeOut) {
         CardTaskSub cardTaskSub = cardTaskSubService.addCardSubTaskService(mainTaskId, sn, CardTaskSubType.POWER_OFF, timeOut);
         return true;
     }
 
     @Override
     public List<TaskVO> addCardResetService(ResetCardDTO parma) {
         List<TaskVO> taskVOS = cardTaskService.addCardResetTaskService(parma.getCardTasks(), parma.getTimeOut());
         if (CollectionUtils.isEmpty(taskVOS)) {
             throw new BasicException(ManageExceptionCode.FAILED_TO_ADD_NODE_RESET_TASK);
         }
         return taskVOS;
     }
 
     @Override
     public Boolean cardResetAndAddResultTaskService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
 
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.CARD_RESET).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
             cardSubTasks.forEach(cardTasksub -> {
                 CardTask cardTask = cardTaskService.getCardTaskById(cardTasksub.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask)) {
                     return;
                 }
 
                 Boolean flag = false;
                 if (LINGDIAN.equals(server.getServerType())) {
                     flag = linDianApiService.reInitCardService(server.getServerUrl(), cardTask.getCardId());
                 }
 
                 if (!flag) {
                     return;
                 }
                 //新建节点重置结果查询任务
                 CardTaskSub cardTaskSub = cardTaskSubService.addCardResetResultTaskService(cardTask);
                 if (ObjectUtils.isEmpty(cardTaskSub)) {
                     return;
                 }
                 cardTaskService.updateCardTaskSubService(cardTasksub.getId(), CardTaskStatus.SUCCESS);
             });
 
         });
         return true;
     }
 
     @Override
     public Boolean getCardResetResultService(List<String> serverSnList, Integer limitTaskSize) {
         serverSnList.parallelStream().forEach(serverSn -> {
 
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", serverSn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询等待执行的任务
             QueryWrapper<CardTaskSub> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskSubType.CARD_RESET_RESULT).eq("status", ONE).eq("server_sn", serverSn);
             exQw.orderByAsc("id").last("limit " + limitTaskSize);
             List<CardTaskSub> cardSubTasks = cardTaskSubService.list(exQw);
             cardSubTasks.forEach(cardTasksub -> {
 
                 CardTask cardTask = cardTaskService.getCardTaskById(cardTasksub.getMainTask());
                 if (ObjectUtils.isEmpty(cardTask)) {
                     return;
                 }
 
                 if (LINGDIAN.equals(server.getServerType())) {
                     ArmCardNetworkVO networkVO = linDianApiService.getArmCardNetworkById(server.getServerUrl(), cardTask.getCardId());
                     if (ObjectUtils.isEmpty(networkVO)) {
                         return;
                     }
                     if (!"0.0.0.0".equals(networkVO.getIp())) {
                         return;
                     }
                 }
 
                 //节点重置成功回调
                 Boolean flag = cardTaskCallbackService.sendCardTaskCallback(DEVICE_TASK_CALLBACK, cardTask.getTaskId(), cardTask.getType(), CardTaskStatus.SUCCESS, REQUEST_SUCCESS);
                 cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.SUCCESS, flag);
                 cardTaskService.updateCardTaskSubService(cardTasksub.getId(), CardTaskStatus.SUCCESS);
             });
 
         });
 
         return true;
     }
 
     @Override
     public Boolean executeReCardInitService(List<String> serverSnList, Integer executeSize) {
         serverSnList.forEach(sn -> {
             AtomicReference<Integer> executeCardTaskSize = new AtomicReference<>(executeSize);
 
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", sn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询正在执行的任务
             QueryWrapper<CardTask> exQw = new QueryWrapper<>();
             exQw.eq("type", CardTaskType.REINIT).eq("status", ONE).eq("server_sn", sn);
             exQw.isNotNull("job_id").orderByDesc("update_time").last("limit " + executeCardTaskSize);
             List<CardTask> executeCardTasks = cardTaskService.list(exQw);
 
             executeCardTasks.forEach(cardTask -> {
                 String taskResult = linDianApiService.getJobInfoService(server.getServerUrl(), cardTask.getJobId());
                 if (taskResult.equals(REQUEST_SUCCESS)) {
                     cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.SUCCESS, false);
                     CardTaskSub cardTaskSub = cardTaskSubService.getOne(new QueryWrapper<CardTaskSub>().eq("main_task", cardTask.getId()).last("limit 1"));
                     cardTaskSubService.updateCardTaskSubService(cardTaskSub.getId(), CardTaskStatus.SUCCESS);
                 } else if (taskResult.equals(REQUEST_FAIL)) {
                     cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.FAIL, false);
                     CardTaskSub cardTaskSub = cardTaskSubService.getOne(new QueryWrapper<CardTaskSub>().eq("main_task", cardTask.getId()).last("limit 1"));
                     cardTaskSubService.updateCardTaskSubService(cardTaskSub.getId(), CardTaskStatus.FAIL);
                 } else {
                     executeCardTaskSize.set(executeCardTaskSize.get() - ONE);
                 }
             });
 
             if (executeCardTaskSize.get() <= ZERO) {
                 return;
             }
 
             QueryWrapper<CardTask> qw = new QueryWrapper<>();
             qw.eq("type", CardTaskType.REINIT).eq("status", ONE).eq("server_sn", sn);
             qw.isNull("job_id").orderByAsc("id").last("limit " + executeCardTaskSize.get());
             List<CardTask> cardTasks = cardTaskService.list(qw);
 
             cardTasks.forEach(cardTask -> {
                 JobVO jobVO = linDianApiService.reInitCardJobService(server.getServerUrl(), cardTask.getCardId());
                 CardTask updateCardTask = new CardTask();
                 updateCardTask.setId(cardTask.getId());
                 updateCardTask.setJobId(jobVO.getJobid());
                 updateCardTask.setUpdateTime(new Date());
                 cardTaskService.updateById(updateCardTask);
             });
 
         });
         return true;
     }
 
     @Override
     public Boolean executeReinstallService(List<String> serverSnList, Integer executeSize) {
         serverSnList.forEach(sn -> {
             AtomicReference<Integer> executeCardTaskSize = new AtomicReference<>(executeSize);
 
             Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("sn", sn).last("limit 1"));
             if (ObjectUtils.isEmpty(server)) {
                 return;
             }
             //查询正在执行的任务
             QueryWrapper<CardTask> exQw = new QueryWrapper<>();
             exQw.in("type", CardTaskType.REINSTALL, CardTaskType.FLASH_BOOT).eq("status", ONE).eq("server_sn", sn);
             exQw.isNotNull("job_id").orderByDesc("update_time").last("limit " + executeCardTaskSize);
             List<CardTask> executeCardTasks = cardTaskService.list(exQw);
 
             executeCardTasks.forEach(cardTask -> {
                 String taskResult = linDianApiService.getJobInfoService(server.getServerUrl(), cardTask.getJobId());
                 if (taskResult.equals(REQUEST_SUCCESS)) {
                     Boolean flag = cardTaskCallbackService.sendCardTaskCallback(DEVICE_TASK_CALLBACK, cardTask.getTaskId(), cardTask.getType(), CardTaskStatus.SUCCESS, REQUEST_SUCCESS);
                     cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.SUCCESS, flag);
                     CardTaskSub cardTaskSub = cardTaskSubService.getOne(new QueryWrapper<CardTaskSub>().eq("main_task", cardTask.getId()).last("limit 1"));
                     cardTaskSubService.updateCardTaskSubService(cardTaskSub.getId(), CardTaskStatus.SUCCESS);
                     //板卡刷机成功，创建设置网络任务
                     if (CardTaskType.REINSTALL.equals(cardTask.getType())) {
                         addRestoreNetworkTaskService(cardTask.getCardId());
                     }
 
                 } else if (taskResult.equals(REQUEST_FAIL)) {
                     cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.FAIL, false);
                     CardTaskSub cardTaskSub = cardTaskSubService.getOne(new QueryWrapper<CardTaskSub>().eq("main_task", cardTask.getId()).last("limit 1"));
                     cardTaskSubService.updateCardTaskSubService(cardTaskSub.getId(), CardTaskStatus.FAIL);
                 } else {
                     executeCardTaskSize.set(executeCardTaskSize.get() - ONE);
                 }
             });
 
             if (executeCardTaskSize.get() <= ZERO) {
                 return;
             }
 
             QueryWrapper<CardTask> qw = new QueryWrapper<>();
             qw.in("type", CardTaskType.REINSTALL, CardTaskType.FLASH_BOOT).eq("status", ONE).eq("server_sn", sn);
             qw.isNull("job_id").orderByAsc("id").last("limit " + executeCardTaskSize.get());
             List<CardTask> cardTasks = cardTaskService.list(qw);
 
             //镜像是否上传完成
             Boolean isExist = checkImageUploadedService(cardTasks, server);
             if (!isExist) {
                 return;
             }
             cardTasks.forEach(cardTask -> {
                 JobVO jobVO = linDianApiService.reinstallJobService(server.getServerUrl(), cardTask.getCardId(), cardTask.getTaskContent());
                 CardTask updateCardTask = new CardTask();
                 updateCardTask.setId(cardTask.getId());
                 updateCardTask.setJobId(jobVO.getJobid());
                 updateCardTask.setUpdateTime(new Date());
                 cardTaskService.updateById(updateCardTask);
             });
 
         });
         return true;
     }
 
     /**
      * 校验镜像是否存在
      *
      * @param executeCardTasks
      * @param server
      * @return
      */
     private Boolean checkImageUploadedService(List<CardTask> executeCardTasks, Server server) {
         if (CollectionUtils.isEmpty(executeCardTasks)) {
             return false;
         }
         ImageDTO imageDTO = linDianApiService.getImagesByRoomNameService(server.getServerUrl(), executeCardTasks.get(0).getTaskContent());
         if (ObjectUtils.isEmpty(imageDTO)) {
             log.error("CardManageServiceImpl.checkImageUploadedService result imageDTO is empty. serverUlr:{}, taskContent:{}",server.getServerUrl(),executeCardTasks.get(0).getTaskContent());
             executeCardTasks.forEach(cardTask -> {
                 Date createTime = cardTask.getCreateTime();
                 log.debug("CardManageServiceImpl.checkImageUploadedService run executeCardTasks. cardTask:{} ", JSONObject.toJSON(cardTask));
                 if(Objects.nonNull(createTime)){
                     LocalDateTime localDate = createTime.toInstant()
                             .atZone(ZoneId.systemDefault())
                             .toLocalDateTime();
                     LocalDateTime plusHours = localDate.plusHours(2);
                     //2小时内的任务,不做任何处理,方便下次定时task能再将它扫出来
                     if(plusHours.isAfter(LocalDateTime.now())){
                         return ;
                     }
                 }
 
                 cardTaskService.updateCardTaskService(cardTask.getId(), CardTaskStatus.FAIL, false);
                 CardTaskSub cardTaskSub = cardTaskSubService.getOne(new QueryWrapper<CardTaskSub>().eq("main_task", cardTask.getId()).last("limit 1"));
                 cardTaskSubService.updateCardTaskSubService(cardTaskSub.getId(), CardTaskStatus.FAIL);
             });
             return false;
         }
         return true;
     }
 
     /**
      * 刷机完成-恢复板卡网络
      *
      * @return
      */
     private Boolean addRestoreNetworkTaskService(String cardId) {
         Card card = cardService.getCardByCardId(cardId);
         if (ObjectUtils.isEmpty(card)) {
             return false;
         }
         List<CardNetworkDTO> cardNetworkInfos = new ArrayList<>();
         CardNetworkDTO cardNetworkDTO = new CardNetworkDTO();
         cardNetworkDTO.setCardId(card.getCardId());
         cardNetworkDTO.setIp(card.getIp());
         cardNetworkDTO.setNetmask(card.getNetmask());
         cardNetworkDTO.setGateway(card.getGateway());
         cardNetworkDTO.setDns(card.getDns());
         cardNetworkDTO.setServerSn(card.getServerSn());
         cardNetworkDTO.setTimeOut(TEN);
 
         cardNetworkInfos.add(cardNetworkDTO);
         cardTaskService.addSetCardNetWorkTaskService(cardNetworkInfos);
         return true;
     }
 
     @Override
     public List<ArmCardNetworkVO> getCardNetworkInfo(CardNetworkInfoDTO parma) {
         Server server = bmcServerMapper.selectOne(new QueryWrapper<Server>().eq("server_url", parma.getSocApiUrl()).last("limit 1"));
         if (ObjectUtils.isEmpty(server)) {
             throw new BasicException(ManageExceptionCode.ARM_SERVER_NOT_EXIST);
         }
         List<ArmCardNetworkVO> armCardNetworkVOS = new ArrayList<>();
         if (LINGDIAN.equals(server.getServerType())) {
             if (ObjectUtils.isEmpty(parma.getCardId())) {
                 List<LdCardNetworkVO> ldCardNetworkVOS = linDianApiService.getArmCardNetworkService(server.getServerUrl());
                 ldCardNetworkVOS.forEach(ldCardNetworkVO -> {
                     armCardNetworkVOS.add(ldCardNetworkVO.getEth0());
                 });
                 return armCardNetworkVOS;
             } else {
                 ArmCardNetworkVO armCardNetworkVO = linDianApiService.getArmCardNetworkById(server.getServerUrl(), parma.getCardId());
                 return Lists.newArrayList(armCardNetworkVO);
             }
 
         }
         return null;
     }
 
     @Override
     public List<ArmCardStatusVO> getCardStatusInfo(CardNetworkInfoDTO parma) {
         return linDianApiService.getCardsStatusService(parma.getSocApiUrl());
     }
 }