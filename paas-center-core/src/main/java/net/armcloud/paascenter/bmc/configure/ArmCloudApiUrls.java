 package net.armcloud.paascenter.bmc.configure;
 
 public interface ArmCloudApiUrls {
     /**
      * callback device info
      */
     String DEVICE_STATUS_CALLBACK = "/openapi/internal/device/callbackUpdateDevice";
     /**
      * callback device task status
      */
     String DEVICE_TASK_CALLBACK = "/openapi/internal/device/callbackDeviceTask";
     /**
      * callback arm server status
      */
     String SERVER_STATUS_CALLBACK = "/openapi/internal/device/callbackArmServerStatus";
 }