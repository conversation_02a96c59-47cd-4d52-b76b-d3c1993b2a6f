 package net.armcloud.paascenter.bmc.model.vo;
 
 import lombok.Data;
 
 import java.io.Serializable;
 
 /**
  * armcloud paas响应信息主体
  *
  * @param <T>
  */
 @Data
 public class ResultArmCloudPaasVO<T> implements Serializable {
     /**
      * 状态码
      */
     private int code;
     /**
      * 状态信息
      */
     private String msg;
     /**
      * 时间戳
      */
     private Long ts;
     /**
      * 数据
      */
     private T data;
 }