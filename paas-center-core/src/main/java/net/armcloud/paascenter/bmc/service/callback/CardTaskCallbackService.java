 package net.armcloud.paascenter.bmc.service.callback;
 
 
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.fastjson2.TypeReference;
 import net.armcloud.paascenter.bmc.utils.HttpClientUtils;
 import net.armcloud.paascenter.common.lingdian.model.vo.ResultLinDianVO;
 import net.armcloud.paascenter.bmc.configure.ArmCloudConfig;
 import net.armcloud.paascenter.bmc.model.dto.ArmServerCallbackDTO;
 import net.armcloud.paascenter.bmc.model.dto.CardStatusCallbackDTO;
 import net.armcloud.paascenter.bmc.model.dto.CardTaskCallbackDTO;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 
 import javax.annotation.Resource;
 
 import static net.armcloud.paascenter.common.lingdian.constant.LingDianConstants.LOGIN_SUCCESS_CODE;
 
 @Slf4j
 @Service
 public class CardTaskCallbackService {
     @Resource
     private ArmCloudConfig armCloudConfig;
 
     /**
      * 推送云机状态
      *
      * @param callbackUrl
      * @param deviceIp
      * @param deviceOutCode
      * @return
      */
     public Boolean sendCardStatusCallback(String callbackUrl, String deviceOutCode, String deviceIp, Integer initStatus) {
         try {
             CardStatusCallbackDTO requestBody = new CardStatusCallbackDTO();
             requestBody.setDeviceIp(deviceIp);
             requestBody.setDeviceOutCode(deviceOutCode);
             requestBody.setInitStatus(initStatus);
             String url = armCloudConfig.getApiUrl() + callbackUrl;
             log.info("sendCardStatusCallback url={},requestBody={}", url, requestBody);
 
             String result = HttpClientUtils.doPost(url, requestBody, null);
             if (null != result) {
                 log.info("sendCardStatusCallback result={}", result);
                 ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                 });
 
                 return LOGIN_SUCCESS_CODE == resultLinDianVO.getCode();
             }
         } catch (Exception e) {
             log.error(" Requesting armcloud paas Error method={}", "sendCardStatusCallback", e);
         }
         return false;
     }
 
     /**
      * 推送任务状态
      *
      * @param callbackUrl 请求地址
      * @param taskId      任务ID
      * @param status      任务状态
      * @param resultMsg   状态信息
      * @return Boolean
      */
     public Boolean sendCardTaskCallback(String callbackUrl, String taskId, Integer type, Integer status, String resultMsg) {
         try {
             CardTaskCallbackDTO requestBody = new CardTaskCallbackDTO();
             requestBody.setTaskId(taskId);
             requestBody.setStatus(status);
             requestBody.setType(type);
             requestBody.setResultMsg(resultMsg);
             String url = armCloudConfig.getApiUrl() + callbackUrl;
             log.info("sendCardTaskCallback url={},requestBody={}", url, requestBody);
 
             String result = HttpClientUtils.doPost(url, requestBody, null);
             if (null != result) {
                 log.info("sendCardTaskCallback result={}", result);
                 ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                 });
                 return LOGIN_SUCCESS_CODE == resultLinDianVO.getCode();
             }
         } catch (Exception e) {
             log.error(" Requesting armcloud paas Error method={}", "sendCardTaskCallback", e);
         }
         return false;
     }
 
     /**
      * 推送arm server状态
      *
      * @param callbackUrl 回调地址
      * @param sn          arm server sn
      * @param status      arm server status
      * @param resultMsg   状态信息
      * @return Boolean
      */
     public Boolean sendServerStatusCallback(String callbackUrl, String sn, Integer status, String resultMsg) {
         try {
             ArmServerCallbackDTO requestBody = new ArmServerCallbackDTO();
             requestBody.setServerSn(sn);
             requestBody.setStatus(status);
             requestBody.setMsg(resultMsg);
 //            String result = HttpClientUtils.doPost(armCloudConfig.getApiUrl() + callbackUrl, requestBody, null);
 //            if (null != result) {
 //                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
 //                });
 //
 //                return LOGIN_SUCCESS_CODE == resultLinDianVO.getCode();
 //            }
         } catch (Exception e) {
             log.error(" Requesting armcloud paas Error method={}", "sendServerStatusCallback", e);
         }
         return false;
     }
 
 }