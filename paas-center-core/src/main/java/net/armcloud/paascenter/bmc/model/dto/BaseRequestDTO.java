 package net.armcloud.paascenter.bmc.model.dto;
 
 import lombok.Data;
 
 import javax.validation.constraints.NotNull;
 import java.io.Serializable;
 
 @Data
 public class BaseRequestDTO implements Serializable {
     /**
      * soc api url
      */
     @NotNull(message = "socApiUrl cannot null")
     private String socApiUrl;
     /**
      * 服务器序列号
      */
     private String serverSn;
     /**
      * 任务超时时间 分钟
      */
     private Integer timeOut;
 
 }