package net.armcloud.paascenter.filecenter.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * File detail view object for API responses
 */
@Data
public class FileDetailVO {

    /**
     * 文件ID
     */
    private Long id;

    /**
     * 文件存储ID
     */
    private Long fileStorageId;

    /**
     * 文件唯一ID
     */
    private String fileUniqueId;
    
    /**
     * Customer ID
     */
    private Long customerId;

    /**
     * Customer name
     */
    private String customerName;

    /**
     * Customer account
     */
    private String customerAccount;

    /**
     * Original file name
     */
    private String fileName;
    
    /**
     * File type
     */
    private String fileType;
    
    /**
     * File size in bytes
     */
    private Long fileSize;

    /**
     * File comment
     */
    private String fileComment;
    
    /**
     * File MD5 hash
     */
    private String fileMd5;
    
    /**
     * Public URL for downloading
     */
    private String publicUrl;
    
    
    /**
     * Creation time
     */
    private Date createdTime;
    
    /**
     * Source type
     */
    private String sourceType;

    /**
     * Original URL
     */
    private String originalUrl;
} 