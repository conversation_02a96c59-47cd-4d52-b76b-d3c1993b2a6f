package net.armcloud.paascenter.filecenter.model.dto;

import lombok.Data;

/**
 * File upload response DTO
 */
@Data
public class FileUploadResponseDTO {
    
    /**
     * File storage ID
     */
    private Long fileStorageId;
    
    /**
     * Whether the file already exists (for fast upload)
     */
    private Boolean fileExists;
    
    /**
     * OSS upload URL (null if file exists)
     */
    private String uploadUrl;

    /**
     * Public URL
     */
    private String publicUrl;

    /**
     * OSS callback URL
     */
    private String callbackUrl;
    
    /**
     * OSS bucket name
     */
    private String bucketName;

    /**
     * OSS region
     */
    private String region;

    /**
     * OSS endpoint
     */
    private String endpoint;
    
    /**
     * OSS access key ID (null if file exists)
     */
    private String accessKeyId;
    
    /**
     * OSS access key secret (null if file exists)
     */
    private String accessKeySecret;
    
    /**
     * OSS session token (null if file exists)
     */
    private String securityToken;
    
    /**
     * Token expiration time in seconds (null if file exists)
     */
    private Long expiration;
} 