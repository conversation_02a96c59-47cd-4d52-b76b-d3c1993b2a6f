package net.armcloud.paascenter.filecenter.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * File upload request DTO
 */
@Data
public class FileUploadRequestDTO {
    
    /**
     * File MD5 hash
     */
    @NotBlank(message = "File MD5 cannot be empty")
    private String fileMd5;
    
    /**
     * File name
     */
    @NotBlank(message = "File name cannot be empty")
    private String fileName;

    /**
     * oss 指定文件夹
     */
    private String directory;
    
} 