package net.armcloud.paascenter.filecenter.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import net.armcloud.paascenter.common.client.internal.vo.CustomerAppFileVO;
import net.armcloud.paascenter.common.client.internal.vo.CustomerFileVO;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.filecenter.model.dto.AppFileListDTO;
import net.armcloud.paascenter.filecenter.model.dto.QueryFileListRequestDTO;
import net.armcloud.paascenter.filecenter.model.vo.AppDetailsVO;
import net.armcloud.paascenter.filecenter.model.vo.FileDetailVO;

/**
 * Mapper for fc_user_files table
 */
@Mapper
public interface UserFileMapper extends BaseMapper<UserFile> {

        /**
         * Query file details
         */
        List<FileDetailVO> queryFileDetails(QueryFileListRequestDTO param);

        /**
         * Query app files
         */
        List<AppDetailsVO> queryAppFiles(QueryFileListRequestDTO param);


        List<AppDetailsVO> queryAppDetails(AppFileListDTO param);

        /**
         * Query app file by file unique id
         */
        AppDetailsVO queryAppDetailByAppId(
                        @Param("appId") String appId, 
                        @Param("customerId") Long customerId);


        CustomerAppFileVO queryCustomerAppFile(@Param("customerId") Long customerId, @Param("appId") String appId, @Param("appName") String appName, @Param("pkgName") String pkgName);

        /**
         * Query customer app file by user file id
         */
        CustomerAppFileVO queryCustomerExistingApp(@Param("userFileId") String userFileId);

        CustomerFileVO queryCustomerFile(@Param("customerId") Long customerId, @Param("fileMd5") String fileMd5, @Param("fileUniqueId") String fileUniqueId);
}