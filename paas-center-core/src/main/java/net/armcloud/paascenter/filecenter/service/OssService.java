package net.armcloud.paascenter.filecenter.service;

import java.io.InputStream;
import java.util.Map;

/**
 * OSS service interface for file operations
 */
public interface OssService {
    
    /**
     * Generate a pre-signed upload URL for direct client upload
     *
     * @param objectKey OSS object key
     * @param contentType Content type of the file
     * @param expiration Expiration time in seconds
     * @return Pre-signed URL and temporary credentials
     */
    Map<String, String> generatePresignedUploadUrl(String objectKey, String contentType, Long expiration);
    
    /**
     * Get file public access URL
     *
     * @param objectKey OSS object key
     * @return Public access URL
     */
    String getPublicUrl(String objectKey);

    /**
     * Get file private access URL
     *
     * @param objectKey OSS object key
     * @return Private access URL
     */
    String getPrivateUrl(String objectKey);
    
    /**
     * Generate a storage path based on file type
     *
     * @param fileType File type
     * @param fileName Original file name
     * @param fileMd5 File MD5 hash
     * @return OSS object key
     */
    String generateStoragePath(String fileType, String fileName, String fileMd5);
    
    /**
     * Generate an APP icon storage path
     *
     * @param packageName APP package name
     * @param versionCode APP version code
     * @return OSS object key for app icon
     */
    String getAppIconPath(String packageName, Integer versionCode, String iconMd5);
    
    /**
     * Upload a file to OSS
     *
     * @param objectKey OSS object key
     * @param inputStream File input stream
     * @param contentType Content type of the file
     * @param contentLength Content length in bytes
     * @return Public access URL if successful, null otherwise
     */
    String uploadFile(String objectKey, InputStream inputStream, String contentType, long contentLength);
    
    /**
     * Upload a Base64 encoded image
     *
     * @param objectKey OSS object key
     * @param base64Image Base64 encoded image without header
     * @param contentType Content type of the image
     * @return Public access URL if successful, null otherwise
     */
    String uploadBase64Image(String objectKey, String base64Image, String contentType);
    
    /**
     * Delete a file from OSS
     *
     * @param objectKey OSS object key
     * @return true if deleted successfully, false otherwise
     */
    boolean deleteFile(String objectKey);
    
    /**
     * Check if file exists in OSS by object key
     *
     * @param objectKey OSS object key
     * @return true if file exists, false otherwise
     */
    boolean checkPathExists(String objectKey);
    
    /**
     * Get file input stream from OSS by object key or URL
     *
     * @param objectKeyOrUrl OSS object key or full URL
     * @return InputStream of the file, caller must close the stream
     */
    InputStream getFileInputStream(String objectKeyOrUrl);

    /**
     * Generate a storage path based on Specify a Directory
     *
     * @param directory
     * @param fileName Original file name
     * @param fileMd5 File MD5 hash
     * @return OSS object key
     */
    String generateStoragePathByDirectory(String directory, String fileName, String fileMd5);
}