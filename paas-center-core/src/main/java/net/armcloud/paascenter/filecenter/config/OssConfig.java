package net.armcloud.paascenter.filecenter.config;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;


/**
 * Aliyun OSS configuration properties
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {
    
    /**
     * OSS endpoint
     */
    private String endpoint = "https://oss-cn-hongkong.aliyuncs.com";
    
    /**
     * OSS access key
     */
    private String accessKey = "LTAI5t6ZtSJzCmuFtCNcRDk7";
    
    /**
     * OSS secret key
     */
    private String secretKey = "******************************";
    
    /**
     * OSS region
     */
    private String region = "cn-hongkong";
    
    /**
     * OSS role ARN
     */
    private String roleArn = "acs:ram::1191921788579688:role/armcloud-paas-oss";

    /**
     * OSS bucket name
     */
    private String bucketName = "armcloud-hk";
    
    /**
     * Base URL for public access
     */
    private String baseUrl = "https://oss-hk.armcloud.net";

    /**
     * OSS private URL
     */
    private String privateUrl = "https://armcloud-hk.oss-cn-hongkong-internal.aliyuncs.com";

    /**
     * OSS callback URL
     */
    private String callbackUrl = "http://openapi-test.armcloud.net:18010/openapi/filecenter/aliyunoss/callback";
    
    /**
     * Whether to enable SSL (https)
     */
    private Boolean enableSsl = true;
    
    /**
     * Path patterns for different file types
     */
    private String appPath = "app/";
    private String imagePath = "images/";
    private String isoPath = "iso/";
    private String filePath = "files/";
    
    /**
     * App icon storage path
     */
    private String appIconPath = "app-icons/";
    
    /**
     * Preview image storage path
     */
    private String previewsPath = "previews/";
    
    /**
     * Temporary file expiration time in seconds (default: 3600s = 1 hour)
     */
    private Long temporaryUrlExpiration = 3600L;

    @PostConstruct
    public void init() {
        log.info("OSS Configuration loaded: endpoint={}, callbackUrl={}, bucketName={}", 
                endpoint, callbackUrl, bucketName);
    }
}