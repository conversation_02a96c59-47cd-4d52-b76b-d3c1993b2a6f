package net.armcloud.paascenter.filecenter.service.impl;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.filecenter.config.OssConfig;
import net.armcloud.paascenter.filecenter.enums.FileTypeEnum;
import net.armcloud.paascenter.filecenter.service.OssService;

/**
 * OSS service implementation for Aliyun OSS
 */
@Slf4j
@Service
public class OssServiceImpl implements OssService {

    @Autowired
    private OssConfig ossConfig;

    private OSS ossClient;

    @Value("${spring.profiles.active:unknown}")
    private String env;

    @PostConstruct
    public void init() {
        try {
            ossClient = new OSSClientBuilder().build(
                    ossConfig.getEndpoint(),
                    new DefaultCredentialProvider(
                            ossConfig.getAccessKey(),
                            ossConfig.getSecretKey()),
                    null);
            log.info("Aliyun OSS client initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Aliyun OSS client: {}", e.getMessage(), e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (ossClient != null) {
            ossClient.shutdown();
            log.info("Aliyun OSS client shutdown");
        }
    }

    @Override
    public Map<String, String> generatePresignedUploadUrl(String objectKey, String contentType, Long expiration) {
        try {
            if (expiration == null) {
                expiration = ossConfig.getTemporaryUrlExpiration();
            }
            if (expiration > 43200L) {
                expiration = 43200L;
            }

            // 首先获取 STS 临时凭据
            DefaultAcsClient stsClient = createStsClient();
            AssumeRoleRequest roleRequest = new AssumeRoleRequest();
            roleRequest.setRoleArn(ossConfig.getRoleArn());
            roleRequest.setRoleSessionName("upload-session");
            roleRequest.setDurationSeconds(expiration);

            AssumeRoleResponse roleResponse = stsClient.getAcsResponse(roleRequest);
            AssumeRoleResponse.Credentials credentials = roleResponse.getCredentials();

            // 使用临时凭据创建 OSS 客户端
            // OSS tempOssClient = new OSSClientBuilder().build(
            // ossConfig.getEndpoint(),
            // credentials.getAccessKeyId(),
            // credentials.getAccessKeySecret(),
            // credentials.getSecurityToken()
            // );

            // // 生成预签名 URL
            // GeneratePresignedUrlRequest urlRequest = new
            // GeneratePresignedUrlRequest(ossConfig.getBucketName(), objectKey);
            // urlRequest.setExpiration(expirationTime);
            // urlRequest.setContentType(contentType);

            // URL url = tempOssClient.generatePresignedUrl(urlRequest);

            // 返回包含临时凭据的结果
            Map<String, String> result = new HashMap<>();

            if (!objectKey.startsWith("/")) {
                objectKey = "/" + objectKey;
            }
            result.put("uploadUrl", objectKey);
            result.put("accessKeyId", credentials.getAccessKeyId());
            result.put("accessKeySecret", credentials.getAccessKeySecret());
            result.put("securityToken", credentials.getSecurityToken());
            result.put("bucketName", ossConfig.getBucketName());
            result.put("region", ossConfig.getRegion());
            result.put("expiration", String.valueOf(expiration));

            // tempOssClient.shutdown();

            return result;
        } catch (Exception e) {
            log.error("Failed to generate presigned upload URL: {}", e.getMessage(), e);
            return null;
        }
    }

    // 添加创建 STS 客户端的辅助方法
    private DefaultAcsClient createStsClient() {
        IClientProfile profile = DefaultProfile.getProfile(
                ossConfig.getRegion(),
                ossConfig.getAccessKey(),
                ossConfig.getSecretKey());
        return new DefaultAcsClient(profile);
    }

    public static void main(String[] args) {
        OssServiceImpl ossService = new OssServiceImpl();
        ossService.init();
        Map<String, String> result = ossService.generatePresignedUploadUrl("test.txt", "text/plain", 3600L);
        System.out.println(result);
    }

    @Override
    public String getPublicUrl(String objectKey) {
        if (!StringUtils.hasText(objectKey)) {
            return null;
        }

        String baseUrl = ossConfig.getBaseUrl();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        if(objectKey.startsWith("/")){
            objectKey = objectKey.substring(1);
        }
        return baseUrl + objectKey;
    }

    @Override
    public String getPrivateUrl(String objectKey) {
        if (!StringUtils.hasText(objectKey)) {
            return null;
        }
        String baseUrl = ossConfig.getPrivateUrl();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        if(objectKey.startsWith("/")){
            objectKey = objectKey.substring(1);
        }
        return baseUrl + objectKey;
    }

    @Override
    public String generateStoragePath(String fileType, String fileName, String fileMd5) {
        FileTypeEnum type = FileTypeEnum.getByCode(fileType);

        String basePath;
        switch (type) {
            case APP:
                basePath = ossConfig.getAppPath();
                break;
            case IMAGE:
                basePath = ossConfig.getImagePath();
                break;
            case ISO:
                basePath = ossConfig.getIsoPath();
                break;
            default:
                basePath = ossConfig.getFilePath();
                break;
        }

        String extension = "";
        if (fileName != null && fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf("."));
        }
        // 此处还需要考虑不同开发环境，不同环境生成不同的路径
        return env + "/" + basePath + fileMd5 + extension;
    }

    @Override
    public String getAppIconPath(String packageName, Integer versionCode, String iconMd5) {
        return env + "/" + ossConfig.getAppIconPath() + packageName + "_" + versionCode + "_" + iconMd5 + ".png";
    }

    @Override
    public String uploadFile(String objectKey, InputStream inputStream, String contentType, long contentLength) {
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            metadata.setContentLength(contentLength);

            ossClient.putObject(ossConfig.getBucketName(), objectKey, inputStream, metadata);

            return getPublicUrl(objectKey);
        } catch (Exception e) {
            log.error("Failed to upload file to OSS: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String uploadBase64Image(String objectKey, String base64Image, String contentType) {
        try {
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes)) {
                return uploadFile(objectKey, inputStream, contentType, imageBytes.length);
            }
        } catch (Exception e) {
            log.error("Failed to upload base64 image to OSS: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean deleteFile(String objectKey) {
        try {
            ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
            return true;
        } catch (Exception e) {
            log.error("Failed to delete file from OSS: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkPathExists(String objectKey) {
        try {
            if (!StringUtils.hasText(objectKey)) {
                return false;
            }
            return ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey);
        } catch (Exception e) {
            log.error("Failed to check if file exists: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public InputStream getFileInputStream(String objectKeyOrUrl) {
        try {
            if (!StringUtils.hasText(objectKeyOrUrl)) {
                return null;
            }
            
            // 如果是完整URL，尝试提取objectKey
            String objectKey = objectKeyOrUrl;
            String baseUrl = ossConfig.getBaseUrl();
            if (objectKeyOrUrl.startsWith(baseUrl)) {
                objectKey = objectKeyOrUrl.substring(baseUrl.length());
            }
            
            String privateUrl = ossConfig.getPrivateUrl();
            if (objectKeyOrUrl.startsWith(privateUrl)) {
                objectKey = objectKeyOrUrl.substring(privateUrl.length());
            }
            
            // 移除开头的斜杠(如果有)
            if (objectKey.startsWith("/")) {
                objectKey = objectKey.substring(1);
            }
            
            // 检查对象是否存在
            if (!ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey)) {
                log.error("File does not exist in OSS: {}", objectKey);
                return null;
            }
            
            // 获取文件对象并返回其输入流
            return ossClient.getObject(ossConfig.getBucketName(), objectKey).getObjectContent();
        } catch (Exception e) {
            log.error("Failed to get file input stream: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String generateStoragePathByDirectory(String directory, String fileName, String fileMd5) {
        String extension = "";
        if (fileName != null && fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf("."));
        }
        // 此处还需要考虑不同开发环境，不同环境生成不同的路径
        return env + "/" + directory + "/" + fileMd5 + extension;
    }

}