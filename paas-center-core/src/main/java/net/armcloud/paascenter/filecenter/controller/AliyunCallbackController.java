package net.armcloud.paascenter.filecenter.controller;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.filecenter.model.dto.FileStatusUpdateDTO;
import net.armcloud.paascenter.filecenter.service.UserFileService;
import net.armcloud.paascenter.filecenter.utils.OSSCallbackValidator;

/**
 * 文件中心API控制器
 */
@Slf4j
@RestController
@RequestMapping("/openapi/filecenter")
public class AliyunCallbackController {

    @Autowired
    private UserFileService userFileService;



    @RequestMapping("/aliyunfc/callback1")
    public Result<Object> aliyunfcCallback1(HttpServletRequest request, @RequestBody String requestBody) {
        try {
            return userFileService.aliyunfcCallback1(requestBody);
        } catch (Exception e) {
            log.error("阿里云fc 回调失败: {}", e.getMessage(), e);
            return Result.fail(500, "Invalid Callback: " + e.getMessage());
        }
    }

    @RequestMapping("/aliyunoss/callback")
    public Result<Object> ossCallback(HttpServletRequest request, @RequestBody String requestBody) {
        log.info("阿里云oss 回调内容: {}", requestBody);
        // 打印所有参数
        try {
            // 校验是阿里云的回调
            String pubKeyUrlBase64 = request.getHeader("x-oss-pub-key-url");
            String authorizationBase64 = request.getHeader("Authorization");

            if (pubKeyUrlBase64 == null || authorizationBase64 == null) {
                return Result.fail(400, "Invalid Callback: Missing headers");
            }

            // 校验回调
            boolean isValid = OSSCallbackValidator.verifyOSSCallbackRequest(request, requestBody);
            if (!isValid) {
                return Result.fail(400, "Invalid Callback: Signature verification failed");
            }
            // 回调内容长这样
            // {"bucket":""armcloud-hk"","object":""docker/files/5c323bd8ab58cf04cee9e3d189699939.exe"","etag":""183E8F7502D9E711AA2CFBB5FCFD9CAD-1"","size":"5078000","mimeType":""application/octet-stream"","height":"","width":"","format":"null","crc64":"10453544606884879603","contentMd5":"null","vpcId":"null","clientIp":""*************"","reqId":""67DBE0FFDA8A79373186A971"","operation":""CompleteMultipartUpload"","fileStorageId":"null"}
            // 解析回调内容
            JSONObject jsonObject = JSON.parseObject(requestBody);
            String operation = jsonObject.getString("operation");
            String fileStorageId = jsonObject.getString("fileStorageId");
            if (StringUtils.isNotBlank(fileStorageId)
                    && ("CompleteMultipartUpload".equals(operation) || "PutObject".equals(operation))) {
                // 更新一下文件状态
                FileStatusUpdateDTO fileStatusUpdateDTO = new FileStatusUpdateDTO();
                fileStatusUpdateDTO.setFileStorageId(Long.parseLong(fileStorageId));
                fileStatusUpdateDTO.setUploadStatus("success");
                
                // 添加空值检查，避免NullPointerException
                String sizeStr = jsonObject.getString("size");
                if (StringUtils.isNotBlank(sizeStr)) {
                    fileStatusUpdateDTO.setFileSize(Long.parseLong(sizeStr));
                } else {
                    log.warn("文件大小为空，设置默认值0");
                    fileStatusUpdateDTO.setFileSize(0L);
                }
                
                userFileService.updateFileStatus(fileStatusUpdateDTO);

                // 如果上传的文件是apk文件, 则需要创建一个解析任务
                // if (jsonObject.containsKey("mimeType")
                //         && "application/vnd.android.package-archive".equals(jsonObject.getString("mimeType"))) {
                //     // 创建一个解析任务
                //     appParseService.createAppParseTask(Long.parseLong(fileStorageId));
                // }
                return Result.ok();
            }
            return Result.fail(500, "no fileStorageId");

        } catch (Exception e) {
            log.error("阿里云oss 上传回调失败: {}", e.getMessage(), e);
            return Result.fail(500, "Invalid Callback: " + e.getMessage());
        }
    }

}