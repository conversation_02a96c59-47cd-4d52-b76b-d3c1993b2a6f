package net.armcloud.paascenter.filecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.filecenter.UserAppFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Mapper for fc_user_app_files table
 */
@Mapper
public interface UserAppFileMapper extends BaseMapper<UserAppFile> {

    @Select(value = "select * from fc_user_app_files where customer_id = #{customerId} and file_unique_id = #{fileUniqueId}")
    UserAppFile getByAppFileIdAndCustomerId(@Param("fileUniqueId") Long fileUniqueId,
                                            @Param("customerId") Long customerId);


}