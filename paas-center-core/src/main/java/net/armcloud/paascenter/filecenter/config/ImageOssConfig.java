package net.armcloud.paascenter.filecenter.config;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Aliyun OSS configuration properties
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.image")
public class ImageOssConfig {
    
    /**
     * OSS endpoint
     */
    private String endpoint = "https://oss-accelerate.aliyuncs.com";
    
    /**
     * OSS access key
     */
    private String accessKey = "LTAI5tDctd7zXD8WivUFtNYC";
    
    /**
     * OSS secret key
     */
    private String secretKey = "******************************";
    
    /**
     * OSS region
     */
    private String region = "cn-hongkong";
    
    /**
     * OSS role ARN
     */
    private String roleArn = "acs:ram::1191921788579688:role/ram-oss";

    /**
     * OSS bucket name
     */
    private String bucketName = "cri-0mywummd9qc3mr4t-registry";

    /**
     * OSS registry provider
     */
    private String registryProvider = "aliyun";

    /**
     * OSS registry URL
     */
    private String registryUrl = "https://armcloud-hk-registry.cn-hongkong.cr.aliyuncs.com";

    /**
     * OSS registry username
     */
    private String registryUsername = "docker-registry-system@1191921788579688";

    /**
     * OSS registry password
     */
    private String registryPassword = "0bJZ7UMUBgHvXckWDlSA";

    @PostConstruct
    public void init() {
        log.info("ImageOSS Configuration loaded: endpoint={}, bucketName={}", 
                endpoint, bucketName);
    }
}