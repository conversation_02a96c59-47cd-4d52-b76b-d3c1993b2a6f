package net.armcloud.paascenter;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
@EnableFeignClients(basePackages = {"net.armcloud.paascenter.common.client",
        "net.armcloud.paascenter.cms.manager.cbs.feign",
"net.armcloud.paascenter.feign"})
@ComponentScan(basePackages = {"net.armcloud.paascenter.bmc",
        "net.armcloud.paascenter.callback",
        "net.armcloud.paascenter.cms",
        "net.armcloud.paascenter.common",
        "net.armcloud.paascenter.commscenter",
        "net.armcloud.paascenter.openapi",
        "net.armcloud.paascenter.rtc",
        "net.armcloud.paascenter.task",
        "net.armcloud.paascenter.job",
        "net.armcloud.paascenter.graceful",
        "net.armcloud.paascenter.filecenter"})
@EnableAspectJAutoProxy
public class CenterCoreApplication {


    public static void main(String[] args) {
        SpringApplication.run(CenterCoreApplication.class, args);
    }

}
