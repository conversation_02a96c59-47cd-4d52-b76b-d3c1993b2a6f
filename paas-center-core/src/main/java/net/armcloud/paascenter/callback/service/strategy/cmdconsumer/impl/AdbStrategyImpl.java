package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadAdbCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.command.ExecuteADBCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.PAD_EXECUTE_ADB_TASK_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.ADB_CMD;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.RESULT;

@Slf4j
@Service
public class AdbStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {

    private final CallbackTaskManager callbackTaskManager;

    @Override
    protected String getSubTaskResult(PadCmdResultMessage message) {
        JSONObject jsonObject = JSONObject.parseObject(message.getJsonData());
        return Optional.ofNullable(jsonObject.getString(RESULT)).orElse("");
    }

    @Override
    public int getByCallbackType() {
        return PAD_EXECUTE_ADB_TASK_CALLBACK_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId) {
        ExecuteADBCMDDTO cmdData = JSON.parseObject(message.getJsonData(), ExecuteADBCMDDTO.class);
        PadAdbCallbackVO result = new PadAdbCallbackVO();
        result.setCmd(cmdData.getCmd());
        result.setCmdResult(cmdData.getResult());
        result.setPadCode(message.getPadCode());
        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        if (isNotEmpty(data) && isNotEmpty(data.getPadTask())) {
            PadTask padTask = data.getPadTask();
            result.setTaskId(padTask.getCustomerTaskId());
            result.setTaskStatus(padTask.getStatus());
            if (isNotEmpty(padTask.getEndTime())) {
                result.setEndTime(padTask.getEndTime().getTime());
            }
            result.setTaskContent(padTask.getTaskContent());
            result.setTaskResult(padTask.getResult());
            result.setTaskBusinessType(TaskTypeConstants.EXECUTE_COMMAND.getType());
        }
        return result;
    }

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(ADB_CMD.getCommand(), "adbStrategyImpl");
    }

    public AdbStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                           CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
    }

}
