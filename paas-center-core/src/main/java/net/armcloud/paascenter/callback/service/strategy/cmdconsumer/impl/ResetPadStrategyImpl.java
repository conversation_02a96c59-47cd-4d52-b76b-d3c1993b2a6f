package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.RESET;

@Service
public class ResetPadStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {

    /**
     * 重置指令和任务额外处理，不根据pad通知的结果处理
     */
    @Override
    protected boolean discardMessage(PadCmdResultMessage message) {
        return true;
    }

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(RESET.getCommand(), "resetPadStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return 0;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        return null;
    }

    public ResetPadStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                CustomerCallbackManager customerCallbackManager,
                                PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
    }
}
