package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;

@Data
public class PadAdbTaskCallbackVO {
    /**
     * 子任务ID
     */
    private Integer taskId;

    /**
     * 实例编号
     */
    private String padCode;

    /**
     * 执行结果：true-成功，false-失败
     */
    private Boolean result;

    /**
     * 执行的命令
     */
    private String cmd;

    /**
     * 执行的命令返回
     */
    private String cmdResult;

    /**
     * 错误码
     */
    private String errorCode;
}
