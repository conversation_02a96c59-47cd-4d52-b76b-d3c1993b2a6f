package net.armcloud.paascenter.callback.rocketmq.pullmode;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceTaskResultMQ;
import net.armcloud.paascenter.common.model.mq.container.ContainerInstanceTaskResultMQ;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.model.dto.PullTaskResultDTO;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * topic:vcp_pull_task_submit_result
 * 处理拉模式下 任务结果上报的消息
 * 这里只是清理一下 再次转发到原来的处理逻辑
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.vcp-pull-task-submit-result}_consumer_callback", topic = "${producer-topic.vcp-pull-task-submit-result}")
public class PullTaskResultConsumer implements AliRocketMQListener<MessageView> {
    @Autowired
    private DefaultRocketMqProducerWrapper rocketMqProducerService;
    @Autowired
    private PadMapper padMapper;
    @Autowired
    private ITaskService taskService;

    /**gameserver任务回调主题*/
    @Value("${producer-topic.vcp-pod-cmd-result}")
    private String vcpPodCmdResultTopic;
    /**实例任务回调主题*/
    @Value("${mq.container-instance-task-message.topic}")
    private String containerInstanceTaskMessageTopic;
    /**板卡任务回调主题*/
    @Value("${mq.container-device-task-message.topic}")
    private String containerDeviceTaskMessageTopic;

    @Override
    public void onMessage(MessageView messageView) {
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        PullTaskResultDTO pullTaskResultDTO = JSON.parseObject(messageStr, PullTaskResultDTO.class);
        if (Objects.isNull(pullTaskResultDTO)) {
            log.error("PullTaskResultConsumer pullTaskResultDTO is null>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }
        String mqJson = null;
        String topic = null;
        if(TaskChannelEnum.GAMESERVER.getCode().equals(pullTaskResultDTO.getDeviceType())){
            PadCmdResultMessage padCmdResultMessage = new PadCmdResultMessage();
            padCmdResultMessage.setPadCode(pullTaskResultDTO.getDeviceCode());
            padCmdResultMessage.setCommand(pullTaskResultDTO.getCommand());
            padCmdResultMessage.setJsonData(pullTaskResultDTO.getResult());
            padCmdResultMessage.setStatus(pullTaskResultDTO.getTaskStatus());
            padCmdResultMessage.setMsg(pullTaskResultDTO.getErrMsg());
            mqJson = JSON.toJSONString(padCmdResultMessage);
            topic = vcpPodCmdResultTopic;
        }else if(TaskChannelEnum.CBS.getCode().equals(pullTaskResultDTO.getDeviceType())){
            //是否操作板卡本身的任务
            boolean isOperateDevice = false;
            TaskTypeAndChannelEnum taskTypeAndChannelEnum = null;
            if(pullTaskResultDTO.getTaskType() != null){
                //cbs任务中存在操作实例和操作板卡本身的 两类任务分别存在pad_task和device_task表 所以需要cbs传递任务类型回来
                taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(pullTaskResultDTO.getTaskType());
                isOperateDevice = taskTypeAndChannelEnum != null && "device".equals(taskTypeAndChannelEnum.getSmallerType());
            }

            if(isOperateDevice){
                ContainerDeviceTaskResultMQ containerDeviceTaskResultMQ = new ContainerDeviceTaskResultMQ();
                containerDeviceTaskResultMQ.setDeviceIp(pullTaskResultDTO.getDeviceIp());
                containerDeviceTaskResultMQ.setMasterTaskId(pullTaskResultDTO.getTaskId());
                containerDeviceTaskResultMQ.setMasterTaskStatus(pullTaskResultDTO.getTaskStatus());
                containerDeviceTaskResultMQ.setMsg(pullTaskResultDTO.getErrMsg());
                containerDeviceTaskResultMQ.setData(pullTaskResultDTO.getResult());
                containerDeviceTaskResultMQ.setPullMode(true);
                mqJson = JSON.toJSONString(containerDeviceTaskResultMQ);
                topic = containerDeviceTaskMessageTopic;
            }else{
                String padCode = pullTaskResultDTO.getDeviceCode();
                if(StrUtil.isEmpty(padCode)){
                    Pad pad = padMapper.selectPadByPadIp(pullTaskResultDTO.getDeviceIp(),pullTaskResultDTO.getClusterCode());
                    if(pad == null){
                        log.error("PullTaskResultConsumer cbs任务未获取到实例编号，{}",messageStr);
                    }else {
                        padCode = pad.getPadCode();
                    }
                }
                ContainerInstanceTaskResultMQ containerInstanceTaskResultMQ = new ContainerInstanceTaskResultMQ();
                containerInstanceTaskResultMQ.setPadCode(padCode);
                containerInstanceTaskResultMQ.setMasterTaskId(pullTaskResultDTO.getTaskId());
                containerInstanceTaskResultMQ.setMasterTaskStatus(pullTaskResultDTO.getTaskStatus());
                containerInstanceTaskResultMQ.setMsg(pullTaskResultDTO.getErrMsg());
                containerInstanceTaskResultMQ.setPullMode(true);
                containerInstanceTaskResultMQ.setResult(pullTaskResultDTO.getResult());
                mqJson = JSON.toJSONString(containerInstanceTaskResultMQ);
                topic = containerInstanceTaskMessageTopic;
            }

            //如果任务成功 则创建实例、一键新机、升级镜像 都需要存task_rel_instance_detail_image_succ
            taskService.saveDeviceInstanceSucc(pullTaskResultDTO.getTaskId(),taskTypeAndChannelEnum);
        }else if(TaskChannelEnum.BMC.getCode().equals(pullTaskResultDTO.getDeviceType())){
            taskService.updateBmcTask(pullTaskResultDTO);
        }
        if(StrUtil.isNotEmpty(topic)){
            rocketMqProducerService.producerNormalMessage(topic, mqJson);
        }
    }
}
