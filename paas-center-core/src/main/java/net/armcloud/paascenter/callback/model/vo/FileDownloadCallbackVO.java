package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FileDownloadCallbackVO extends BaseTaskCallbackVO {
    private String originFileUrl;
    private String fileUniqueId;
    /**
     * 任务类型
     */
    private Integer taskBusinessType;
    /**
          * 子任务状态
          */
    private Integer taskStatus;
}
