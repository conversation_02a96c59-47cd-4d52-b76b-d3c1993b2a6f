package net.armcloud.paascenter.callback.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.armcloud.paascenter.common.client.internal.dto.UpdateInfoDTO;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.filecenter.mapper.UserFileMapper;

@Component
public class FileCenterManager {
    // private final FileCenterFileInternalFacade fileCenterFileFacade;

    @Autowired
    private UserFileMapper userFileMapper;

    public void updateCustomerDownloadFileInfo(UpdateInfoDTO dto) {

        // FeignUtils.getContent(fileCenterFileFacade.updateCustomerDownloadFileInfo(dto));
    }

    public UserFile getFileIdByCustomerFileId(long fileId) {
        return userFileMapper.selectById(fileId);
    }


}
