package net.armcloud.paascenter.callback.controller;


import com.alibaba.fastjson.JSONObject;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.model.vo.PadDownloadAppFileCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.facade.TimeOutMsgFacade;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.InstallAppTaskTimeOutMsgDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@RestController
public class TimeOutMsgController implements TimeOutMsgFacade {

    @Resource
    private   PadCmdConsumerStrategyContext padCmdConsumerStrategyContext;

    @Resource
    private CustomerCallbackManager customerCallbackManager;
    public Result<String> cmdCallBack(@RequestBody InstallAppTaskTimeOutMsgDTO dto) {
        log.info("TimeOutMsgController.cmdCallBack dto:{}",JSONObject.toJSONString(dto));
        AbstractPadCmdConsumerStrategy strategy = padCmdConsumerStrategyContext.getInstanceByBusinessType(dto.getTaskBusinessType());
        if(Objects.isNull(strategy)){
            log.info("TimeOutMsgController.cmdCallBack strategy is null. dto:{}", JSONObject.toJSONString(dto));
            return Result.ok();
        }
        PadDownloadAppFileCallbackVO callbackVO = PadDownloadAppFileCallbackVO.build(dto);
        customerCallbackManager.callback(dto.getCustomerId(),strategy.getByCallbackType(),() -> callbackVO);
        return Result.ok();
    }


}
