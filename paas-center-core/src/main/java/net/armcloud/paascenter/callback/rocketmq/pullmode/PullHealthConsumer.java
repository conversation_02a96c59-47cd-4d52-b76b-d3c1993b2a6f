package net.armcloud.paascenter.callback.rocketmq.pullmode;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceStatusMQ;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.IArmService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.task.config.PullModeImageConfig;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.task.model.dto.PullTaskHealthDTO;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * topic:vcp_pull_health
 * 处理拉模式下 设备在线状态上报
 * 这里只是清理一下 再次转发到原来的处理逻辑
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.vcp-pull-health}_consumer_callback", topic = "${producer-topic.vcp-pull-health}")
public class PullHealthConsumer implements AliRocketMQListener<MessageView> {
    @Autowired
    private DefaultRocketMqProducerWrapper rocketMqProducerService;
    @Autowired
    private ITaskService taskService;
    @Autowired
    private IArmService armService;

    @Autowired
    private PullModeImageConfig pullModeImageConfig;
    @Autowired
    private IPadService padService;

    @Autowired
    private PadMapper padMapper;

    int defaultThreads = Runtime.getRuntime().availableProcessors();
    int maximumPoolSize = defaultThreads * 2 + 1;
    private final ExecutorService threadPool = new ThreadPoolExecutor(defaultThreads, maximumPoolSize,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(5000), new ThreadFactoryBuilder()
            .setNameFormat("PullHealthConsumerThreadPool-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**实例在线状态消息主题*/
    @Value("${mq.vcp-pad-ws-status.topic}")
    private String vcpPodCmdResultTopic;
    /**板卡在线状态消息主题*/
    @Value("${mq.container-device-status-message.topic}")
    private String containerDeviceStatusMessageTopic;

    @Override
    public void onMessage(MessageView messageView) {
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        PullTaskHealthDTO pullTaskHealthDTO = JSON.parseObject(messageStr, PullTaskHealthDTO.class);
        if (Objects.isNull(pullTaskHealthDTO)) {
            log.error("PullHealthConsumer pullTaskHealthDTO is null>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }
        String mqJson = null;
        String topic = null;
        Boolean gsPushModeImage = false;
        if(TaskChannelEnum.GAMESERVER.getCode().equals(pullTaskHealthDTO.getDeviceType())){
            padService.checkIp(pullTaskHealthDTO.getDeviceCode(), pullTaskHealthDTO.getDeviceIp());
            PadWSStatusMessage message = new PadWSStatusMessage();
            message.setPadCode(pullTaskHealthDTO.getDeviceCode());
            message.setConnected(pullTaskHealthDTO.getOnline() == 1);
            message.setPullMode(true);

            //判断当前实例的镜像是否为兼容2.0的  是则跳过 因为1.0的也会连上来
            Pad pad = padMapper.selectPadByPadCode(pullTaskHealthDTO.getDeviceCode());
            if(pad != null && StrUtil.isNotEmpty(pad.getImageId())){
                gsPushModeImage = StrUtil.isNotEmpty(pullModeImageConfig.getPushModeImageIds()) && pullModeImageConfig.getPushModeImageIds().contains(pad.getImageId());
            }
            if(!gsPushModeImage){
                mqJson = JSON.toJSONString(message);
                topic = vcpPodCmdResultTopic;
            }else{
                log.info("PullHealthConsumer pad.getImageId():{}",pad.getImageId());
            }
        }else if(TaskChannelEnum.CBS.getCode().equals(pullTaskHealthDTO.getDeviceType())){
            ContainerDeviceStatusMQ containerDeviceStatusMQ = new ContainerDeviceStatusMQ();
            containerDeviceStatusMQ.setDeviceIp(pullTaskHealthDTO.getDeviceIp());
            containerDeviceStatusMQ.setDeviceStatus(pullTaskHealthDTO.getOnline());
            containerDeviceStatusMQ.setClusterCode(pullTaskHealthDTO.getClusterCode());
            containerDeviceStatusMQ.setVersion(pullTaskHealthDTO.getVersion());
            containerDeviceStatusMQ.setVersionCode(pullTaskHealthDTO.getVersionCode());
            mqJson = JSON.toJSONString(containerDeviceStatusMQ);
            topic = containerDeviceStatusMessageTopic;
        }else if(TaskChannelEnum.BMC.getCode().equals(pullTaskHealthDTO.getDeviceType())){
            //更新arm服务器状态
            armService.armServerUpdateStatus(pullTaskHealthDTO.getClusterCode(),pullTaskHealthDTO.getDeviceIp(),pullTaskHealthDTO.getOnline(),pullTaskHealthDTO.getVersion());
        }

        if(StrUtil.isNotEmpty(topic)){
            String hasKey = null;
            if(StrUtil.isNotEmpty(pullTaskHealthDTO.getDeviceIp())){
                hasKey = pullTaskHealthDTO.getClusterCode() + "_" + pullTaskHealthDTO.getDeviceIp();
            }else if(StrUtil.isNotEmpty(pullTaskHealthDTO.getDeviceCode())){
                hasKey = pullTaskHealthDTO.getClusterCode() + "_" + pullTaskHealthDTO.getDeviceCode();
            }
            if(StrUtil.isEmpty(hasKey)){
                rocketMqProducerService.producerNormalMessage(topic, mqJson);
            }else{
                rocketMqProducerService.producerOrderlyNormalMessage(topic, mqJson,hasKey);
            }

        }

        //收到离线状态 异步处理任务状态 板卡离线 则清除板卡的任务 实例离线则处理实例的任务
        if(pullTaskHealthDTO.getOnline() == 0 && !gsPushModeImage){
            threadPool.submit(() -> {
                taskService.offlineCleanTask(pullTaskHealthDTO);
            });
        }
    }
}
