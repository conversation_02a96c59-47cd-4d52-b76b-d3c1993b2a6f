package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.PROXY_CONFIGURE;

@Service
public class SetPadNetworkProxyStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(PROXY_CONFIGURE.getCommand(), "setPadNetworkProxyStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return -1;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId) {
        return null;
    }

    public SetPadNetworkProxyStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                          CustomerCallbackManager customerCallbackManager,
                                          PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
    }
}
