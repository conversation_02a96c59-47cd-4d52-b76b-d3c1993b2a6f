package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadDownloadFileCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.command.PadDownloadFileCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.PAD_DOWNLOAD_FILE_TASK_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.DOWNLOAD_FILE_CMD;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.SUCCESS;

@Service
public class DownloadFileStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {

    private final CallbackTaskManager callbackTaskManager;

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(DOWNLOAD_FILE_CMD.getCommand(), "downloadFileStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return PAD_DOWNLOAD_FILE_TASK_CALLBACK_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId) {
        PadDownloadFileCMDDTO cmdData = JSON.parseObject(message.getJsonData(), PadDownloadFileCMDDTO.class);
        PadDownloadFileCallbackVO result = new PadDownloadFileCallbackVO();
//        result.setOriginFileUrl(cmdData.getPath());
        result.setPadCode(message.getPadCode());
        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        if (isNotEmpty(data) && isNotEmpty(data.getPadTask())) {
            result.setTaskId(data.getPadTask().getCustomerTaskId());
            result.setResult(SUCCESS.getStatus().equals(data.getPadTask().getStatus()) ? true : false);
            if (isNotEmpty(data.getFileCustomer())) {
                result.setFileId(data.getFileCustomer().getUniqueId());
            }
            result.setTaskBusinessType(TaskTypeConstants.DOWNLOAD_FILE.getType());
        }
        return result;
    }

    public DownloadFileStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                    CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
    }

}
