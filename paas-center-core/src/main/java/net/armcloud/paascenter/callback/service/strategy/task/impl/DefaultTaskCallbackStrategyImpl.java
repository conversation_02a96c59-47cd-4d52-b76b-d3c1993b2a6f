package net.armcloud.paascenter.callback.service.strategy.task.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.mapper.CallbackCustomerCallbackMapper;
import net.armcloud.paascenter.callback.model.CustomerCallbackInfoVO;
import net.armcloud.paascenter.callback.model.vo.PadDefaultTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.task.ITaskCallbackStrategy;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class DefaultTaskCallbackStrategyImpl implements ITaskCallbackStrategy {
    private final CustomerCallbackManager customerCallbackManager;

    private final CallbackCustomerCallbackMapper callbackCustomerCallbackMapper;
    @Override
    public void handler(PadStatusTaskMessageMQ message) {
//        log.info("start default handler callback >>> message:{}", JSON.toJSONString(message));
//        log.info("start handler DefaultTaskCallbackStrategyImpl task callback >>> message:{}", JSON.toJSONString(message));
        PadDefaultTaskCallbackVO callbackVO = new PadDefaultTaskCallbackVO();
        callbackVO.setPadCode(message.getPadCode());
        callbackVO.setTaskBusinessType(message.getTaskBusinessType());
        callbackVO.setTaskStatus(message.getTaskStatus());
        callbackVO.setTaskId(message.getTaskId());
        callbackVO.setTaskContent(message.getTaskContent());
        callbackVO.setEndTime(Objects.isNull(message.getEndTime())?System.currentTimeMillis():message.getEndTime());
        callbackVO.setTaskResult(message.getTaskResult());
        if(Objects.isNull(message.getTaskBusinessType())){
            log.warn("DefaultTaskCallbackStrategyImpl process task callback. but message_taskBusinessType_is_empty message:{}", JSON.toJSONString(message));
            return ;
        }
        List<CustomerCallbackInfoVO> list = callbackCustomerCallbackMapper.listAllTaskBusinessTypeNonEmpty();
        list.stream().filter(customerCallbackInfoVO -> Objects.equals(customerCallbackInfoVO.getTaskBusinessType(),message.getTaskBusinessType())).findFirst().ifPresent(customerCallbackInfoVO -> {
            customerCallbackManager.callback(message.getCustomerId(), customerCallbackInfoVO.getType(), () -> callbackVO);
        });
//        log.info("end default handler callback  >>> message:{} callbackVO:{}", JSON.toJSONString(message), JSON.toJSONString(callbackVO));
    }

    public DefaultTaskCallbackStrategyImpl(CustomerCallbackManager customerCallbackManager, CallbackCustomerCallbackMapper callbackCustomerCallbackMapper) {
        this.customerCallbackManager = customerCallbackManager;
        this.callbackCustomerCallbackMapper = callbackCustomerCallbackMapper;
    }
}
