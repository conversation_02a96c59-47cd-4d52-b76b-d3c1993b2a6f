package net.armcloud.paascenter.callback.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.mapper.CallbackCustomerAccessMapper;
import net.armcloud.paascenter.callback.mapper.CallbackCustomerCallbackMapper;
import net.armcloud.paascenter.callback.model.CustomerCallbackInfoVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.http.HttpClientUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static net.armcloud.paascenter.callback.constant.CacheConstants.CUSTOMER_ACCESS_PREFIX;
import static net.armcloud.paascenter.callback.constant.CacheConstants.CUSTOMER_CALLBACK_LIST_PREFIX;

@Slf4j
@Component
public class CustomerCallbackManager {
    private final CallbackCustomerCallbackMapper callbackCustomerCallbackMapper;
    private final RedisService redisService;
    private final CallbackCustomerAccessMapper callbackCustomerAccessMapper;
    ExecutorService customerCallbackExecutor = new ThreadPoolExecutor(
            50, // 核心线程数
            50, // 最大线程数
            60L, TimeUnit.SECONDS, // 线程空闲存活时间
            new SynchronousQueue<>(), // 任务直接交给线程处理，没有队列
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：主线程执行任务
    );


    public List<CustomerCallbackInfoVO> listCache(long customerId) {
        String key = CUSTOMER_CALLBACK_LIST_PREFIX + customerId;
        if (Boolean.TRUE.equals(redisService.hasKey(key))) {
            return JSONArray.parseArray(redisService.getCacheObject(key), CustomerCallbackInfoVO.class);
        }

        List<CustomerCallbackInfoVO> customerCallbacks = callbackCustomerCallbackMapper.listByCustomerId(customerId);
        //缓存一分钟的回调地址
        redisService.setCacheObject(key, JSON.toJSONString(customerCallbacks), 1L, TimeUnit.MINUTES);
        return customerCallbacks;
    }

    public CustomerAccess getCustomerAccess(long customerId) {
        String key = CUSTOMER_ACCESS_PREFIX + customerId;
        if (Boolean.TRUE.equals(redisService.hasKey(key))) {
            String objStr = redisService.getCacheObject(key);
            return JSON.parseObject(objStr, CustomerAccess.class);
        }

        CustomerAccess customerAccess = callbackCustomerAccessMapper.getByCustomerId(customerId);
        redisService.setCacheObject(key, JSON.toJSONString(customerAccess), 10L, TimeUnit.MINUTES);
        return customerAccess;
    }

    public void callback(long customerId, int callbackType, Supplier<Object> callbackData) {
        List<CustomerCallbackInfoVO> callbackInformationList = this.listCache(customerId);
        CustomerCallbackInfoVO callbackInfo = callbackInformationList.stream()
                .filter(callback -> Objects.equals(callback.getType(), callbackType))
                .findFirst()
                .orElse(null);

        log.info("Start callback >>> customerId={}, callbackType={}, callbackInfo={}",
                customerId, callbackType, JSON.toJSONString(callbackInfo));

        if (Objects.isNull(callbackInfo)) {
            log.warn("No callback info found, skipping callback for customerId={}", customerId);
            return;
        }

        String callbackDtaJson = JSON.toJSONString(callbackData.get(), SerializerFeature.WriteMapNullValue);
        CustomerAccess access = this.getCustomerAccess(customerId);
        if (Objects.isNull(access)) {
            log.warn("CustomerAccess is null, skipping callback >>> customerId={}, callbackDtaJson={}",
                    customerId, callbackDtaJson);
            return;
        }


        customerCallbackExecutor.submit(() -> {
            String customerSK = access.getSecretAccessKey();
            int maxRetries = 1; // 最大重试次数
            int attempt = 0;    // 当前尝试次数
            int statusCode = 0; // HTTP 状态码
            Map<String, Object> responseMap = null;
            while (attempt < maxRetries) {
                attempt++;
                try {
                    log.info("Attempt {}: Sending callback >>> callbackInfo={}, callbackDtaJson={}",
                            attempt, JSON.toJSONString(callbackInfo), callbackDtaJson);
                    TimeInterval timer = DateUtil.timer(); // 开始计时

                    responseMap = HttpClientUtils.sendHttpCallbackRequest(
                            callbackInfo.getHost(),
                            callbackInfo.getCallbackUrl(),
                            callbackDtaJson,
                            customerSK
                    );

                    long elapsed = timer.interval(); // 获取耗时（毫秒）

                    if (elapsed > 500) {
                        log.warn("Callback耗时过长：{}ms, callbackInfo={}, callbackDtaJson={}",
                                elapsed, JSON.toJSONString(callbackInfo), callbackDtaJson);
                    }
                    statusCode = (int) responseMap.get("statusCode");

                    if (statusCode == 200) {
                        log.info("Callback successful,callbackUrl:{},callbackDtaJson:{},statusCode={}, attempt={}", callbackInfo.getCallbackUrl(), callbackDtaJson, statusCode, attempt);
                        break; // 请求成功，跳出循环
                    } else {
                        log.warn("Callback failed, callbackUrl:{},callbackDtaJson:{},statusCode={}, attempt={}", callbackInfo.getCallbackUrl(), callbackDtaJson, statusCode, attempt);
                        if (attempt < maxRetries) {
                            Thread.sleep(500); // 休眠 2 秒后重试
                        }
                    }
                } catch (Exception e) {
                    log.error("Callback failed on attempt {} >>> error={}, callbackInfo,:{},callbackDtaJson={}, customerId={}",
                            attempt, e.getMessage(), callbackInfo,callbackDtaJson, customerId, e);
                    if (attempt < maxRetries) {
                        try {
                            Thread.sleep(500); // 休眠 2 秒后重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.error("Thread interrupted while waiting to retry callback", ie);
                            return;
                        }
                    }
                }
            }

            if (statusCode != 200) {
                log.error("Callback failed after {} attempts >>> last statusCode={}, callbackDtaJson={}, customerId={}",
                        maxRetries, statusCode, callbackDtaJson, customerId);
            }
        });

    }

    public CustomerCallbackManager(CallbackCustomerCallbackMapper callbackCustomerCallbackMapper, RedisService redisService, CallbackCustomerAccessMapper callbackCustomerAccessMapper) {
        this.callbackCustomerCallbackMapper = callbackCustomerCallbackMapper;
        this.redisService = redisService;
        this.callbackCustomerAccessMapper = callbackCustomerAccessMapper;
    }
}
