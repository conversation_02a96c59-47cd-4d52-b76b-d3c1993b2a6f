package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadAppStartCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.command.PadStartAppCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.APP_START_OR_STOP_APP_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.START_APP_CMD;

@Service
public class AppStartStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    private final CallbackTaskManager callbackTaskManager;
    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(START_APP_CMD.getCommand(), "appStartStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return APP_START_OR_STOP_APP_CALLBACK_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        PadStartAppCMDDTO cmdData = JSON.parseObject(message.getJsonData(), PadStartAppCMDDTO.class);
        //查询任务状态
        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        PadAppStartCallbackVO result = new PadAppStartCallbackVO();
        result.setPackageName(cmdData.getPackageName());
        result.setPadCode(message.getPadCode());
        if(isNotEmpty(data) && isNotEmpty(data.getPadTask())){
            result.setTaskId(data.getPadTask().getCustomerTaskId());
            result.setTaskStatus(data.getPadTask().getStatus());
            result.setTaskBusinessType(TaskTypeConstants.START_APP.getType());
        }
        return result;
    }

    public AppStartStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
    }
}
