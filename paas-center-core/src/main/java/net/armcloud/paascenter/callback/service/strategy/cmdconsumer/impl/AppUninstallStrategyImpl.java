package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadAppUninstallCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadDownloadAppFileCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.command.PadUninstallAppCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.Collections;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.PAD_UNINSTALL_APP_TASK_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UNINSTALL_APP_CMD;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.SUCCESS;

@Service
public class AppUninstallStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    private final CallbackTaskManager callbackTaskManager;

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(UNINSTALL_APP_CMD.getCommand(), "appUninstallStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return PAD_UNINSTALL_APP_TASK_CALLBACK_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        PadUninstallAppCMDDTO cmdData = JSON.parseObject(message.getJsonData(), PadUninstallAppCMDDTO.class);
        PadAppUninstallCallbackVO result = new PadAppUninstallCallbackVO();
//        result.setFileId(cmdData.getFileId());
//        result.setPadCode(message.getPadCode());
        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        if (isNotEmpty(data) && isNotEmpty(data.getPadTask())) {
            result.setTaskId(data.getPadTask().getCustomerTaskId());
            if (isNotEmpty(data.getFileCustomer())) {
                FileCustomer fileCustomer = data.getFileCustomer();
                PadDownloadAppFileCallbackVO.AppInfo appInfo = new PadDownloadAppFileCallbackVO.AppInfo();
                appInfo.setAppId(fileCustomer.getAppId());
                appInfo.setAppName(fileCustomer.getAppName());
                appInfo.setPkgName(fileCustomer.getAppPackageName());
                appInfo.setPadCode(message.getPadCode());
                appInfo.setResult(SUCCESS.getStatus().equals(data.getPadTask().getStatus()) ? true : false);
                result.setApps(Collections.singletonList(appInfo));
            }
        }
        return result;
    }

    public AppUninstallStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                    CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
    }
}
