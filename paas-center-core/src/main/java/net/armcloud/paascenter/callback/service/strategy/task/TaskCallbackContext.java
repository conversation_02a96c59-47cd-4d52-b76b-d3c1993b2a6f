package net.armcloud.paascenter.callback.service.strategy.task;

import net.armcloud.paascenter.callback.service.strategy.task.impl.DefaultTaskCallbackStrategyImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class TaskCallbackContext {
    private static final Map<Integer, String> TASK_TYPE_REF_BEAN_NAME_MAP = new HashMap<>();
    private final ApplicationContext applicationContext;
    private final DefaultTaskCallbackStrategyImpl defaultCallbackContentStrategy;

    public TaskCallbackContext(ApplicationContext applicationContext, DefaultTaskCallbackStrategyImpl defaultCallbackContentStrategy) {
        this.applicationContext = applicationContext;
        this.defaultCallbackContentStrategy = defaultCallbackContentStrategy;
    }

    public static void putBeanName(int taskType, String beanName) {
        TASK_TYPE_REF_BEAN_NAME_MAP.put(taskType, beanName);
    }

    public ITaskCallbackStrategy getInstance(int taskType) {
        String beanName = TASK_TYPE_REF_BEAN_NAME_MAP.get(taskType);
        if (StringUtils.isBlank(beanName)) {
            return defaultCallbackContentStrategy;
        }

        return (ITaskCallbackStrategy) applicationContext.getBean(beanName);
    }

}
