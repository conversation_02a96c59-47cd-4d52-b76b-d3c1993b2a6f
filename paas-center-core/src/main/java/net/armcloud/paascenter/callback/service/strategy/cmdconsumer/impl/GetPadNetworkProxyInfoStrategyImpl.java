package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.core.constant.comms.CommsConstant;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.PROXY_INFO;

@Service
public class GetPadNetworkProxyInfoStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(PROXY_INFO.getCommand(), "getPadNetworkProxyInfoStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return -1;
    }

    @Override
    protected void acceptBefore(PadCmdResultMessage message) {
        String jsonData = message.getJsonData();
        if (StringUtils.isBlank(jsonData)) {
            return;
        }

        // gameserver传入的数据在data中，没有传result。为了兼容现有数据设置result内容
        JSONObject jsonObject = JSONObject.parseObject(jsonData);
        if (StringUtils.isNotBlank(jsonObject.getString(CommsConstant.DataField.RESULT))) {
            return;
        }

        Map<String, String> resultData = new HashMap<>(1);
        resultData.put("ip", jsonObject.getString("ip"));
        jsonObject.put(CommsConstant.DataField.RESULT, JSON.toJSONString(resultData));
        message.setJsonData(JSON.toJSONString(jsonObject));
    }

    @Override
    protected String getSubTaskResult(PadCmdResultMessage message) {
        return JSONObject.parseObject(message.getJsonData()).getString(CommsConstant.DataField.RESULT);
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId) {
        return null;
    }

    public GetPadNetworkProxyInfoStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                              CustomerCallbackManager customerCallbackManager,
                                              PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager,padCommsDataService);
    }
}
