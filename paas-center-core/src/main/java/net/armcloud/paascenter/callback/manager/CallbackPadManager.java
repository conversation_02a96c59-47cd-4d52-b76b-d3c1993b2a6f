package net.armcloud.paascenter.callback.manager;

import net.armcloud.paascenter.callback.mapper.CallbackPadInstalledAppInformationMapper;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.model.dto.api.PadStatusDTO;
import net.armcloud.paascenter.common.model.dto.api.UpdatePadOnlineDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.openapi.service.IPadStatusService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Collections;

import static net.armcloud.paascenter.callback.constant.CacheConstants.PAD_INSTALLED_APP_PREFIX;
import static net.armcloud.paascenter.common.core.constant.Constants.NO;
import static net.armcloud.paascenter.common.core.constant.Constants.YES;

@Component
public class CallbackPadManager {
    private final RedisService redisService;
    private final CallbackPadInstalledAppInformationMapper callbackPadInstalledAppInformationMapper;

    private final IPadService padService;
    private final IPadStatusService padStatusService;

    public void updatePadStatusRunning(String padCode) {
        PadStatusDTO padStatusDTO = new PadStatusDTO();
        padStatusDTO.setPadCode(padCode);
        padStatusDTO.setPadStatus(PadStatusConstant.RUNNING);
        padStatusDTO.setOprBusiness("Long Connection-updatePadStatusRunning");
        Pad pad = padService.getPadByPadCode(padStatusDTO.getPadCode());
        padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padStatusDTO.getPadCode()), padStatusDTO.getPadStatus(), pad.getCustomerId(), padStatusDTO.getOprBusiness());
    }

    public void updatePadOnline(String padCode, boolean online, String imageId, Long dataSize, Long dataSizeUsed, Long dataSizeAvailable, String rtcVersionName, Integer rtcVersionCode,String adbEnable) {
        UpdatePadOnlineDTO updatePadOnlineDTO = new UpdatePadOnlineDTO();
        updatePadOnlineDTO.setPadCodes(Collections.singletonList(padCode));
        updatePadOnlineDTO.setOnline(online ? YES : NO);
        updatePadOnlineDTO.setImageId(imageId);
        updatePadOnlineDTO.setDataSize(dataSize);
        updatePadOnlineDTO.setDataSizeUsed(dataSizeUsed);
        updatePadOnlineDTO.setDataSizeAvailable(dataSizeAvailable);
        updatePadOnlineDTO.setRtcVersionName(rtcVersionName);
        updatePadOnlineDTO.setRtcVersionCode(rtcVersionCode);
        updatePadOnlineDTO.setAdbEnable(adbEnable);
        padStatusService.updatePadOnline(updatePadOnlineDTO);
    }

    @Async
    public void refreshPadInstalledAppInformation(String padCode, String dataJson) {
        callbackPadInstalledAppInformationMapper.deleteByPadCode(padCode);

        PadInstalledAppInformation padInstalledAppInformation = new PadInstalledAppInformation();
        padInstalledAppInformation.setPadCode(padCode);
        dataJson = StringUtils.isBlank(dataJson) ? "" : dataJson;
        padInstalledAppInformation.setAppsJSON(dataJson);
        callbackPadInstalledAppInformationMapper.insert(padInstalledAppInformation);

        String key = PAD_INSTALLED_APP_PREFIX + padCode;
        redisService.deleteObject(key);
    }

    public CallbackPadManager(RedisService redisService, CallbackPadInstalledAppInformationMapper callbackPadInstalledAppInformationMapper,
                              IPadService padService, IPadStatusService padStatusService) {
        this.redisService = redisService;
        this.callbackPadInstalledAppInformationMapper = callbackPadInstalledAppInformationMapper;
        this.padService = padService;
        this.padStatusService = padStatusService;
    }

}
