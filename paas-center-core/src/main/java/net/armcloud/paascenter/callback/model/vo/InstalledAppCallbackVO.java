package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class InstalledAppCallbackVO extends BaseTaskCallbackVO {
    private String padCode;

    private Integer taskStatus;

    private Integer taskBusinessType;

    private List<APP> apps;

    @Data
    public static class APP {
        private String packageName;

        private String appName;

        private String versionCode;

        private String versionName;

        // 0 已完成  1安装中   2下载中
        private Integer appState;


    }
}
