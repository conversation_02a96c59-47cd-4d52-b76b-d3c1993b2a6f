package net.armcloud.paascenter.callback.service.strategy.cmdconsumer;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants;
import net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum;
import net.armcloud.paascenter.common.core.constant.comms.CommsConstant;
import net.armcloud.paascenter.common.model.mq.cmd.PadAdbCmdMessage;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.Supplier;

import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.CommsCmdStatus.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;

@Slf4j
@Component
public abstract class AbstractPadCmdConsumerStrategy implements IPadCmdConsumerStrategy {
    private final CallbackPadMapper callbackPadMapper;
    private final CallbackTaskManager callbackTaskManager;
    private final CustomerCallbackManager customerCallbackManager;
    private final PadCommsDataService padCommsDataService;
    @Override
    public void accept(PadCmdResultMessage message) {
        acceptBefore(message);


        //临时日志打印
        if(CommsCommandEnum.DOWNLOAD_FILE_APP_CMD.getCommand().equals(message.getCommand())){
            log.info("AbstractPadCmdConsumerStrategy download_file_app:{}",JSON.toJSONString(message));
        }

        if (discardMessage(message)) {
            log.info("AbstractPadCmdConsumerStrategy discardMessage {}", JSON.toJSONString(message));
            return;
        }

        PadAdbCmdMessage padAdbCmdMessage = JSON.parseObject(message.getJsonData(), PadAdbCmdMessage.class);
        if (padAdbCmdMessage == null || (Objects.isNull(padAdbCmdMessage.getTaskId()) && Objects.isNull(padAdbCmdMessage.getSubTaskId()))) {
//            log.info("任务ID为空，则表示此任务是gameServer主动上报指令数据:{}", JSON.toJSONString(message));
            return;
        }

        String padCode = message.getPadCode();

        /**
         *  指令结果应由指令中心模块接受处理，更新pad指令结果
         */
        try {
            padCommsDataService.updateCmdResult(message);
        } catch (Exception e) {
            log.error("updateCmdResult error>>>>message:{}", JSON.toJSONString(message), e);
        }

        // todo 任务状态应由任务模块去接受处理
        Long subTaskId = padAdbCmdMessage.getSubTaskId();
        updateTaskStatus(padAdbCmdMessage, subTaskId, message);


        acceptAfter(message);

        // TODO 第三方回调应监听任务状态变更消息，以此来发送第三方回调消息
        int callbackType = getByCallbackType();
        if (callbackType <= 0) {
            return;
        }

        Long customerId = callbackPadMapper.getCustomerIdByPadCode(padCode);
        if (Objects.isNull(customerId)) {
            log.debug("customerId isnull skip send notify callback >>>>> message:{}", JSON.toJSONString(message));
            return;
        }

        Supplier<Object> callbackData = () -> getCallbackData(message, subTaskId);
        customerCallbackManager.callback(customerId, callbackType, callbackData);
    }

    protected void updateTaskStatus(PadAdbCmdMessage padAdbCmdMessage, Long subTaskId, PadCmdResultMessage message) {
        Long masterTaskId = padAdbCmdMessage.getTaskId();
        if (Objects.isNull(masterTaskId) || Objects.isNull(subTaskId)) {
            log.info("masterTaskId or subTaskId isnull skip accept >>>>> message:{}", JSON.toJSONString(message));
            return;
        }

        if (masterTaskId <= 0 || subTaskId <= 0) {
            return;
        }

        int subTaskStatus = getTaskStatusByCmdStatus(message.getStatus());
        //如果是应用安装 返回的状态为2 则直接更新为成功 (此类场景为 同一个应用同时多次发起安装任务 则非第一个任务都是直接返回状态2(下载中))
        if((CommsCommandEnum.DOWNLOAD_FILE_CMD.getCommand().equals(message.getCommand())
                || CommsCommandEnum.DOWNLOAD_FILE_APP_CMD.getCommand().equals(message.getCommand()))
                && Objects.equals(CommsConstant.CommsCmdStatus.EXECUTING_COMMS_CMD_RECORD_STATUS,message.getStatus())){
            subTaskStatus = CommsConstant.CommsCmdStatus.SUCCESS_COMMS_CMD_RECORD_STATUS;
        }
        try {
            callbackTaskManager.updateSubTaskStatus(masterTaskId, subTaskId, subTaskStatus, getSubTaskResult(message), message.getMsg(),padAdbCmdMessage.getResult());
        } catch (Exception e) {
            log.error("pad cmd consumer updateSubTaskStatus() error >>>>> message:{}", JSON.toJSONString(message), e);
        }
    }

    protected String getSubTaskResult(PadCmdResultMessage message) {
        return "";
    }

    protected boolean discardMessage(PadCmdResultMessage message) {
        return false;
    }

    protected void acceptAfter(PadCmdResultMessage message) {

    }

    protected void acceptBefore(PadCmdResultMessage message) {

    }

    /**
     * 获取当前回调类型
     * <p>
     * {@link CallbackTypeConstants}
     */
    public abstract int getByCallbackType();

    /**
     * 获取回调通知内容
     */
    protected abstract BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId);

    private int getTaskStatusByCmdStatus(Integer cmdStatus) {
        if (Objects.equals(cmdStatus, WAIT_EXECUTE_COMMS_CMD_RECORD_STATUS)) {
            return WAIT_EXECUTE.getStatus();
        }

        if (Objects.equals(cmdStatus, EXECUTING_COMMS_CMD_RECORD_STATUS)) {
            return EXECUTING.getStatus();
        }

        if (Objects.equals(cmdStatus, SUCCESS_COMMS_CMD_RECORD_STATUS)) {
            return SUCCESS.getStatus();
        }

        return FAIL_ALL.getStatus();
    }

    protected AbstractPadCmdConsumerStrategy(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                             CustomerCallbackManager customerCallbackManager,
                                             PadCommsDataService padCommsDataService) {
        this.callbackTaskManager = callbackTaskManager;
        this.callbackPadMapper = callbackPadMapper;
        this.customerCallbackManager = customerCallbackManager;
        this.padCommsDataService = padCommsDataService;
    }
}
