package net.armcloud.paascenter.callback.rocketmq;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.model.vo.PadStatusCallbackVo;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.common.utils.http.HttpClientUtils;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * VcpPadStatusConsumer
 * 状态同步topic:vcp_pod_status
 * 消息来源：MessageNotifyManager
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.vcp-pod-status}_consumer", topic = "${producer-topic.vcp-pod-status}")
public class VcpPadStatusConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("VcpPadStatusConsumer原始消息:{}", str);

        PadStatusTaskMessageMQ dto = JSON.parseObject(str, PadStatusTaskMessageMQ.class);

        //消息幂等
        // String key = RedisKeyPrefix.VCP_PAD_STATUS_MSG_LOCK + messageView.getMessageId().toString();
        // String key = RedisKeyPrefix.VCP_PAD_STATUS_MSG_LOCK + dto.getPadCode() +":"+ dto.getPadStatus();
        // RLock lock = redissonDistributedLock.tryLock(key, 0, 5);
        // if(lock==null){
        //     return;
        // }
        try {
            //下发Pad状态
            PadStatusCallbackVo body = new PadStatusCallbackVo();
            body.setPadCode(dto.getPadCode());
            body.setPadStatus(dto.getPadStatus());
            body.setTaskBusinessType(999);
            body.setPadConnectStatus(dto.getPadConnectStatus());
            log.info("实例状态及在线状态发起回调 {}", JSONUtil.toJsonStr(body));
            HttpClientUtils.sendCallback(dto.getHost(), dto.getUrl(), JSON.toJSONString(body), dto.getSk());
        } catch (Exception e) {
            log.info("实例状态及在线状态回调地址异常，入参：{}，异常信息： {}", str, e.getMessage());
            // throw new RuntimeException(e);
        }finally {
            // redissonDistributedLock.unlock(lock);
        }


    }
}
