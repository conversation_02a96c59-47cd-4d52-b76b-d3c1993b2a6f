package net.armcloud.paascenter.job.mapper;

import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PadTaskBackupMapper {
    PadBackupTaskInfo getLatestByPadCodeAndSubTaskId(@Param("padCode") String padCode, @Param("subTaskId") long subTaskId);

    PadBackupTaskInfo getById(@Param("backupId") long backupId);
}
