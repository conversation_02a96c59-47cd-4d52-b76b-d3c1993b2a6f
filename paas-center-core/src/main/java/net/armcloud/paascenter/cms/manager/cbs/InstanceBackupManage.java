 package net.armcloud.paascenter.cms.manager.cbs;
 
 import net.armcloud.paascenter.cms.model.request.ContainerBackupReqParam;
 import net.armcloud.paascenter.cms.model.request.GetContainerBackupStatusReqParam;
 import net.armcloud.paascenter.cms.model.response.ContainerBackupRespVO;
 import net.armcloud.paascenter.cms.model.response.GetContainerBackupStatusRespVO;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.cms.config.MinIOConfig;
 import net.armcloud.paascenter.cms.manager.cbs.feign.ContainerBackupFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import feign.RetryableException;
 import org.springframework.stereotype.Component;
 
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_CONNECT_EXCEPTION;
 import static net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils.builderHost;
 
 @Component
 public class InstanceBackupManage {
     private final MinIOConfig minIOConfig;
     private final ContainerBackupFeignClient containerBackupFeignClient;
 
     public void backup(String deviceIp, String instanceName, String storagePath) {
 
         ContainerBackupRespVO response;
         try {
             ContainerBackupReqParam param = new ContainerBackupReqParam();
             param.setContainerName(instanceName);
             param.setMinioEndpoint(minIOConfig.getEndpoint());
             param.setMinioAccessKey(minIOConfig.getAccessKey());
             param.setMinioSecretKey(minIOConfig.getSecretKey());
             param.setMinioBucket(minIOConfig.getBucket());
             param.setMinioStorageDirectory(storagePath);
 
             response = containerBackupFeignClient.start(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(response);
     }
 
     public GetContainerBackupStatusRespVO status(String deviceIp, String instanceName, String storagePath) {
         GetContainerBackupStatusRespVO response;
         try {
             GetContainerBackupStatusReqParam param = new GetContainerBackupStatusReqParam();
             param.setContainerName(instanceName);
             param.setMinioEndpoint(minIOConfig.getEndpoint());
             param.setMinioAccessKey(minIOConfig.getAccessKey());
             param.setMinioSecretKey(minIOConfig.getSecretKey());
             param.setMinioBucket(minIOConfig.getBucket());
             param.setMinioStorageDirectory(storagePath);
             response = containerBackupFeignClient.status(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(response);
         return response;
     }
 
     public InstanceBackupManage(MinIOConfig minIOConfig, ContainerBackupFeignClient containerBackupFeignClient) {
         this.minIOConfig = minIOConfig;
         this.containerBackupFeignClient = containerBackupFeignClient;
     }
 }