 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.model.request.ContainerRestoreReqParam;
 import net.armcloud.paascenter.cms.model.request.GetContainerRestoreStatusReqParam;
 import net.armcloud.paascenter.cms.model.response.ContainerRestoreRespVO;
 import net.armcloud.paascenter.cms.model.response.GetContainerRestoreStatusRespVO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-restore", url = "placeholder")
 public interface ContainerRestoreFeignClient {
 
     @PostMapping(value = "/restore/start")
     ContainerRestoreRespVO start(URI deviceHost, @RequestBody ContainerRestoreReqParam param);
 
     @PostMapping(value = "/restore/status")
     GetContainerRestoreStatusRespVO status(URI deviceHost, @RequestBody GetContainerRestoreStatusReqParam param);
 }