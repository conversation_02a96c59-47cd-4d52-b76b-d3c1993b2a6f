 package net.armcloud.paascenter.cms.mapper;
 
 
 import net.armcloud.paascenter.common.model.entity.container.ImageTask;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.Date;
 import java.util.List;
 
 @Mapper
 public interface CmsImageTaskMapper {
     int insert(ImageTask record);
 
     ImageTask getByDcIdAndImageIdAndStatus(@Param("dcId") long dcId, @Param("imageId") String imageId,
                                            @Param("status") int status);
 
 
     List<ImageTask> listEarliestByDcIdAndTargetTaskTypesAndStatus(@Param("dcId") long dcId, @Param("type") int type,
                                                                   @Param("status") int status, @Param("limit") int limit);
 
     void batchUpdateStatusById(@Param("ids") List<Long> ids, @Param("status") int status, @Param("startTime") Date startTime,
                                @Param("endTime") Date endTime, @Param("timeoutTime") Date timeoutTime, @Param("msg") String msg);
 
     List<ImageTask> listByTaskId(@Param("id") long id);
 
     List<ImageTask> listTimeout(@Param("dcId") long dcId);
 
     int updateStatusById(@Param("id") long id, @Param("status") int status, @Param("msg") String msg,
                          @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                          @Param("timeoutTime") Date timeoutTime, @Param("originStatus") Integer originStatus);
 }