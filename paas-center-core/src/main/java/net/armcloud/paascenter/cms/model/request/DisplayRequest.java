 package net.armcloud.paascenter.cms.model.request;
 
 
 import lombok.Data;
 
 import javax.validation.constraints.NotNull;
 
 /**
  * 屏幕布局
  */
 @Data
 public class DisplayRequest {
     /**
      * 宽度
      */
     @NotNull(message = "fps cannot null")
     private Integer width;
 
     /**
      * 高度
      */
     @NotNull(message = "fps cannot null")
     private Integer height;
 
     /**
      * 屏幕像素密度
      */
     @NotNull(message = "fps cannot null")
     private Integer dpi;
 
     /**
      * 屏幕刷新率
      */
     @NotNull(message = "fps cannot null")
     private Integer fps;
 }