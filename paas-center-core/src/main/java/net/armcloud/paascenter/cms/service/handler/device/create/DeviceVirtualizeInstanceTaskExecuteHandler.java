 package net.armcloud.paascenter.cms.service.handler.device.create;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.apache.commons.lang3.StringUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 
 @Slf4j
 @Service
 public class DeviceVirtualizeInstanceTaskExecuteHandler extends AbstractDeviceTaskHandler {
     private final InstanceManager cbsInstanceManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final net.armcloud.paascenter.cms.manager.InstanceManager instanceManager;
     private final HarborConfigManage harborConfigManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final DeviceInstanceTaskMapper deviceInstanceTaskMapper;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
 
     public void start(DeviceTask task) {
         super.asyncExecute(() -> tryStart(task));
     }
 
     private void tryStart(DeviceTask task) {
         String deviceIp = task.getIp();
         long taskId = task.getId();
         String dataJson = JSON.toJSONString(task);
 
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start exec device virtualize task:{}", currentThreadId, dataJson);
         String key = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} exec device virtualize task:{} not get lock key {}", currentThreadId, dataJson, key);
             return;
         }
 
         try {
             task = cmsDeviceTaskMapper.getById(taskId);
             boolean isContinue = isContinue(task);
             if (!isContinue) {
                 return;
             }
 
             List<DeviceInstanceTask> deviceInstanceTasks = deviceInstanceTaskMapper.listByMasterTaskId(taskId);
             if (CollectionUtils.isEmpty(deviceInstanceTasks)) {
                 boolean success = super.updateStatus(task, SUCCESS, "", EXECUTING.getStatus());
                 if (success) {
                     cmsDeviceTaskMapper.updateProgressDescription(taskId, "\n" + LocalDateTime.now() + "创建成功,跳过创建实例");
                 }
                 return;
             }
 
             for (DeviceInstanceTask deviceInstanceTask : deviceInstanceTasks) {
                 boolean success = super.updateStatus(deviceInstanceTask, EXECUTING, "", WAIT_EXECUTE.getStatus());
                 if (!success) {
                     continue;
                 }
 
                 String progressDescription = "\n" + LocalDateTime.now() + " 开始创建实例：" + deviceInstanceTask.getInstanceName();
                 cmsDeviceTaskMapper.updateProgressDescription(taskId, progressDescription);
                 virtualize(task, deviceInstanceTask);
             }
 
         } catch (Exception e) {
             String errorMsg = cbsInstanceManager.destroyAll(deviceIp);
             if (StringUtils.isNotBlank(errorMsg)) {
                 String finalMsg = errorMsg + " and destroy pad fail";
                 super.updateStatus(task, EXCEPTION, finalMsg, null);
                 return;
             }
 
             log.error("execDeviceVirtualizeTask error>>>> masterTaskId:{}", taskId, e);
             super.updateStatus(task, FAIL_ALL, e.getMessage(), null);
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private boolean isContinue(DeviceTask task) {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} isContinue task:{}", currentThreadId, JSON.toJSONString(task));
 
         if (!task.getStatus().equals(EXECUTING.getStatus())) {
             return false;
         }
 
         if (Boolean.FALSE.equals(task.getDiskDone())) {
             return false;
         }
 
         if (Boolean.FALSE.equals(task.getDiskDone())) {
             return false;
         }
 
         if (Boolean.FALSE.equals(task.getNetworkDone())) {
             return false;
         }
 
         return Boolean.TRUE.equals(task.getImageDone());
     }
 
     /**
      * 虚拟化实例
      */
     private void virtualize(DeviceTask task, DeviceInstanceTask deviceTask) {
         long subTaskId = deviceTask.getId();
         TaskRelInstanceDetail taskRelPadDetail = taskRelInstanceDetailMapper
                 .getByMasterTaskIdAndSubTaskId(task.getType(), deviceTask.getMasterTaskId(), subTaskId);
         String instanceName = taskRelPadDetail.getInstanceName();
         HarborConfig harborConfig = harborConfigManage.get();
         CreatePadBO createPadBO = CreatePadBO.builder(task, taskRelPadDetail, harborConfig);
         createPadBO.setClearContainerData(true);
         instanceManager.setDefaultDNS(createPadBO);
         String requestDataJson = JSON.toJSONString(createPadBO);
         taskRelInstanceDetailMapper.updateContainerProperty(taskRelPadDetail.getId(), requestDataJson);
         try {
             cbsInstanceManager.create(createPadBO);
         } catch (Exception e) {
             log.error("virtualize >>>> create error requestDataJson:{}", requestDataJson, e);
             String errorMsg = instanceName + " virtualize error>>>" + e.getMessage();
             super.updateStatus(deviceTask, FAIL_ALL, e.getMessage(), null);
             throw new BasicException(PROCESSING_FAILED.getStatus(), errorMsg);
         }
     }
 
     public DeviceVirtualizeInstanceTaskExecuteHandler(CmsTaskManager cmsTaskManager, InstanceManager cbsInstanceManager,
                                                       net.armcloud.paascenter.cms.manager.InstanceManager instanceManager,
                                                       RedissonDistributedLock redissonDistributedLock,
                                                       TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                                                       DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                                       DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                       DeviceTaskStatusMQConfig deviceTaskStatusMQConfig,
                                                       CmsDeviceTaskMapper cmsDeviceTaskMapper, HarborConfigManage harborConfigManage,
                                                       InstanceDetailImageSuccService instanceDetailImageSuccService) {
         super(cmsTaskManager, deviceInstanceTaskMapper, rocketMqProducerService, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.cbsInstanceManager = cbsInstanceManager;
         this.instanceManager = instanceManager;
         this.redissonDistributedLock = redissonDistributedLock;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.harborConfigManage = harborConfigManage;
         this.deviceInstanceTaskMapper = deviceInstanceTaskMapper;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
     }
 }