 package net.armcloud.paascenter.cms.mapper;
 
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.Date;
 import java.util.List;
 import java.util.Set;
 
 @Mapper
 public interface CmsDeviceTaskMapper {
     Set<String> listIpByDcIdAndStatus(@Param("dcId") long dcId, @Param("status") int status);
 
     List<DeviceTask> listWaitRunningTask(@Param("dcId") long dcId, @Param("excludeDeviceIps") Set<String> excludeDeviceIps);
 
     DeviceTask getById(@Param("id") long id);
 
     void updateProgressDescriptionAndDiskDone(@Param("id") long id, @Param("progressDescription") String progressDescription,
                                               @Param("diskDone") boolean diskDone);
 
     void updateProgressDescription(@Param("id") long id, @Param("progressDescription") String progressDescription);
 
     void updateProgressDescriptionAndNetworkDone(@Param("id") long id, @Param("progressDescription") String progressDescription,
                                                  @Param("networkDone") boolean networkDone);
 
     List<DeviceTask> listWaitVerifyResultTask(@Param("dcId") long dcId, @Param("type") int type);
 
     void insert(DeviceTask deviceTask);
 
     List<DeviceTask> listTimeout(@Param("dcId") long dcId);
 
     int updateStatusById(@Param("id") long id, @Param("status") int status, @Param("msg") String msg,
                          @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                          @Param("timeoutTime") Date timeoutTime, @Param("originStatus") Integer originStatus);
 
     void updateImageDone(@Param("id") long id, @Param("imageDone") boolean imageDone);
 }