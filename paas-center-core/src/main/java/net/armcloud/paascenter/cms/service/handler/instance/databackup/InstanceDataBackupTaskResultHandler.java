 package net.armcloud.paascenter.cms.service.handler.instance.databackup;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.cms.model.response.GetContainerBackupStatusRespVO;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceBackupManage;
 import net.armcloud.paascenter.cms.mapper.InstanceBackupDataProgressMapper;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.HashMap;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.INSTANCE_BACKUP_DATA;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.FAIL_ALL;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY;
 import static net.armcloud.paascenter.cms.utils.DCUtils.getDcId;
 import static java.util.Collections.singletonList;
 
 @Slf4j
 @Service
 public class InstanceDataBackupTaskResultHandler extends AbstractInstanceTaskHandler {
     private final InstanceTaskMapper instanceTaskMapper;
     private final InstanceBackupManage instanceBackupManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final InstanceBackupDataProgressMapper instanceBackupDataProgressMapper;
 
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start verify running task result....", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<InstanceTask> tasks = instanceTaskMapper.listByStatusAndTargetTaskTypes(getDcId(), EXECUTING.getStatus(),
                     singletonList(INSTANCE_BACKUP_DATA.getIntValue()));
             if (CollectionUtils.isEmpty(tasks)) {
                 return;
             }
 
             tasks.forEach(task -> {
                 try {
                     verifyResult(task);
                 } catch (Exception e) {
                     log.error("handlerBackupDataProgress error>>>> task:{}", JSON.toJSONString(task), e);
                 }
             });
         } catch (Exception e) {
             log.error("InstanceDataBackupTaskResultHandler error>>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void verifyResult(InstanceTask task) {
         try {
             String deviceIp = task.getDeviceIp();
             String instanceName = task.getInstanceName();
             InstanceBackupDataProgress dataProgress = instanceBackupDataProgressMapper.getByInstanceTaskId(task.getId());
             GetContainerBackupStatusRespVO response = instanceBackupManage.status(deviceIp, instanceName, dataProgress.getPath());
             if (response.inProgress()) {
                 return;
             }
 
             if (response.failed()) {
                 super.updateStatus(task, FAIL_ALL, "备份失败", EXECUTING.getStatus());
                 return;
             }
 
             Map<String, Object> msgParm = new HashMap<>();
             msgParm.put("filesize", response.getFilesize());
             if (response.succeed()) {
                 super.updateStatus(task, TaskStatusConstants.SUCCESS, JSON.toJSONString(msgParm), EXECUTING.getStatus());
             }
 
         } catch (Exception e) {
             log.error("verify backup result padCode:{} task:{} error>>>>", task.getInstanceName(), JSON.toJSONString(task), e);
             super.updateStatus(task, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         }
     }
 
     protected InstanceDataBackupTaskResultHandler(CmsTaskManager cmsTaskManager, InstanceTaskMapper instanceTaskMapper,
                                                   InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                   DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                   InstanceTaskMapper instanceTaskMapper1, InstanceBackupManage instanceBackupManage,
                                                   RedissonDistributedLock redissonDistributedLock,
                                                   InstanceBackupDataProgressMapper instanceBackupDataProgressMapper,
                                                   InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                   TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceTaskMapper = instanceTaskMapper1;
         this.instanceBackupManage = instanceBackupManage;
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceBackupDataProgressMapper = instanceBackupDataProgressMapper;
     }
 }