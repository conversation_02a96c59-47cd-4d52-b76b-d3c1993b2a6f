 package net.armcloud.paascenter.cms.manager;
 
 import com.alibaba.fastjson2.JSONObject;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.context.ApplicationContext;
 import org.springframework.stereotype.Component;
 
 import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.HARBOR_CONFIGURATION;
 
 @Component
 public class HarborConfigManage {
     private final ConfigurationMapper configurationMapper;
     private final ApplicationContext applicationContext;
 
     public HarborConfig get() {
         String harborConfig = configurationMapper.selectValueByKey(HARBOR_CONFIGURATION);
         if (StringUtils.isBlank(harborConfig)) {
             return applicationContext.getBean(HarborConfig.class);
         }
 
         JSONObject jsonObject = JSONObject.parseObject(harborConfig);
         HarborConfig config = new HarborConfig();
         config.setUrl(jsonObject.getString("uri"));
         config.setUsername(jsonObject.getString("account"));
         config.setPassword(jsonObject.getString("password"));
         config.setProject(jsonObject.getString("project"));
         return config;
     }
 
     public HarborConfigManage(ConfigurationMapper configurationMapper, ApplicationContext applicationContext) {
         this.configurationMapper = configurationMapper;
         this.applicationContext = applicationContext;
     }
 
 }