 package net.armcloud.paascenter.cms.facade;
 
 import net.armcloud.paascenter.cms.model.request.DeviceIpsRequest;
 import net.armcloud.paascenter.cms.model.request.SelfUpdateRequest;
 import net.armcloud.paascenter.cms.model.request.VirtualizeDeviceRequest;
 import net.armcloud.paascenter.cms.model.response.*;
 import net.armcloud.paascenter.common.core.domain.Result;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import javax.validation.Valid;
 import java.util.List;
 
 public interface DeviceFacade {
     /**
      * 创建云机
      */
     @PostMapping(value = "/armcloud-container/open/device/virtualize")
     Result<List<DeviceVirtualizeResponse>> virtualize(@RequestBody VirtualizeDeviceRequest req);
 
     /**
      * 删除云机
      */
     @PostMapping(value = "/armcloud-container/open/device/destroy")
     Result<List<DeviceDestroyResponse>> destroy(@RequestBody DeviceIpsRequest req);
 
     /**
      * 重启云机
      */
     @PostMapping(value = "/armcloud-container/open/device/restart")
     Result<List<DeviceRestartResponse>> restart(@RequestBody DeviceIpsRequest req);
 
     /**
      * 查询板卡信息
      */
     @PostMapping(value = "/armcloud-container/open/device/info")
     Result<List<DeviceInfoResponse>> info(@RequestBody DeviceIpsRequest req);
 
     /**
      * cbs自更新
      */
     @PostMapping(value = "/armcloud-container/open/device/selfUpdate")
     Result<List<SelfUpdateResponse>> selfUpdate(@Valid @RequestBody SelfUpdateRequest request);
 }