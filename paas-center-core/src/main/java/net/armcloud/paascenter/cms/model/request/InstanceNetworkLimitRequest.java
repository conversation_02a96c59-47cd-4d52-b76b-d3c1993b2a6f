 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.Min;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class InstanceNetworkLimitRequest {
     @Valid
     @Size(min = 1, message = "instances cannot null")
     @NotNull(message = "instances cannot null")
     private List<Instance> instances;
 
     @Data
     public static class Instance {
         @NotBlank(message = "padCode cannot null")
         private String padCode;
 
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         /**
          * 最大上行,单位：Mbps
          * -1:禁用
          * 0:不启用限制
          */
         @Min(value = -1, message = "maxUplinkBandwidth out range")
         private String maxUplinkBandwidth;
 
         /**
          * 最大下行,单位：Mbps
          * -1:禁用
          * 0:不启用限制
          */
         @Min(value = -1, message = "maxDownlinkBandwidth out range")
         private String maxDownlinkBandwidth;
     }
 }