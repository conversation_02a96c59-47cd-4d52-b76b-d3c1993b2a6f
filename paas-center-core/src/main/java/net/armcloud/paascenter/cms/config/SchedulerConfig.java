 package net.armcloud.paascenter.cms.config;
 
 import org.springframework.beans.factory.annotation.Value;
 import org.springframework.context.annotation.Bean;
 import org.springframework.context.annotation.Configuration;
 import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
 
 @Configuration
 public class SchedulerConfig {
     @Value("${thread.scheduler.core-size}")
     private Integer coreSize;
 
     @Bean
     public ThreadPoolTaskScheduler taskScheduler() {
         ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
         taskScheduler.setPoolSize(coreSize);
         taskScheduler.setThreadNamePrefix("scheduled-task-");
         taskScheduler.initialize();
         return taskScheduler;
     }
 }