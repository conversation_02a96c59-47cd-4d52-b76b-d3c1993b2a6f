 package net.armcloud.paascenter.cms.service.handler.device.create;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.cms.enums.ContainerImageStatusEnum;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.ImageManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.time.LocalDateTime;
 import java.util.*;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.DEVICE_CREATE;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_DEVICE_VIRTUALIZE_VERIFY_LOCK_KEY;
 
 @Slf4j
 @Service
 public class DeviceVirtualizeTaskResultHandler extends AbstractDeviceTaskHandler {
     private final ImageManager imageManager;
     private final InstanceManager instanceManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final HarborConfigManage harborConfigManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final DeviceInstanceTaskMapper deviceInstanceTaskMapper;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     private final DeviceVirtualizeInstanceTaskExecuteHandler deviceVirtualizeInstanceTaskExecuteHandler;
 
 
     /**
      * 验证进行中的任务结果
      *
      * <p>
      * 任务成功规则：运行中的实例的host名称为当前任务创建的
      */
     public void start() {
         List<DeviceTask> deviceTasks = cmsDeviceTaskMapper.listWaitVerifyResultTask(DCUtils.getDcId(), DEVICE_CREATE.getIntValue());
         if (CollectionUtils.isEmpty(deviceTasks)) {
             return;
         }
 
         Collections.shuffle(deviceTasks);
         deviceTasks.forEach(task -> super.asyncExecute(() -> tryStart(task)));
     }
 
     public void tryStart(DeviceTask deviceTask) {
         long currentThreadId = Thread.currentThread().getId();
         long taskId = deviceTask.getId();
         String lockKey = SCHEDULED_DEVICE_VIRTUALIZE_VERIFY_LOCK_KEY + taskId;
         if (redissonDistributedLock.isHeldByCurrentThread(lockKey)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, lockKey);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(lockKey, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, lockKey);
             return;
         }
 
         try {
             boolean initializationFinished = initializationFinished(deviceTask);
             if (!initializationFinished) {
                 return;
             }
 
             List<DeviceInstanceTask> deviceInstanceTasks = deviceInstanceTaskMapper.listByMasterTaskId(taskId);
             if (CollectionUtils.isEmpty(deviceInstanceTasks)) {
                 super.updateStatus(deviceTask, SUCCESS, "", EXECUTING.getStatus());
                 return;
             }
 
             verifyResultBySubTasks(deviceTask, deviceInstanceTasks);
         } catch (Exception e) {
             log.error("tryStart error>>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void verifyResultBySubTasks(DeviceTask deviceTask, List<DeviceInstanceTask> deviceInstanceTasks) {
         List<DeviceInstanceTask> waitCreateTask = deviceInstanceTasks.stream()
                 .filter(deviceInstanceTask -> WAIT_EXECUTE.getStatus().equals(deviceInstanceTask.getStatus()))
                 .collect(Collectors.toList());
         executeCreateInstance(deviceTask, waitCreateTask);
 
         List<DeviceInstanceTask> creatingTasks = deviceInstanceTasks.stream()
                 .filter(deviceInstanceTask -> EXECUTING.getStatus().equals(deviceInstanceTask.getStatus()))
                 .collect(Collectors.toList());
         verifyCreateInstanceResult(deviceTask, creatingTasks);
     }
 
     private void executeCreateInstance(DeviceTask deviceTask, List<DeviceInstanceTask> creatingTasks) {
         if (CollectionUtils.isEmpty(creatingTasks)) {
             return;
         }
 
         creatingTasks.forEach(deviceInstanceTask -> deviceVirtualizeInstanceTaskExecuteHandler.start(deviceTask));
     }
 
     private void verifyCreateInstanceResult(DeviceTask deviceTask, List<DeviceInstanceTask> waitCreateTask) {
         if (CollectionUtils.isEmpty(waitCreateTask)) {
             return;
         }
 
         List<DeviceInstanceTask> failTasks = new ArrayList<>();
 
         Map<String, InstanceInfoResponse> padCodeInfoMap;
         try {
             padCodeInfoMap = instanceManager.listAll(deviceTask.getIp()).stream()
                     .collect(Collectors.toMap(InstanceInfoResponse::getName, obj -> obj, (o1, o2) -> o1));
         } catch (Exception e) {
             log.error("listAll error>>>", e);
             return;
         }
 
         long deviceTaskId = deviceTask.getId();
         waitCreateTask.forEach(deviceInstanceTask -> {
             String instanceName = deviceInstanceTask.getInstanceName();
             InstanceInfoResponse instanceInfoResponse = padCodeInfoMap.get(instanceName);
             if (Objects.isNull(instanceInfoResponse)) {
                 return;
             }
 
             long subTaskId = deviceInstanceTask.getId();
             String exId = instanceInfoResponse.getExtId();
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper
                     .getByMasterTaskIdAndSubTaskId(deviceTask.getType(), deviceInstanceTask.getMasterTaskId(), subTaskId);
             if (taskRelInstanceDetail.getIdentificationCode().equals(exId)) {
                 super.updateStatus(deviceInstanceTask, SUCCESS, null, EXECUTING.getStatus());
                 String progressDescription = "\n" + LocalDateTime.now() + " 实例[" + instanceName + "]创建成功";
                 cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, progressDescription);
             } else {
                 failTasks.add(deviceInstanceTask);
                 super.updateStatus(deviceInstanceTask, EXCEPTION, null, EXECUTING.getStatus());
                 String progressDescription = "\n" + LocalDateTime.now() + " 实例[" + instanceName + "]创建失败：存在实例[" + exId + "]";
                 cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, progressDescription);
             }
         });
 
 
         List<DeviceInstanceTask> deviceInstanceTasks = deviceInstanceTaskMapper.listByMasterTaskId(deviceTaskId);
         long successSize = deviceInstanceTasks.stream()
                 .filter(deviceInstanceTask -> Objects.equals(deviceInstanceTask.getStatus(), SUCCESS.getStatus())).count();
         if (successSize == deviceInstanceTasks.size()) {
             super.updateStatus(deviceTask, SUCCESS, null, EXECUTING.getStatus());
             String progressDescription = "\n" + LocalDateTime.now() + " 任务完成";
             cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, progressDescription);
             return;
         }
 
         if (CollectionUtils.isNotEmpty(failTasks)) {
             String errorMsg = "pad exist but task id mismatch pods:" + JSON.toJSONString(failTasks);
             super.updateStatus(deviceTask, FAIL_ALL, errorMsg, null);
         }
 
     }
 
     private boolean initializationFinished(DeviceTask deviceTask) {
         long deviceTaskId = deviceTask.getId();
         String deviceIp = deviceTask.getIp();
         if (Boolean.FALSE.equals(deviceTask.getDiskDone())) {
             return false;
         }
 
         if (Boolean.FALSE.equals(deviceTask.getNetworkDone())) {
             return false;
         }
 
         if (Boolean.FALSE.equals(deviceTask.getImageDone())) {
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getLatestByMasterTaskId(deviceTask.getType(), deviceTaskId);
             HarborConfig harborConfig = harborConfigManage.get();
             String imageRepository = harborConfig.getUrl() + "/" + harborConfig.getProject() + "/" + taskRelInstanceDetail.getImageId();
             String imageTag = taskRelInstanceDetail.getImageTag();
             int status = imageManager.getPullStatus(deviceIp, imageRepository, imageTag);
 
             if (ContainerImageStatusEnum.SUCCESS.getCode() == status) {
                 cmsDeviceTaskMapper.updateImageDone(deviceTaskId, true);
                 String progressDescription = "\n" + LocalDateTime.now() + imageRepository + " " + imageTag + " 镜像下载成功";
                 cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, progressDescription);
                 return true;
             }
 
             if (ContainerImageStatusEnum.FAIL.getCode() == status) {
                 String progressDescription = "\n" + LocalDateTime.now() + " " + imageRepository + " " + imageTag + " 重试拉取镜像 ";
                 cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, progressDescription);
                 imageManager.pull(deviceIp, imageRepository, imageTag, true);
                 return false;
             }
 
             return true;
         }
 
         return true;
     }
 
     public DeviceVirtualizeTaskResultHandler(CmsTaskManager cmsTaskManager, InstanceManager instanceManager,
                                              RedissonDistributedLock redissonDistributedLock,
                                              TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                                              DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                              DefaultRocketMqProducerWrapper rocketMqProducerService,
                                              DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                              DeviceVirtualizeInstanceTaskExecuteHandler deviceVirtualizeInstanceTaskExecuteHandler,
                                              ImageManager imageManager, HarborConfigManage harborConfigManage,
                                              InstanceDetailImageSuccService instanceDetailImageSuccService) {
         super(cmsTaskManager, deviceInstanceTaskMapper, rocketMqProducerService, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceManager = instanceManager;
         this.redissonDistributedLock = redissonDistributedLock;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
         this.deviceInstanceTaskMapper = deviceInstanceTaskMapper;
         this.deviceVirtualizeInstanceTaskExecuteHandler = deviceVirtualizeInstanceTaskExecuteHandler;
         this.imageManager = imageManager;
         this.harborConfigManage = harborConfigManage;
     }
 }