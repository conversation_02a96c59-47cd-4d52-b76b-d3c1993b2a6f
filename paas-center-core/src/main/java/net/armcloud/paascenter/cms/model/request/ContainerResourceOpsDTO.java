package net.armcloud.paascenter.cms.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.cms.model.BaseLvmDiskOpsParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/29 16:53
 * @Description:
 */
@Data
public class ContainerResourceOpsDTO {
    @ApiModelProperty("容器名字(用于后续容器操作标识)")
    private String name;
    @ApiModelProperty("是否异步执行任务")
    private Boolean async = false;

    @ApiModelProperty("cpu核心限制数量,1000代表1核心,2000代表2核心")
    private Long cpuLimit;

    @ApiModelProperty("运行内存限制大小,单位MB,比如1024代表1024MG")
    private Long memoryLimit;

    @ApiModelProperty("存储隔离,单位:GB")
    private Long storageLimit;

    @ApiModelProperty("容器网络ip")
    private String ip;

    @ApiModelProperty("容器网络mac")
    private String mac;

    @ApiModelProperty("安卓dns1")
    private String dns1;

    @ApiModelProperty("安卓dns2")
    private String dns2;

    @ApiModelProperty("系统主机名称")
    private String hostname;

    @ApiModelProperty("镜像名称,镜像仓库")
    private String imageRepository;

    @ApiModelProperty("镜像标识")
    private String imageTag;

    @ApiModelProperty("磁盘是否隔离和限制(使用独立的磁盘)(默认true)")
    private Boolean isolateDisk = true;

    @ApiModelProperty("扩展标识id")
    private String extId;

    @ApiModelProperty("磁盘隔离参数")
    private BaseLvmDiskOpsParam diskOpsParam;

    @ApiModelProperty("容器运行逻辑编号,对应第几开(eg:1,2,...8)")
    private Integer containerIndex;

    @ApiModelProperty("macvlan名字")
    private String macvlanName="";

    @ApiModelProperty( "宽度")
    private Integer width;

    @ApiModelProperty( "高度")
    private Integer height;

    @ApiModelProperty( "屏幕刷新率")
    private Integer fps;

    @ApiModelProperty("屏幕像素密度")
    private Integer dpi;

    @ApiModelProperty("GameServer的后端服务器访问地址")
    private String gameServerAddress;

    @ApiModelProperty("安卓设备改机属性")
    private Map<String, String> deviceAndroidProps;

    @ApiModelProperty("安卓证书数据")
    private String androidCertData;

    @ApiModelProperty("云真机模板信息,zip包下载地址")
    private String downloadUrlOfADI;

    @ApiModelProperty("云真机模板信息,zip包密码")
    private String passwordOfADI;

    @ApiModelProperty("网存Id")
    private String storageId;

    @ApiModelProperty("目标网存id")
    private String storageBasedOn;




    @ApiModelProperty("安卓是否清除数据")
    private Boolean clearContainerData = true;


    /**
     * 因需兼容历史数据添加此选项。待统一后，后续统一使用v2版本创建
     */
    private String createVersion;

    /**
     * 网络相关信息
     */
    private ContainerNetworkDTO containerNetwork;
}
