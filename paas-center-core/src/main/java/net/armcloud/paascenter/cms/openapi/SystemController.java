 package net.armcloud.paascenter.cms.openapi;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.facade.SystemFacade;
 import net.armcloud.paascenter.cms.model.request.ProxyDetectionRequest;
 import net.armcloud.paascenter.cms.model.request.SystemCmdRequest;
 import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.utils.CmdUtil;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Value;
 import org.springframework.validation.annotation.Validated;
 import org.springframework.web.bind.annotation.RequestBody;
 import org.springframework.web.bind.annotation.RestController;
 
 @Slf4j
 @RestController
 public class SystemController implements SystemFacade {
     private final SystemManager systemManager;
 
     @Value("${proxyDetectionDomain:https://openapi-hk.armcloud.net/openapi/open/network/proxy/getMyIp}")
     private String proxyDetectionDomain;
 
     @Override
     public Result<String> execSyncCmd(@Validated @RequestBody SystemCmdRequest req) {
         return Result.ok(systemManager.execSyncCmd(req.getDeviceIp(), req.getCmd()));
     }
 
     @Override
     public Result<ProxyDetectionResponse> proxyDetection(@Validated @RequestBody ProxyDetectionRequest req) {
         boolean isS5 = req.getProxyType()==null || req.getProxyType() == 1;
         ProxyDetectionResponse proxyDetectionResponse = CmdUtil.proxyDetection(proxyDetectionDomain,req.getProxyHost(),req.getProxyPort(),req.getProxyAccount(),req.getProxyPwd(),isS5);
         return Result.ok(proxyDetectionResponse);
     }
 
     public SystemController(SystemManager systemManager) {
         this.systemManager = systemManager;
     }
 }