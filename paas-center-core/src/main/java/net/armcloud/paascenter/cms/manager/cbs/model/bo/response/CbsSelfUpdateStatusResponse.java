 package net.armcloud.paascenter.cms.manager.cbs.model.bo.response;
 
 import lombok.Data;
 
 /**
  * cbs自更新状态响应
  * status = 1时，cbs肯定是升级中
  * status = 0时，version和当请求参数的版本一致 则升级成功 （这一条可忽略相同版本的升级 因为就算没成功也不影响当前的cbs）
  * status = 0时，判断lastFailedReason为空则更新成功，不为空则更新失败
  * 如果请求不通 则不处理
  */
 @Data
 public class CbsSelfUpdateStatusResponse {
     /**状态 0正常 1升级中*/
     private Integer status;
     /**当前cbs版本*/
     private String version;
     /**异常信息*/
     private String lastFailedReason;
 }