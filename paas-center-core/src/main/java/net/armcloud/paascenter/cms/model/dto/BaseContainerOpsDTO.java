 package net.armcloud.paascenter.cms.model.dto;
 
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 import java.util.Map;
 
 /**
  * cbs重置请求参数
  */
 @Data
 public class BaseContainerOpsDTO {
     @ApiModelProperty("容器名字(用于后续容器操作标识)")
     private String name;
     @ApiModelProperty("是否异步执行任务")
     private Boolean async = false;
 
     /**
      * 设置安卓属性
      */
     private Map<String,String> deviceAndroidProps;
 }