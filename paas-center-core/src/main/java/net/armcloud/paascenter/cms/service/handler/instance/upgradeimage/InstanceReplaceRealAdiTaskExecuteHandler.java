 package net.armcloud.paascenter.cms.service.handler.instance.upgradeimage;
 
 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSONObject;
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.model.request.InstanceReplaceRealAdiTemplateRequest;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.CBSHarborManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Objects;
 import java.util.concurrent.RejectedExecutionException;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.exception.code.InstanceExceptionCode.NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION;
 
 /**
  * 升级真机adi模板
  */
 @Slf4j
 @Service
 public class InstanceReplaceRealAdiTaskExecuteHandler extends AbstractInstanceTaskHandler {
     private final CBSHarborManager cbsHarborManager;
     private final InstanceManager cbsInstanceManager;
     private final HarborConfigManage harborConfigManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     private final net.armcloud.paascenter.cms.manager.InstanceManager instanceManager;
 
     public void start(InstanceTask instanceTask) {
         try {
             super.asyncExecute(() -> tryStart(instanceTask));
         } catch (RejectedExecutionException e) {
             log.error("线程池:InstanceTaskPool,队列已满，存在任务丢失", e);
             DingTalkRobotClient.sendMessage("CMS服务-线程池:InstanceTaskPool,队列已满，存在任务丢失");
         }
     }
 
     private void tryStart(InstanceTask instanceTask) {
         String instanceTaskJson = JSON.toJSONString(instanceTask);
         log.info(" InstanceReplaceRealAdiTaskExecuteHandler  executeTask start handler instanceTask:{}", instanceTaskJson);
         String key = EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX + instanceTask.getInstanceName();
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (lock == null) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, key);
             return;
         }
 
         RLock deviceTaskLock = null;
         try {
             // 云机任务与实例任务互斥，要执行任务则需要拿到板卡锁
             String deviceIp = instanceTask.getDeviceIp();
             String deviceTaskLockKey = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
             if (redissonDistributedLock.isHeldByCurrentThread(deviceTaskLockKey)) {
                 log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
                 return;
             }
 
             deviceTaskLock = redissonDistributedLock.tryLock(key, 0, 300);
             if (Objects.isNull(deviceTaskLock)) {
                 log.debug("thread {} not get lock {} skip....", currentThreadId, key);
                 return;
             }
 
             boolean success = super.updateStatus(instanceTask, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
 
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getLatestByMasterTaskId(instanceTask.getType(), instanceTask.getId());
             if (taskRelInstanceDetail == null) {
                 throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
             }
 
             TaskRelInstanceDetail createTaskRelPadDetail = getCreateTaskRelPadDetail(instanceTask.getInstanceName());
             if (createTaskRelPadDetail == null) {
                 throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
             }
 
             removeInstance(instanceTask);
             virtualizeNewInstance(instanceTask, createTaskRelPadDetail, taskRelInstanceDetail);
         } catch (Exception e) {
             log.error("InstanceUpgradeImageTaskHandler executeTask error>>>>{}", JSON.toJSONString(instanceTask), e);
             super.updateStatus(instanceTask, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         } finally {
             redissonDistributedLock.unlock(lock);
             if (deviceTaskLock != null) {
                 redissonDistributedLock.unlock(deviceTaskLock);
             }
         }
     }
 
 
 
     private void virtualizeNewInstance(InstanceTask task, TaskRelInstanceDetail originCreateDetail, TaskRelInstanceDetail newCreateDetail) {
         cbsHarborManager.loginHarbor(task.getDeviceIp());
         String instanceName = task.getInstanceName();
         log.info("InstanceReplaceRealAdiTaskExecuteHandler instanceName {} >>> virtualizeNewInstance task:{} originCreateDetail:{} newCreateDetail:{}",
                 instanceName, JSON.toJSONString(task), JSON.toJSONString(originCreateDetail), JSON.toJSONString(newCreateDetail));
 
         CreatePadBO createPadBO = JSON.parseObject(originCreateDetail.getContainerProperty(), CreatePadBO.class);
         HarborConfig harborConfig = harborConfigManage.get();
         if(StringUtils.isNotEmpty(newCreateDetail.getImageId())){
             String imageRepository = harborConfig.getUrl() + "/" + harborConfig.getProject() + "/" + originCreateDetail.getImageId();
             createPadBO.setImageRepository(imageRepository);
         }
         //是否有下发adi模板
         Boolean newCreateAdiIsEmpty =false;
         //获取adi数据
         //判断空对象以及空字符串
         if(StringUtils.isNotEmpty(newCreateDetail.getAdiJson())&& !Objects.equals(newCreateDetail.getAdiJson(),"{}")){
             newCreateAdiIsEmpty=true;
             InstanceReplaceRealAdiTemplateRequest.ADI adi = JSONUtil.toBean(newCreateDetail.getAdiJson(), InstanceReplaceRealAdiTemplateRequest.ADI.class);
             createPadBO.setDownloadUrlOfADI(adi.getTemplateUrl());
             createPadBO.setPasswordOfADI(adi.getTemplatePassword());
             if(adi.getLayoutWidth() != null){
                 createPadBO.setWidth(adi.getLayoutWidth().intValue());
                 createPadBO.setHeight(adi.getLayoutHigh().intValue());
                 createPadBO.setDpi(adi.getLayoutDpi().intValue());
                 createPadBO.setFps(adi.getLayoutFps().intValue());
             }
         }else if(originCreateDetail.getWidth() != null && originCreateDetail.getWidth() > 0){
             createPadBO.setDownloadUrlOfADI(originCreateDetail.getAdiJson());
             createPadBO.setWidth(originCreateDetail.getWidth());
             createPadBO.setHeight(originCreateDetail.getHeight());
             createPadBO.setDpi(originCreateDetail.getDpi());
             createPadBO.setFps(originCreateDetail.getFps());
         }
 
         log.info("virtualizeNewInstance_originCreateDetail:{},newCreateAdiIsEmpty:{}", JSONObject.toJSONString(originCreateDetail.getAdiJson()),newCreateAdiIsEmpty);
         if(StringUtils.isNotEmpty(originCreateDetail.getAdiJson()) &&!newCreateAdiIsEmpty){
             InstanceReplaceRealAdiTemplateRequest.ADI adi = JSONUtil.toBean(originCreateDetail.getAdiJson(), InstanceReplaceRealAdiTemplateRequest.ADI.class);
             createPadBO.setDownloadUrlOfADI(adi.getTemplateUrl());
             createPadBO.setPasswordOfADI(adi.getTemplatePassword());
         }
         createPadBO.setImageTag(originCreateDetail.getImageTag());
 
         createPadBO.setExtId(originCreateDetail.getIdentificationCode());
         createPadBO.setClearContainerData(Boolean.TRUE.equals(task.getClearDiskData()));
         if (StringUtils.isBlank(createPadBO.getMac())) {
             createPadBO.setMac(newCreateDetail.getMac());
         }
 
 
         instanceManager.setDefaultDNS(createPadBO);
         String requestDataJson = JSON.toJSONString(createPadBO);
         taskRelInstanceDetailMapper.updateContainerProperty(newCreateDetail.getId(), requestDataJson);
         cbsInstanceManager.create(createPadBO);
     }
 
     private void removeInstance(InstanceTask task) {
         String deviceIp = task.getDeviceIp();
         String instanceName = task.getInstanceName();
 
         log.debug("InstanceReplaceRealAdiTaskExecuteHandler removeInstance deviceIp:{} instanceName:{}", deviceIp, instanceName);
         try {
             boolean instanceExist = cbsInstanceManager.listAll(deviceIp).stream()
                     .anyMatch(instance -> instanceName.equals(instance.getName()));
             if (!instanceExist) {
                 log.debug("InstanceReplaceRealAdiTaskExecuteHandler removeInstance deviceIp:{} instanceName:{} not found skip remove", deviceIp, instanceName);
                 return;
             }
 
             cbsInstanceManager.remove(deviceIp, instanceName, task.getClearDiskData());
         } catch (Exception e) {
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         }
     }
 
     protected InstanceReplaceRealAdiTaskExecuteHandler(CmsTaskManager cmsTaskManager,
                                                        RedissonDistributedLock redissonDistributedLock,
                                                        InstanceManager cbsInstanceManager,
                                                        InstanceTaskMapper instanceTaskMapper,
                                                        InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                        DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                        TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                                                        HarborConfigManage harborConfigManage,
                                                        CBSHarborManager cbsHarborManager,
                                                        net.armcloud.paascenter.cms.manager.InstanceManager instanceManager,
                                                        InstanceDetailImageSuccService instanceDetailImageSuccService) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.cbsInstanceManager = cbsInstanceManager;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.harborConfigManage = harborConfigManage;
         this.cbsHarborManager = cbsHarborManager;
         this.instanceManager = instanceManager;
     }
 }