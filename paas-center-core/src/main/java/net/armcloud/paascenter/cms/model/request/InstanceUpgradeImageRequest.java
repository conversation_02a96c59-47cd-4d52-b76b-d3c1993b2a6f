 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class InstanceUpgradeImageRequest {
     @Valid
     @NotNull(message = "instances cannot null")
     @Size(min = 1, message = "instances cannot null")
     private List<Instance> instances;
 
     @Data
     public static class Instance{
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         @NotBlank(message = "padCode cannot null")
         private String padCode;
 
         @Valid
         @NotNull(message = "image cannot null")
         private ImageRequest image;
 
         @NotNull(message = "clearDiskData cannot null")
         private Boolean clearDiskData;
 
         private String mac;

         private VirtualizeDeviceRequest.Device.Pad oldParam;
     }
 }