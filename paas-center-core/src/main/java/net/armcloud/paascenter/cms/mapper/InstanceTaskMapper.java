 package net.armcloud.paascenter.cms.mapper;
 
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.Collection;
 import java.util.Date;
 import java.util.List;
 import java.util.Set;
 
 @Mapper
 public interface InstanceTaskMapper {
     int insert(InstanceTask record);
 
     List<String> listDeviceIpWithRunningTask(@Param("dcId") long dcId);
 
     List<InstanceTask> listWaitRunTasks(@Param("dcId") long dcId, @Param("excludeInstanceNames") Collection<String> excludeInstanceNames,
                                         @Param("excludeDeviceIps") Collection<String> excludeDeviceIps);
 
     List<Long> getIdsByInstanceNameAndDeviceIp(@Param("dcId") long dcId, @Param("excludeInstanceNames") Collection<String> excludeInstanceNames,
                                         @Param("excludeDeviceIps") Collection<String> excludeDeviceIps);
     List<InstanceTask> listWaitRunTasksNew(@Param("minIds") List<Long> minIds);
 
     void batchUpdateStatusById(@Param("ids") List<Long> ids, @Param("status") int status, @Param("startTime") Date startTime,
                                @Param("endTime") Date endTime, @Param("timeoutTime") Date timeoutTime, @Param("msg") String msg);
 
     List<InstanceTask> listByTaskId(@Param("id") long id);
 
     List<InstanceTask> listByStatusAndTargetTaskTypes(@Param("dcId") long dcId, @Param("status") int status,
                                                       @Param("typeValues") List<Integer> typeValues);
 
     List<InstanceTask> listByMasterTaskId(@Param("masterTaskId") long masterTaskId);
 
     void updateByMasterTaskId(@Param("masterTaskId") long masterTaskId, @Param("status") int status,
                               @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                               @Param("timeoutTime") Date timeoutTime, @Param("msg") String msg);
 
     List<InstanceTask> listById(@Param("ids") List<Long> ids);
 
     List<InstanceTask> listTimeout(@Param("dcId") long dcId);
 
     InstanceTask getById(@Param("id") long id);
 
     int updateStatusById(@Param("id") long id, @Param("status") int status, @Param("msg") String msg,
                          @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                          @Param("timeoutTime") Date timeoutTime, @Param("originStatus") Integer originStatus);
 
     Set<String> listInstanceNameByDcIdAndStatus(@Param("dcId") long dcId, @Param("status") int status);
 
     Set<String> listDeviceIpByDcIdAndStatus(@Param("dcId") long dcId, @Param("statusList") List<Integer> statusList);

     /**
      * 查询某个时间段之前的任务，只返回任务ID
      * @param date
      * @return
      */
     List<Long> selectIdByCreateTime(@Param("date") String date);

     /**
      * 根据ID批量删除任务
      * @param ids
      * @return
      */
     int delByIds(@Param("ids") List<Long> ids);
 }