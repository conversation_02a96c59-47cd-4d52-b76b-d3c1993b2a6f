 package net.armcloud.paascenter.cms.service;
 
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.nacos.common.utils.CollectionUtils;
 import com.google.common.collect.Lists;
 import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.cms.model.request.*;
 import net.armcloud.paascenter.cms.model.response.*;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.InstanceBackupDataProgressMapper;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.model.bo.TaskRelInstanceDetailOtherParamBO;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import net.armcloud.paascenter.cms.utils.IdentificationCodeUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 
 import java.io.File;
 import java.util.ArrayList;
 import java.util.List;
 import java.util.Objects;
 import java.util.function.Consumer;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.*;
 
 
 @Slf4j
 @Service
 public class InstanceOpService {
     private final InstanceTaskMapper instanceTaskMapper;
 
     private final InstanceManager instanceManager;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     private final InstanceBackupDataProgressMapper instanceBackupDataProgressMapper;
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceRestartResponse> addRestartTask(InstanceRestartRequest requests) {
         List<InstanceRestartRequest.Instance> instances = requests.getInstances();
         List<InstanceRestartResponse> result = new ArrayList<>(instances.size());
 
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode, INSTANCE_RESTART);
 
             InstanceRestartResponse restartResponse = new InstanceRestartResponse();
             restartResponse.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             restartResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             restartResponse.setPadCode(padCode);
             result.add(restartResponse);
         });
 
         return result;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceResetResponse> addResetTask(InstanceResetRequest request) {
         List<InstanceResetRequest.Instance> instances = request.getInstances();
         List<InstanceResetResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode, INSTANCE_RESET);
 
             InstanceResetResponse restartResponse = new InstanceResetResponse();
             restartResponse.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             restartResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             restartResponse.setPadCode(padCode);
             result.add(restartResponse);
         });
 
         return result;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceUpgradeImageResponse> addUpgradeImage(InstanceUpgradeImageRequest request) {
         List<InstanceUpgradeImageRequest.Instance> instances = request.getInstances();
         List<InstanceUpgradeImageResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
 
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = obj -> {
                 ImageRequest image = instance.getImage();
                 obj.setImageId(image.getId());
                 obj.setImageTag(image.getTag());
                 obj.setMac(instance.getMac());
             };
 
             Consumer<InstanceTask> instanceTaskConsumer = obj -> obj.setClearDiskData(instance.getClearDiskData());
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode, INSTANCE_UPGRADE_IMAGE,
                     instanceTaskConsumer, taskRelInstanceDetailConsumer);
 
             InstanceUpgradeImageResponse response = new InstanceUpgradeImageResponse();
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setPadCode(padCode);
             result.add(response);
         });
 
         return result;
     }
 
     private TaskRelInstanceDetail addOperateInstanceTaskDetail(String deviceIp, String instanceName, TaskTypeEnum taskTypeEnum) {
         return addOperateInstanceTaskDetail(deviceIp, instanceName, taskTypeEnum, null, null);
     }
 
     private TaskRelInstanceDetail addOperateInstanceTaskDetail(String deviceIp, String instanceName,
                                                                TaskTypeEnum taskTypeEnum, Consumer<InstanceTask> instanceTaskConsumer,
                                                                Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer) {
         InstanceTask instanceTask = new InstanceTask();
         instanceTask.setDcId(DCUtils.getDcId());
         instanceTask.setType(taskTypeEnum.getIntValue());
         instanceTask.setDeviceIp(deviceIp);
         instanceTask.setInstanceName(instanceName);
         instanceTask.setStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
         if (instanceTaskConsumer != null) {
             instanceTaskConsumer.accept(instanceTask);
         }
 
         instanceTaskMapper.insert(instanceTask);
 
         long taskId = instanceTask.getId();
         TaskRelInstanceDetail taskRelPadDetail = new TaskRelInstanceDetail();
         taskRelPadDetail.setTaskType(instanceTask.getType());
         taskRelPadDetail.setMasterTaskId(instanceTask.getId());
         taskRelPadDetail.setSubTaskId(instanceTask.getId());
         taskRelPadDetail.setInstanceName(instanceName);
         taskRelPadDetail.setIdentificationCode(IdentificationCodeUtils.genInstIdCode(taskId, taskId, instanceName));
         if (taskRelInstanceDetailConsumer != null) {
             taskRelInstanceDetailConsumer.accept(taskRelPadDetail);
         }
         taskRelInstanceDetailMapper.insert(taskRelPadDetail);
         return taskRelPadDetail;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceNetworkLimitResponse> addNetworkLimitTask(InstanceNetworkLimitRequest request) {
         List<InstanceNetworkLimitRequest.Instance> instances = request.getInstances();
         List<InstanceNetworkLimitResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = taskRelInstanceDetail -> {
                 taskRelInstanceDetail.setMaxUplinkBandwidth(instance.getMaxUplinkBandwidth());
                 taskRelInstanceDetail.setMaxDownlinkBandwidth(instance.getMaxDownlinkBandwidth());
             };
 
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode,
                     INSTANCE_NETWORK_LIMIT, null, taskRelInstanceDetailConsumer);
             InstanceNetworkLimitResponse networkLimitResponse = new InstanceNetworkLimitResponse();
             networkLimitResponse.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             networkLimitResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             networkLimitResponse.setPadCode(padCode);
             result.add(networkLimitResponse);
         });
 
         return result;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceUpdatePropResponse> updateProp(InstanceUpdatePropRequest request) {
         List<InstanceUpdatePropRequest.Instance> instances = request.getInstances();
         List<InstanceUpdatePropResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String padCode = instance.getPadCode();
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = taskRelInstanceDetail -> {
                 taskRelInstanceDetail.setDeviceAndroidProp(JSON.toJSONString(instance.getProps()));
 
                 TaskRelInstanceDetailOtherParamBO otherParamBO = new TaskRelInstanceDetailOtherParamBO();
                 otherParamBO.setRestart(request.getRestart());
                 taskRelInstanceDetail.setOtherParamJson(JSON.toJSONString(otherParamBO));
             };
 
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(instance.getDeviceIp(), padCode,
                     INSTANCE_SET_ANDROID_PROP, null, taskRelInstanceDetailConsumer);
             InstanceUpdatePropResponse response = new InstanceUpdatePropResponse();
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setPadCode(padCode);
             result.add(response);
         });
 
         return result;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceBackupDataResponse> backupData(InstanceBackupDataRequest req) {
         List<InstanceBackupDataResponse> responses = new ArrayList<>(req.getInstances().size());
         List<InstanceBackupDataProgress> progressList = new ArrayList<>(req.getInstances().size());
 
         req.getInstances().forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
             String backupPath = "/instance-backup/" + padCode + File.separator + System.currentTimeMillis();
 
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode,
                     INSTANCE_BACKUP_DATA, null, null);
             InstanceBackupDataResponse response = new InstanceBackupDataResponse();
             response.setPadCode(padCode);
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setStorageFilePath(backupPath);
             responses.add(response);
 
             InstanceBackupDataProgress dataProgress = new InstanceBackupDataProgress();
             dataProgress.setInstanceTaskId(taskRelPadDetail.getSubTaskId());
             dataProgress.setInstanceName(padCode);
             dataProgress.setUploadDone(false);
             dataProgress.setPackageDone(false);
             dataProgress.setPath(backupPath);
             progressList.add(dataProgress);
         });
 
         instanceBackupDataProgressMapper.batchInsert(progressList);
         return responses;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceRestoreBackupDataResponse> restoreBackupData(InstanceRestoreBackupDataRequest req) {
         List<InstanceRestoreBackupDataResponse> responses = new ArrayList<>(req.getInstances().size());
         List<InstanceBackupDataProgress> progressList = new ArrayList<>(req.getInstances().size());
 
         req.getInstances().forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode,
                     INSTANCE_RESTORE_BACKUP_DATA, null, null);
             InstanceRestoreBackupDataResponse response = new InstanceRestoreBackupDataResponse();
             response.setPadCode(padCode);
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             responses.add(response);
 
             InstanceBackupDataProgress dataProgress = new InstanceBackupDataProgress();
             dataProgress.setInstanceTaskId(taskRelPadDetail.getSubTaskId());
             dataProgress.setInstanceName(padCode);
             dataProgress.setPath(instance.getRestoreFilePath());
             dataProgress.setUploadDone(false);
             dataProgress.setPackageDone(false);
             progressList.add(dataProgress);
         });
 
         instanceBackupDataProgressMapper.batchInsert(progressList);
         return responses;
     }
 
     public InstanceOpService(TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                              InstanceTaskMapper instanceTaskMapper,
                              InstanceManager instanceManager,
                              InstanceBackupDataProgressMapper instanceBackupDataProgressMapper) {
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.instanceTaskMapper = instanceTaskMapper;
         this.instanceBackupDataProgressMapper = instanceBackupDataProgressMapper;
         this.instanceManager = instanceManager;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceResetResponse> replaceProp(InstanceReplacePropRequest req) {
         InstanceReplacePropRequest.Instance instance =req.getInstances();
         List<InstanceResetResponse> result = new ArrayList<>(1);
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
             String deviceAndroidProps = instance.getDeviceAndroidProps();
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = taskRelInstanceDetail -> {
                 taskRelInstanceDetail.setDeviceAndroidProp(deviceAndroidProps);
                 taskRelInstanceDetail.setAdiJson(JSON.toJSONString(req.getInstances().getAdi()));
                 taskRelInstanceDetail.setMac(instance.getMac());
             };
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(instance.getDeviceIp(), padCode,
                     INSTANCE_REPLACE_PROP, null, taskRelInstanceDetailConsumer);
             InstanceResetResponse restartResponse = new InstanceResetResponse();
             restartResponse.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             restartResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             restartResponse.setPadCode(padCode);
             result.add(restartResponse);
         return result;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceUpgradeImageResponse> addVirtualRealSwitchUpgradeImage(InstanceVirtualRealSwitchRequest request) {
         List<InstanceVirtualRealSwitchRequest.Instance> instances = request.getInstances();
         List<InstanceUpgradeImageResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
 
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = obj -> {
                 ImageRequest image = instance.getImage();
                 obj.setImageId(image.getId());
                 obj.setImageTag(image.getTag());
                 obj.setMac(instance.getMac());
                 obj.setAdiJson(JSON.toJSONString(instance.getAdi()));
             };
 
             Consumer<InstanceTask> instanceTaskConsumer = obj -> obj.setClearDiskData(instance.getClearDiskData());
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode, INSTANCE_UPGRADE_IMAGE,
                     instanceTaskConsumer, taskRelInstanceDetailConsumer);
 
             InstanceUpgradeImageResponse response = new InstanceUpgradeImageResponse();
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setPadCode(padCode);
             result.add(response);
         });
 
         return result;
     }
 
     public List<InstanceModifyPropertiesResponse> modifyProperties(InstanceModifyPropertiesRequest req) {
         List<InstanceModifyPropertiesRequest.Instance> instances = req.getInstances();
         List<InstanceModifyPropertiesResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String padCode = instance.getPadCode();
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = taskRelInstanceDetail -> {
                 if(Objects.nonNull(instance.getScreenWidth())){
                     taskRelInstanceDetail.setWidth(instance.getScreenWidth().intValue());
 
                 }
                 if(Objects.nonNull(instance.getScreenHigh())){
                     taskRelInstanceDetail.setHeight(instance.getScreenHigh().intValue());
                 }
                 if(Objects.nonNull(instance.getDpi())){
                     taskRelInstanceDetail.setDpi(instance.getDpi().intValue());
                 }
                 if(Objects.nonNull(instance.getFps())) {
                     taskRelInstanceDetail.setFps(instance.getFps().intValue());
                 }
                 if(Objects.nonNull(instance.getDeviceAndroidProps())){
                     taskRelInstanceDetail.setAndroidProp(instance.getDeviceAndroidProps());
                 }
                 if(Objects.nonNull(instance.getDns())){
                     taskRelInstanceDetail.setDns(instance.getDns());
                 }
             };
             TaskRelInstanceDetail detail = new TaskRelInstanceDetail();
             taskRelInstanceDetailConsumer.accept(detail);
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(instance.getDeviceIp(), padCode,
                     INSTANCE_MODIFY_PROPERTIES_PROP, null, taskRelInstanceDetailConsumer);
             InstanceModifyPropertiesResponse response = new InstanceModifyPropertiesResponse();
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setPadCode(padCode);
             result.add(response);
         });
         return result;
     }
 
     @Transactional(rollbackFor = Exception.class)
     public List<InstanceUpgradeImageResponse> addRealVirtualSwitchUpgradeImage(InstanceRealVirtualSwitchRequest request) {
         List<InstanceRealVirtualSwitchRequest.Instance> instances = request.getInstances();
         List<InstanceUpgradeImageResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
 
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = obj -> {
                 ImageRequest image = instance.getImage();
                 obj.setImageId(image.getId());
                 obj.setImageTag(image.getTag());
                 obj.setMac(instance.getMac());
                 if(instance.getLayoutWidth()!=null){
                     obj.setWidth(instance.getLayoutWidth().intValue());
                     obj.setHeight(instance.getLayoutHigh().intValue());
                     obj.setDpi(instance.getLayoutDpi().intValue());
                     obj.setFps(instance.getLayoutFps().intValue());
                 }
             };
 
             Consumer<InstanceTask> instanceTaskConsumer = obj -> obj.setClearDiskData(instance.getClearDiskData());
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode, INSTANCE_UPGRADE_IMAGE,
                     instanceTaskConsumer, taskRelInstanceDetailConsumer);
 
             InstanceUpgradeImageResponse response = new InstanceUpgradeImageResponse();
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setPadCode(padCode);
             result.add(response);
         });
 
         return result;
     }
 
     public List<InstanceLifecycleStatusResponse> instanceLifecycleStatus(InstanceLifecycleStatusRequest req) {
         List<InstanceLifecycleStatusResponse> list = Lists.newArrayList();
         List<InstanceLifecycleStatusRequest.Instance> instances = req.getInstances();
         instances.forEach(instance -> {
             try{
                 List<InstanceInfoResponse> instanceInfoResponses = instanceManager.listAll(instance.getDeviceIp());
                 if(CollectionUtils.isNotEmpty(instanceInfoResponses)) {
                     instanceInfoResponses.forEach(rsp->{
                         InstanceLifecycleStatusResponse response = new InstanceLifecycleStatusResponse();
                         response.setDeviceIp(instance.getDeviceIp());
                         response.setVersion(rsp.getMountVersion());
                         response.setName(rsp.getName());
                         list.add(response);
                     });
                 }
             }catch (Exception e){
                 log.error("InstanceOpService.instanceLifecycleStatus error! instance:{}", JSON.toJSONString(instance), e);
             }
 
         });
         return list;
     }
 
     public List<InstanceUpgradeImageResponse> replaceRealAdiTemplate(InstanceReplaceRealAdiTemplateRequest request) {
         List<InstanceReplaceRealAdiTemplateRequest.Instance> instances = request.getInstances();
         List<InstanceUpgradeImageResponse> result = new ArrayList<>(instances.size());
         instances.forEach(instance -> {
             String deviceIp = instance.getDeviceIp();
             String padCode = instance.getPadCode();
 
             Consumer<TaskRelInstanceDetail> taskRelInstanceDetailConsumer = obj -> {
                 obj.setMac(instance.getMac());
                 obj.setAndroidProp(instance.getAndroidProp());
                 obj.setAdiJson(JSON.toJSONString(instance.getAdi()));
             };
 
             Consumer<InstanceTask> instanceTaskConsumer = obj -> obj.setClearDiskData(instance.getClearDiskData());
             TaskRelInstanceDetail taskRelPadDetail = addOperateInstanceTaskDetail(deviceIp, padCode, TaskTypeEnum.INSTANCE_REPLACE_REAL_ADB,
                     instanceTaskConsumer, taskRelInstanceDetailConsumer);
 
             InstanceUpgradeImageResponse response = new InstanceUpgradeImageResponse();
             response.setMasterTaskId(taskRelPadDetail.getMasterTaskId());
             response.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             response.setPadCode(padCode);
             result.add(response);
         });
 
         return result;
     }
 }