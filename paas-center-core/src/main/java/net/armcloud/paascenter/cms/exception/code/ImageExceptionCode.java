 package net.armcloud.paascenter.cms.exception.code;
 
 import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
 import lombok.AllArgsConstructor;
 import lombok.Getter;
 
 @Getter
 @AllArgsConstructor
 public enum ImageExceptionCode implements ExceptionCode {
     LOGIN_IMAGE_REPOSITORY_FAIL_EXCEPTION(173000, "登录镜像仓库失败"),
     TAG_IMAGE_FAIL_EXCEPTION(173001, "标记镜像失败"),
     PUSH_IMAGE_REPOSITORY_FAIL_EXCEPTION(173002, "推送镜像失败"),
     LOAD_LOCALHOST_IMAGE_FAIL_EXCEPTION(173003, "加载本地镜像失败"),
     DELETE_LOCALHOST_IMAGE_FAIL_EXCEPTION(173004, "删除本地镜像失败"),
     IMPORT_LOCALHOST_IMAGE_FAIL_EXCEPTION(173005, "导入本地镜像失败"),
     ;
 
     private final int status;
     private final String msg;
 }