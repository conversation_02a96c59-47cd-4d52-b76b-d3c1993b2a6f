 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.constraints.NotNull;
 
 @Data
 public class NetworkRequest {
     /**
      * 实例ip
      */
     @NotNull(message = "ip cannot null")
     private String ip;
 
     /**
      * 最大上行带宽（单位：Mbps）
      */
     private String maxUplinkBandwidth;
 
     /**
      * 最大下行带宽（单位：Mbps）
      */
     private String maxDownlinkBandwidth;
 
     /**
      * 子网
      */
     private String subnet;
 
     /**
      * IP范围
      */
     private String ipRange;
 
     /**
      * 网关
      */
     private String gateway;
 
     /**
      * 网络驱动名称
      */
     private String networkDeviceName;
 
     /**
      * dns配置
      */
     private String dns;
 
     /**
      * MAC地址
      */
     private String mac;
 }