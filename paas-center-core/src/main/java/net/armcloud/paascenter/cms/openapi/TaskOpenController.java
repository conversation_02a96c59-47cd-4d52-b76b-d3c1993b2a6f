 package net.armcloud.paascenter.cms.openapi;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.facade.TaskFacade;
 import net.armcloud.paascenter.cms.model.request.TaskDetailRequest;
 import net.armcloud.paascenter.cms.model.response.DeviceTaskDetailResponse;
 import net.armcloud.paascenter.cms.model.response.InstanceTaskDetailResponse;
 import net.armcloud.paascenter.cms.service.TaskOpenService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.web.bind.annotation.RequestBody;
 import org.springframework.web.bind.annotation.RestController;
 
 import javax.validation.Valid;
 
 @Slf4j
 @RestController
 public class TaskOpenController implements TaskFacade {
     private final TaskOpenService taskOpenService;
 
     @Override
     public Result<InstanceTaskDetailResponse> instanceDetail(@Valid @RequestBody TaskDetailRequest request) {
         return Result.ok(taskOpenService.instanceDetail(request));
     }
 
     @Override
     public Result<DeviceTaskDetailResponse> deviceDetail(@Valid @RequestBody TaskDetailRequest request) {
         return Result.ok(taskOpenService.deviceDetail(request));
     }
 
     public TaskOpenController(TaskOpenService taskOpenService) {
         this.taskOpenService = taskOpenService;
     }
 
 }