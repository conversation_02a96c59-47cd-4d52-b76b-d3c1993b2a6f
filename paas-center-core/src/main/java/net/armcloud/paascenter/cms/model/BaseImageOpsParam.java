 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 @ApiModel("容器镜像操作参数")
 @Data
 public class BaseImageOpsParam {
 
     @ApiModelProperty("镜像名称,镜像仓库")
     private String imageRepository;
 
     @ApiModelProperty("镜像标识")
     private String imageTag;
 
 
     @ApiModelProperty("任务是否异步执行")
     private Boolean async = true;
 
 }