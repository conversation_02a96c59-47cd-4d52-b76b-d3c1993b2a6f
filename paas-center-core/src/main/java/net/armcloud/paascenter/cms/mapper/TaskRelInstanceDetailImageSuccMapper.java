 package net.armcloud.paascenter.cms.mapper;
 
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.List;
 
 /**
  * <p>
  * 任务关联实例信息详情表(镜像相关执行成功记录 目前保留最近7次的操作记录) Mapper 接口
  * </p>
  *
  * <AUTHOR>
  * @since 2025-01-02
  */
 @Mapper
 public interface TaskRelInstanceDetailImageSuccMapper extends BaseMapper<TaskRelInstanceDetailImageSucc> {
 
     List<Long> getAssignDataIds(@Param("dcId")Long dcId, @Param("padCode") String padCode, @Param("taskType") Integer taskType, @Param("pageIndex") Integer pageIndex);
 }