 package net.armcloud.paascenter.cms.manager.cbs;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.cms.model.BaseLvmDiskOpsParam;
 import net.armcloud.paascenter.cms.model.SysCmdOpsVO;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.cms.manager.cbs.feign.DiskFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import feign.RetryableException;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Component;
 
 import java.net.URI;
 
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_CONNECT_EXCEPTION;
 import static net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils.builderHost;
 
 @Slf4j
 @Component
 public class DiskManager {
     private final DiskFeignClient diskFeignClient;
 
     /**
      * 磁盘分区
      */
     public void partition(String deviceIp, long hostStorageSize, long containerStorageSize, long containerSize, boolean isolateDisk) {
         URI deviceHost = builderHost(deviceIp);
         SysCmdOpsVO iOpsVo;
         try {
 
             BaseLvmDiskOpsParam baseLvmDiskOpsParam = new BaseLvmDiskOpsParam();
             baseLvmDiskOpsParam.setHostStorageSize(hostStorageSize);
             baseLvmDiskOpsParam.setContainerPerStorageSize(containerStorageSize);
             baseLvmDiskOpsParam.setContainerConcurrentSize(containerSize);
             baseLvmDiskOpsParam.setIsolateDisk(isolateDisk);
 
             log.debug("deviceIp:{} start partition >>> baseLvmDiskOpsParam:{}", deviceIp, JSON.toJSONString(baseLvmDiskOpsParam));
             iOpsVo = diskFeignClient.partition(deviceHost, baseLvmDiskOpsParam);
             log.debug("deviceIp:{} end partition iOpsVo:{}", deviceIp, iOpsVo);
             BackendFeignUtils.versify(iOpsVo);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
     }
 
 
     public DiskManager(DiskFeignClient diskFeignClient) {
         this.diskFeignClient = diskFeignClient;
     }
 }