 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.model.SysBuildInfo;
 import net.armcloud.paascenter.cms.model.SysCmdOpsVO;
 import net.armcloud.paascenter.cms.model.SysCmdParam;
 import net.armcloud.paascenter.cms.model.SysSelfUpdateDTO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.GetMapping;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-system", url = "placeholder")
 public interface SystemFeignClient{
 
     @PostMapping(value = "/sys/exec/cmd")
     SysCmdOpsVO execCmd(URI host, @RequestBody SysCmdParam dto);
 
     @GetMapping(value = "/actuator/health")
     void health(URI host);
 
     @PostMapping(value = "/sys/restartDocker")
     SysCmdOpsVO restartDocker(URI host);
 
     @PostMapping(value = "/sys/build/info")
     SysBuildInfo buildInfo(URI host);
 
     /**
      * cbs自更新
      * @param host
      * @param sysSelfUpdateDTO
      * @return
      */
     @PostMapping(value = "/sys/self/update")
     SysCmdOpsVO selfUpdate(URI host, @RequestBody SysSelfUpdateDTO sysSelfUpdateDTO);
 
     /**
      * 查询cbs更新状态
      * @param host
      * @return
      */
     @PostMapping(value = "/sys/self/update/status")
     SysCmdOpsVO selfUpdateStatus(URI host);
 }