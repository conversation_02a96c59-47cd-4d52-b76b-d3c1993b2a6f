 package net.armcloud.paascenter.cms.config;
 
 import lombok.Getter;
 import lombok.Setter;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.boot.context.properties.ConfigurationProperties;
 import org.springframework.stereotype.Component;
 
 @Getter
 @Setter
 @Slf4j
 @Component
 @ConfigurationProperties(prefix = "minio")
 public class MinIOConfig {
     private String endpoint;
     private String accessKey;
     private String secretKey;
     private String bucket;
 }