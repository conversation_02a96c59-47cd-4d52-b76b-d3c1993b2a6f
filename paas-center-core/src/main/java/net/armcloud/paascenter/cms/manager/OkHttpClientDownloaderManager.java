 package net.armcloud.paascenter.cms.manager;
 
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import lombok.extern.slf4j.Slf4j;
 import okhttp3.OkHttpClient;
 import okhttp3.Request;
 import okhttp3.Response;
 import okhttp3.ResponseBody;
 import org.springframework.stereotype.Service;
 
 import javax.net.ssl.SSLContext;
 import javax.net.ssl.SSLSocketFactory;
 import javax.net.ssl.TrustManager;
 import javax.net.ssl.X509TrustManager;
 import java.io.*;
 import java.security.cert.X509Certificate;
 import java.util.Objects;
 import java.util.concurrent.TimeUnit;
 
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.SYSTEM_EXCEPTION;
 
 @Slf4j
 @Service
 public class OkHttpClientDownloaderManager {
     private static final OkHttpClient HTTP_CLIENT;
 
     static {
         OkHttpClient.Builder builder = new OkHttpClient.Builder()
                 .readTimeout(20, TimeUnit.MINUTES);
         try {
             builder.sslSocketFactory(getUnsafeSSLSocketFactory(), getUnsafeTrustManager());
         } catch (Exception e) {
             log.error("builder OkHttpClient sslSocketFactory error>>>>", e);
             e.printStackTrace();
         }
 
         builder.hostnameVerifier((hostname, session) -> true);
         HTTP_CLIENT = builder.build();
     }
 
     public void download(String fileUrl, String saveFilePath) throws BasicException {
         log.info("okhttp start download {}", fileUrl);
         Response response = null;
         ResponseBody responseBody = null;
         InputStream inputStream = null;
         FileOutputStream outputStream = null;
         try {
             Request request = new Request.Builder()
                     .url(fileUrl)
                     .addHeader("Connection", "close")
                     .build();
 
             response = HTTP_CLIENT.newCall(request).execute();
             responseBody = response.body();
             if (responseBody == null) {
                 return;
             }
 
             inputStream = responseBody.byteStream();
             outputStream = new FileOutputStream(saveFilePath);
 
             byte[] buffer = new byte[1024];
             int bytesRead;
             while ((bytesRead = inputStream.read(buffer)) != -1) {
                 outputStream.write(buffer, 0, bytesRead);
             }
 
             outputStream.flush();
         } catch (InterruptedIOException e) {
             log.warn("download InterruptedIOException >>>>>fileUrl={},saveFilePath={}", fileUrl, saveFilePath, e);
             throw new BasicException(SYSTEM_EXCEPTION.getStatus(), "文件:" + fileUrl + "下载失败>" + e.getMessage());
         } catch (IOException e) {
             log.error("download error>>>>>fileUrl={},saveFilePath={}", fileUrl, saveFilePath, e);
             throw new BasicException(SYSTEM_EXCEPTION.getStatus(), "文件:" + fileUrl + "下载失败>" + e.getMessage());
         } catch (IllegalArgumentException e) {
             log.error("download error>>>>>fileUrl={},saveFilePath={}", fileUrl, saveFilePath, e);
             throw new BasicException(SYSTEM_EXCEPTION.getStatus(), "文件地址无效");
         } finally {
             if (Objects.nonNull(response)) {
                 response.close();
             }
 
             if (Objects.nonNull(responseBody)) {
                 responseBody.close();
             }
 
             if (Objects.nonNull(inputStream)) {
                 try {
                     inputStream.close();
                 } catch (IOException e) {
                     log.error("close inputStream error fileUrl:{} saveFilePath:{}", fileUrl, saveFilePath, e);
                 }
             }
 
             if (Objects.nonNull(outputStream)) {
                 try {
                     outputStream.close();
                 } catch (IOException e) {
                     log.error("close outputStream error fileUrl:{} saveFilePath:{}", fileUrl, saveFilePath, e);
                 }
             }
         }
 
         log.info("okhttp end download {} to saveFilePath:{} saveFilePath exists >>{}", fileUrl, saveFilePath, new File(saveFilePath).exists());
     }
 
     private static SSLSocketFactory getUnsafeSSLSocketFactory() throws Exception {
         TrustManager[] trustAllCerts = new TrustManager[]{
                 new X509TrustManager() {
                     public X509Certificate[] getAcceptedIssuers() {
                         return new X509Certificate[]{};
                     }
 
                     public void checkClientTrusted(X509Certificate[] chain, String authType) {
                     }
 
                     public void checkServerTrusted(X509Certificate[] chain, String authType) {
                     }
                 }
         };
 
         SSLContext sslContext = SSLContext.getInstance("TLS");
         sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
         return sslContext.getSocketFactory();
     }
 
     private static X509TrustManager getUnsafeTrustManager() {
         return new X509TrustManager() {
             public X509Certificate[] getAcceptedIssuers() {
                 return new X509Certificate[]{};
             }
 
             public void checkClientTrusted(X509Certificate[] chain, String authType) {
             }
 
             public void checkServerTrusted(X509Certificate[] chain, String authType) {
             }
         };
     }
 
 }