 package net.armcloud.paascenter.cms.manager.cbs.utils;
 
 import net.armcloud.paascenter.cms.enums.ContainerOpsStatusEnum;
 import net.armcloud.paascenter.cms.model.IBaseOpsVo;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import lombok.extern.slf4j.Slf4j;
 
 import java.net.URI;
 import java.net.URISyntaxException;
 import java.util.Objects;
 import java.util.Optional;
 
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 
 @Slf4j
 public class BackendFeignUtils {
     private BackendFeignUtils() {
     }
 
     public static void versify(IBaseOpsVo iOpsVo) {
         if (Objects.isNull(iOpsVo)) {
             log.error("BackendFeign result versify fail >>> iOpsVo is null");
             throw new BasicException(PROCESSING_FAILED.getStatus(), "板卡程序处理结果为空");
         }
 
         int code = Optional.ofNullable(iOpsVo.getCode()).orElse(ContainerOpsStatusEnum.FAIL.code);
         log.info("backend feign result>>>code:{} dataPayload:{} className:{}", code, iOpsVo.getDataPayload(), iOpsVo.getClass().getName());
         if (!ContainerOpsStatusEnum.SUCCESS.code.equals(code)) {
             log.error("BackendFeign result versify fail >>> status:{}", code);
             String error = "板卡程序处理失败：" + iOpsVo.getMsg();
             throw new BasicException(PROCESSING_FAILED.getStatus(), error);
         }
     }
 
     public static URI builderHost(String deviceIp) {
         int port = 18182;
         String host = "http://" + deviceIp + ":" + port;
         try {
             return new URI(host);
         } catch (URISyntaxException e) {
             log.error("builderHost error>>>deviceIp:{}", deviceIp, e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), "板卡程序地址构建失败");
         }
     }
 }