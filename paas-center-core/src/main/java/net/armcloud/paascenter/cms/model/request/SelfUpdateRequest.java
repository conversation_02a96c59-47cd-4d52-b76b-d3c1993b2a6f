 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 /**
  * cbs自更新请求参数
  */
 @Data
 public class SelfUpdateRequest {
 
     /**板卡ip列表*/
     @NotNull(message = "deviceIps cannot null")
     @Size(min = 1, message = "deviceIps cannot null")
     private List<String> deviceIps;
     /**cbs下载地址*/
     @NotBlank(message = "cbsFileUrl cannot null")
     private String cbsFileUrl;
     /**cbs版本号*/
     @NotBlank(message = "cbsVersion cannot null")
     private String cbsVersion;
 
 }