 package net.armcloud.paascenter.cms.openapi;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.facade.DeviceFacade;
 import net.armcloud.paascenter.cms.model.request.DeviceIpsRequest;
 import net.armcloud.paascenter.cms.model.request.SelfUpdateRequest;
 import net.armcloud.paascenter.cms.model.request.VirtualizeDeviceRequest;
 import net.armcloud.paascenter.cms.model.response.*;
 import net.armcloud.paascenter.cms.service.DeviceOpService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.web.bind.annotation.RequestBody;
 import org.springframework.web.bind.annotation.RestController;
 
 import javax.validation.Valid;
 import java.util.List;
 
 @Slf4j
 @RestController
 public class DeviceOpenController implements DeviceFacade {
     private final DeviceOpService deviceOpService;
 
     @Override
     public Result<List<DeviceVirtualizeResponse>> virtualize(@Valid @RequestBody VirtualizeDeviceRequest request) {
         return Result.ok(deviceOpService.addVirtualizeTask(request));
     }
 
     @Override
     public Result<List<DeviceDestroyResponse>> destroy(@Valid @RequestBody DeviceIpsRequest req) {
         return Result.ok(deviceOpService.addDestroyTask(req));
     }
 
     @Override
     public Result<List<DeviceRestartResponse>> restart(@Valid @RequestBody DeviceIpsRequest req) {
         return Result.ok(deviceOpService.addRestartTask(req));
     }
 
     @Override
     public Result<List<DeviceInfoResponse>> info(@Valid @RequestBody DeviceIpsRequest req) {
         return Result.ok(deviceOpService.info(req));
     }
 
     @Override
     public Result<List<SelfUpdateResponse>> selfUpdate(@Valid @RequestBody SelfUpdateRequest request) {
         return Result.ok(deviceOpService.selfUpdate(request));
     }
 
     public DeviceOpenController(DeviceOpService deviceOpService) {
         this.deviceOpService = deviceOpService;
     }
 }