 package net.armcloud.paascenter.cms.exception.code;
 
 import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
 import lombok.AllArgsConstructor;
 import lombok.Getter;
 
 @Getter
 @AllArgsConstructor
 public enum DeviceExceptionCode implements ExceptionCode {
     DEVICE_NOT_VIRTUALIZE_EXCEPTION(171000, "云机未创建"),
     DEVICE_GET_STATUS_FAIL_VIRTUALIZE_EXCEPTION(171001, "云机信息查询失败"),
     DEVICE_CONNECT_EXCEPTION(171002, "连接云机失败"),
     DEVICE_NETWORK_DELETION_FAILED_EXCEPTION(171003, "云机网络删除失败"),
     DEVICE_PARTITION_DISK_FAILED_EXCEPTION(171004, "云机格盘失败"),
     DEVICE_RESTART_DOCKER_FAILED_EXCEPTION(171005, "云机docker重启失败"),
     DEVICE_NETWORK_CREATE_FAILED_EXCEPTION(171006, "云机网络创建失败"),
     DEVICE_MACVLAN_MODE_FILE_DELETE_FAILED_EXCEPTION(171007, "云机标准化IP路由配置删除失败"),
     ;
 
     private final int status;
     private final String msg;
 }