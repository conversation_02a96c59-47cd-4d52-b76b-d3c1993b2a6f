 package net.armcloud.paascenter.cms.openapi;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.facade.InstanceFacade;
 import net.armcloud.paascenter.cms.model.request.*;
 import net.armcloud.paascenter.cms.model.response.*;
 import net.armcloud.paascenter.cms.service.InstanceOpService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.web.bind.annotation.RequestBody;
 import org.springframework.web.bind.annotation.RestController;
 
 import javax.validation.Valid;
 import java.util.List;
 
 @Slf4j
 @RestController
 public class InstanceOpenController implements InstanceFacade {
     private final InstanceOpService instanceOpService;
 
     @Override
     public Result<List<InstanceUpgradeImageResponse>> upgradeImage(@Valid @RequestBody InstanceUpgradeImageRequest req) {
         return Result.ok(instanceOpService.addUpgradeImage(req));
     }
 
     @Override
     public Result<List<InstanceRestartResponse>> restart(@Valid @RequestBody InstanceRestartRequest req) {
         return Result.ok(instanceOpService.addRestartTask(req));
     }
 
     @Override
     public Result<List<InstanceResetResponse>> reset(@Valid @RequestBody InstanceResetRequest req) {
         return Result.ok(instanceOpService.addResetTask(req));
     }
 
     @Override
     public Result<List<InstanceResetResponse>> replaceProp(InstanceReplacePropRequest req) {
         return Result.ok(instanceOpService.replaceProp(req));
     }
 
 
     @Override
     public Result<List<InstanceNetworkLimitResponse>> networkLimit(InstanceNetworkLimitRequest req) {
         return Result.ok(instanceOpService.addNetworkLimitTask(req));
     }
 
     @Override
     public Result<List<InstanceUpdatePropResponse>> updateProp(InstanceUpdatePropRequest req) {
         return Result.ok(instanceOpService.updateProp(req));
     }
 
     @Override
     public Result<List<InstanceUpgradeImageResponse>> virtualRealSwitchUpgradeImage(InstanceVirtualRealSwitchRequest req) {
         return Result.ok(instanceOpService.addVirtualRealSwitchUpgradeImage(req));
     }
 
     @Override
     public Result<List<InstanceBackupDataResponse>> backupData(InstanceBackupDataRequest req) {
         return Result.ok(instanceOpService.backupData(req));
     }
 
     @Override
     public Result<List<InstanceRestoreBackupDataResponse>> restoreBackupData(InstanceRestoreBackupDataRequest req) {
         return Result.ok(instanceOpService.restoreBackupData(req));
     }
 
     @Override
     public Result<List<InstanceModifyPropertiesResponse>> modifyProperties(InstanceModifyPropertiesRequest req) {
         return Result.ok(instanceOpService.modifyProperties(req));
     }
 
     @Override
     public Result<List<InstanceUpgradeImageResponse>> virtualRealSwitchUpgradeImage(InstanceRealVirtualSwitchRequest req) {
         return Result.ok(instanceOpService.addRealVirtualSwitchUpgradeImage(req));
     }
 
     @Override
     public Result<List<InstanceLifecycleStatusResponse>> instanceLifecycleStatus(InstanceLifecycleStatusRequest req) {
         return Result.ok( instanceOpService.instanceLifecycleStatus(req));
     }
 
     @Override
     public Result<List<InstanceUpgradeImageResponse>> replaceRealAdbTemplate(@RequestBody InstanceReplaceRealAdiTemplateRequest req) {
         return Result.ok(instanceOpService.replaceRealAdiTemplate(req));
     }
 
     public InstanceOpenController(InstanceOpService instanceOpService) {
         this.instanceOpService = instanceOpService;
     }
 }