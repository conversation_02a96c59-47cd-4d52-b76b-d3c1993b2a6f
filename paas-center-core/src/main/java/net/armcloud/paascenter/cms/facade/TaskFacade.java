 package net.armcloud.paascenter.cms.facade;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.model.request.TaskDetailRequest;
 import net.armcloud.paascenter.cms.model.response.DeviceTaskDetailResponse;
 import net.armcloud.paascenter.cms.model.response.InstanceTaskDetailResponse;
 import org.springframework.web.bind.annotation.PostMapping;
 
 public interface TaskFacade {
     /**
      * 实例任务详情
      */
     @PostMapping(value = "/armcloud-container/open/task/instance/detail")
     Result<InstanceTaskDetailResponse> instanceDetail(TaskDetailRequest request);
 
     /**
      * 云机任务详情
      */
     @PostMapping(value = "/armcloud-container/open/task/device/detail")
     Result<DeviceTaskDetailResponse> deviceDetail(TaskDetailRequest request);
 }