 package net.armcloud.paascenter.cms.manager.cbs;
 
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.stereotype.Component;
 
 import static net.armcloud.paascenter.cms.exception.code.ImageExceptionCode.LOGIN_IMAGE_REPOSITORY_FAIL_EXCEPTION;
 
 @Component
 public class CBSHarborManager {
     private final SystemManager systemManager;
     private final HarborConfigManage harborConfigManage;
 
     //TODO 登录镜像仓库后续可以优化成传账号密码,由CBS登录
     public void loginHarbor(String deviceIp) {
         String loginHarborTemplateCmd = "echo %s | docker login %s --username %s --password-stdin";
         HarborConfig harborConfig = harborConfigManage.get();
         String loginCmd = String.format(loginHarborTemplateCmd, harborConfig.getPassword(), harborConfig.getUrl(), harborConfig.getUsername());
         String loginResult = systemManager.execSyncCmd(deviceIp, loginCmd);
         if (StringUtils.isBlank(loginResult) || !loginResult.contains("Succeeded")) {
             throw new BasicException(LOGIN_IMAGE_REPOSITORY_FAIL_EXCEPTION.getStatus(), "login harbor fail");
         }
     }
 
     public CBSHarborManager(SystemManager systemManager, HarborConfigManage harborConfigManage) {
         this.systemManager = systemManager;
         this.harborConfigManage = harborConfigManage;
     }
 }