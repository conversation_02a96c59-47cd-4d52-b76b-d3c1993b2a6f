 package net.armcloud.paascenter.cms.manager;
 
 import net.armcloud.paascenter.common.model.entity.container.TaskTimeoutConfig;
 import net.armcloud.paascenter.common.redis.service.RedisService;
 import net.armcloud.paascenter.cms.mapper.*;
 import org.springframework.stereotype.Component;
 
 import java.time.LocalDateTime;
 import java.time.ZoneId;
 import java.util.Date;
 import java.util.Objects;
 import java.util.Optional;
 import java.util.concurrent.TimeUnit;
 
 import static net.armcloud.paascenter.cms.constants.CacheKeyConstants.TARGET_TASK_TYPE_TIMEOUT_SECOND;
 
 @Component
 public class CmsTaskManager {
     private final RedisService redisService;
     private final CmsTaskTimeoutConfigMapper cmsTaskTimeoutConfigMapper;
 
     public Date getTimeoutTime(int taskType) {
         String key = TARGET_TASK_TYPE_TIMEOUT_SECOND + taskType;
         String timeoutSecondsStr = redisService.getCacheObject(key);
         Integer timeoutSeconds = Optional.ofNullable(timeoutSecondsStr).map(Integer::valueOf).orElse(null);
         if (Objects.nonNull(timeoutSeconds) && timeoutSeconds <= 0) {
             return null;
         }
 
         if (Objects.nonNull(timeoutSeconds)) {
             return Date.from(LocalDateTime.now().plusSeconds(timeoutSeconds).atZone(ZoneId.systemDefault()).toInstant());
         }
 
         TaskTimeoutConfig taskTimeoutConfig = cmsTaskTimeoutConfigMapper.getByTaskType(taskType);
         if (Objects.isNull(taskTimeoutConfig)) {
             redisService.setCacheObject(key, "-1", 3L, TimeUnit.DAYS);
             return null;
         }
 
         timeoutSeconds = taskTimeoutConfig.getTimeoutSecond();
         redisService.setCacheObject(key, String.valueOf(timeoutSeconds), 3L, TimeUnit.DAYS);
         return Date.from(LocalDateTime.now().plusSeconds(timeoutSeconds).atZone(ZoneId.systemDefault()).toInstant());
     }
 
     public CmsTaskManager(RedisService redisService, CmsTaskTimeoutConfigMapper cmsTaskTimeoutConfigMapper) {
         this.redisService = redisService;
         this.cmsTaskTimeoutConfigMapper = cmsTaskTimeoutConfigMapper;
     }
 }