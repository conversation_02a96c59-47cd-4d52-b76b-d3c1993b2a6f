package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AudioToMicDTO {

    private Long customerId;
    
    /**实例编号 必填*/
    @Size(min = 1, max = 200, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;
    
    @ApiModelProperty(value = "音频文件下载地址")
    private String url;

    @ApiModelProperty(value = "文件id唯一标识")
    private String fileUniqueId;

    @NotNull(message = "注入音频状态不能为空")
    @ApiModelProperty(value = "注入音频:true | false")
    private Boolean enable = false;

}