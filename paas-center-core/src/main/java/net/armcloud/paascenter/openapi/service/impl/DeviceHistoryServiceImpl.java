package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.service.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class DeviceHistoryServiceImpl extends ServiceImpl<DeviceHistoryMapper, DeviceHistory> implements IDeviceHistoryService {

    @Resource
    DeviceHistoryMapper deviceHistoryMapper;
    
    @Resource
    DeviceMapper deviceMapper;

    @Async
    @Override
    public void insertDeviceHistoryRecords(List<String> deviceCodes, Integer deviceStatus) {
        // 1. 根据deviceCodes查询device_history表中每个deviceCode最新的记录
        List<Device> latestRecords = deviceMapper.selectList(new QueryWrapper<>(Device.class).in("device_code",deviceCodes));
        if (latestRecords == null || latestRecords.isEmpty()) {
            log.warn("No device history records found for device codes: {}", deviceCodes);
            return;
        }
        // 2. 为每条记录创建新的DeviceHistory对象，更新deviceStatus
        List<DeviceHistory> newRecords = new ArrayList<>();
        Date now = new Date();
        for (Device latest : latestRecords) {
            DeviceHistory newRecord = new DeviceHistory();
            BeanUtil.copyProperties(latest,newRecord);
            // 复制所有原始记录的属性
            newRecord.setDeviceStatus(deviceStatus);
            // 设置审计字段
            newRecord.setCreateTime(now);
            newRecord.setUpdateTime(now);
            newRecord.setCreateBy("admin");
            newRecord.setUpdateBy("admin");
            newRecord.setId(null);
            newRecords.add(newRecord);
        }
        
        // 3. 批量插入新的记录
        if (!newRecords.isEmpty()) {
            int insertCount = deviceHistoryMapper.batchInsert(newRecords);
//            log.info("Successfully inserted {} device history records with status: {}", insertCount, deviceStatus);
        }
    }
}