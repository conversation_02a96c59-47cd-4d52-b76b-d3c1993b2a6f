package net.armcloud.paascenter.openapi.netpadv2.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.netpadv2.dto.*;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2Service;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ResultVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * 网存实例V2控制器
 *
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Slf4j
@RestController
@RequestMapping("/openapi/open/pad/v2")
@Api(tags = "网存实例V2管理")
public class NetPadV2Controller {

    @Resource
    private NetPadV2Service netPadV2Service;

    /**
     * 创建网存实例V2
     * 优化版本的网存实例创建接口，使用雪花算法ID生成，工厂方法创建实体，事务处理
     */
    @PostMapping("/net/storage/res/create")
    @ApiOperation(value = "创建网存实例V2", notes = "优化版本的网存实例创建接口")
    public Result<List<NetPadV2CreateResponseDTO>> createNetStorageInstances(@RequestBody NetPadV2CreateDTO dto) {

        // 设置客户ID和操作人
        dto.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        dto.setOprBy(String.valueOf(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest())));
        dto.setSourceCode("PAAS");

        List<NetPadV2CreateResponseDTO> results = netPadV2Service.createNetStorageInstances(dto, SourceTargetEnum.PAAS);

        log.info("网存实例V2创建完成，共创建{}个实例", results.size());
        return Result.ok(results);
    }

    /**
     * 批量开机网存实例
     * 支持传入多个实例进行批量开机操作
     *
     * @param param 批量开机参数
     * @return 任务列表
     */
    @PostMapping("/net/storage/batch/boot/on")
    @ApiOperation(value = "批量开机网存实例", notes = "支持传入多个实例进行批量开机操作")
    public Result<NetPadV2ResultVO> batchBootOn(@RequestBody NetPadV2BatchBootOnDTO param) {

        // 设置客户ID
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        // 调用服务层进行批量开机
        NetPadV2ResultVO result = netPadV2Service.batchBootOn(param, SourceTargetEnum.PAAS);

        result.printResult(log);
        return Result.ok(result);
    }

    /**
     * 批量关机网存实例
     * 支持传入多个实例进行批量关机操作
     *
     * @param param 批量关机参数
     * @return 任务列表
     */
    @PostMapping("/net/storage/batch/off")
    @ApiOperation(value = "批量关机网存实例", notes = "支持传入多个实例进行批量关机操作")
    public Result<NetPadV2ResultVO> batchBootOff(@RequestBody NetPadV2BatchOffDTO param) {
        // 设置客户ID
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        // 调用服务层进行批量关机
        NetPadV2ResultVO result = netPadV2Service.batchBootOff(param, SourceTargetEnum.PAAS);

        result.printResult(log);
        return Result.ok(result);
    }
    /**
     * 批量删除网存
     * @param param
     * @return
     */
    @PostMapping("/net/storage/batch/delete")
    @ApiOperation(value = "批量删除网存实例", notes = "支持传入多个实例进行批量删除操作")
    public Result<NetPadV2ResultVO> batchDelete(@RequestBody NetPadV2BatchDelDTO param) {

        // 设置客户ID
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

         // 调用服务层进行批量删除
        NetPadV2ResultVO result = netPadV2Service.batchDelete(param, SourceTargetEnum.PAAS);

        result.printResult(log);
        return Result.ok(result);
    }

}

