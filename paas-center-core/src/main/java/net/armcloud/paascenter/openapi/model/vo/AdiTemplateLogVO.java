package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * ADI模板操作日志返回对象
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板操作日志VO", description = "返回给前端的ADI模板操作日志信息")
public class AdiTemplateLogVO {

    @ApiModelProperty(value = "日志ID")
    private Long id;

    @ApiModelProperty(value = "ADI模板ID")
    private Long templateId;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "操作类型编码(0:上传/1:启用/2:禁用/3:编辑/4:删除)")
    private Integer operationType;

    @ApiModelProperty(value = "操作类型描述")
    private String operationTypeDesc;

    @ApiModelProperty(value = "操作者ID")
    private Long operator;

    @ApiModelProperty(value = "操作者名称")
    private String operatorName;

    @ApiModelProperty(value = "操作详情")
    private String operationDetail;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;
} 