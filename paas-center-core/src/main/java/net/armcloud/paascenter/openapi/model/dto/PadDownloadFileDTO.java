package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class PadDownloadFileDTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于1个")
    private List<String> padCodes;

    @ApiModelProperty(value = "文件下载地址", required = true)
    @NotBlank(message = "fileDownloadUrl cannot null")
    private String fileDownloadUrl;

    @ApiModelProperty(hidden = true)
    private Long customerId;

    @NotBlank(message = "fileName cannot null")
    @ApiModelProperty(value = "文件名", required = true)
    private String fileName;

    @ApiModelProperty(value = "是否进行安装，默认false")
    private Boolean install = false;

    @ApiModelProperty(value = "文件存储路径，非必传，需以/开头")
    private String targetDirectory;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @ApiModelProperty(value = "操作者")
    private String oprBy;
}
