package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.DeviceChangeInfo;

import java.util.List;

public interface IDeviceChangeInfoService extends IService<DeviceChangeInfo> {
    /**
     * 获取所有挂载版本是V2的板卡IP
     * @return
     */
   List<String> getMountVersionV1(List<String> deviceIpList);
}
