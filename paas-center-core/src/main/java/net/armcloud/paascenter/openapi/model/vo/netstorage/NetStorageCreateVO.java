package net.armcloud.paascenter.openapi.model.vo.netstorage;

import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.Pad;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/29 18:53
 * @Description:
 */
@Data
public class NetStorageCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String padCode;

    private String deviceLevel;
    private String padIp;
    private Integer padSn;
    private String imageId;
    private Integer cloudVendorType;
    private Long groupId;
    private Long customerId;
    private Integer status;
    private Integer online;
    private Integer streamStatus;
    private Long dataSize;
    private Long dataSizeUsed;
    private Long dataSizeAvailable;
    private Integer streamType;
    private String armServerCode;
    private String socModel;
    private Integer cpu;
    private Integer memory;
    private Integer storage;
    private String screenLayoutCode;
    private Integer netStorageResFlag;
    private String netStorageResId;
    private Long netStorageResSize;
    private Long realPhoneTemplateId;
    private String dns;
    private String clusterCode;

    public static NetStorageCreateVO build(Pad pad) {
        if (pad == null) {
            return null;
        }

        NetStorageCreateVO createVO = new NetStorageCreateVO();

        createVO.setPadCode(pad.getPadCode());
        createVO.setDeviceLevel(pad.getDeviceLevel());
        createVO.setPadIp(pad.getPadIp());
        createVO.setPadSn(pad.getPadSn());
        createVO.setImageId(pad.getImageId());
        createVO.setCloudVendorType(pad.getCloudVendorType());
        createVO.setGroupId(pad.getGroupId());
        createVO.setCustomerId(pad.getCustomerId());
        createVO.setStatus(pad.getStatus());
        createVO.setOnline(pad.getOnline());
        createVO.setStreamStatus(pad.getStreamStatus());
        createVO.setDataSize(pad.getDataSize());
        createVO.setDataSizeUsed(pad.getDataSizeUsed());
        createVO.setDataSizeAvailable(pad.getDataSizeAvailable());
        createVO.setStreamType(pad.getStreamType());
        createVO.setArmServerCode(pad.getArmServerCode());
        createVO.setSocModel(pad.getSocModel());
        createVO.setCpu(pad.getCpu());
        createVO.setMemory(pad.getMemory());
        createVO.setStorage(pad.getStorage());
        createVO.setScreenLayoutCode(pad.getScreenLayoutCode());
        createVO.setNetStorageResFlag(pad.getNetStorageResFlag());
        createVO.setNetStorageResId(pad.getNetStorageResId());
        createVO.setNetStorageResSize(pad.getNetStorageResSize());
        createVO.setRealPhoneTemplateId(pad.getRealPhoneTemplateId());
        createVO.setDns(pad.getDns());
        createVO.setClusterCode(pad.getClusterCode());

        return createVO;
    }

}
