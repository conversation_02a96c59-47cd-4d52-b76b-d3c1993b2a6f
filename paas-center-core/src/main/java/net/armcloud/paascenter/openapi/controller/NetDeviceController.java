package net.armcloud.paascenter.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paascenter.openapi.model.dto.NetDeviceDTO;
import net.armcloud.paascenter.openapi.service.INetDeviceService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.NetDevice;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/2/11 15:42
 * @Version 1.0
 */
@RestController
@RequestMapping("/openapi/open/netDevice")
@Api(tags = "板卡网络")
public class NetDeviceController {


    @Resource
    private INetDeviceService netDeviceService;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "新增板卡网络", httpMethod = "POST", notes = "新增板卡网络")
    public Result<?> saveNetDevice(@Valid @RequestBody NetDeviceDTO param) {
        NetDevice netDevice = new NetDevice();
        BeanUtil.copyProperties(param, netDevice);
        return netDeviceService.saveNetDevice(netDevice);
    }
}
