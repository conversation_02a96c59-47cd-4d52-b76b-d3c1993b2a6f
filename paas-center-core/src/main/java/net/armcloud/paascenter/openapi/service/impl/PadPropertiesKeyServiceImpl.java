package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import net.armcloud.paascenter.openapi.mapper.PadPropertiesKeyMapper;
import net.armcloud.paascenter.openapi.service.IPadPropertiesKeyService;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PadPropertiesKeyServiceImpl implements IPadPropertiesKeyService {

    @Resource
    RedisService redisService;

    @Resource
    private PadPropertiesKeyMapper padPropertiesKeyMapper;

    @Override
    public List<String> getPropertiesKeyByType(Integer propertiesType) {
        String key = RedisKeyPrefix.PROPERTIES_KEY + propertiesType;
        String keyArray = redisService.getCacheObject(key);
        if (ObjectUtil.isNull(keyArray)) {
            List<String> keys = padPropertiesKeyMapper.selectPropertiesKeyByType(propertiesType);
            String jsonArray = JSON.toJSONString(keys);
            redisService.setCacheObject(key, jsonArray, RedisKeyTime.minute_30, TimeUnit.MINUTES);
            return keys;
        }
        return JSONArray.parseArray(keyArray, String.class);
    }
}
