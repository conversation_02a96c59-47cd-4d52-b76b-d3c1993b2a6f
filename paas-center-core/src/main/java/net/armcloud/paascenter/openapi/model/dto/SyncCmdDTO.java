package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SyncCmdDTO extends BaseDTO {
    @ApiModelProperty(value = "实例列表", required = true)
    @NotBlank(message = "padCode cannot null")
    private String padCode;

    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "ADB命令", required = true)
    @NotBlank(message = "scriptContent cannot null")
    private String scriptContent;
}
