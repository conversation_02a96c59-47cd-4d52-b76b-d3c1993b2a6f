package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import net.armcloud.paascenter.cms.model.request.ProxyDetectionRequest;
import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;
import net.armcloud.paascenter.common.client.component.CommonPadCommandComponent;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.command.ClearAppHomeCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.GetPadProxyCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadSetProxyCMDDTO;
import net.armcloud.paascenter.common.client.internal.feign.ContainerSystemFeignClient;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.utils.FeignUtils;
import net.armcloud.paascenter.common.utils.MD5Utils;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.dto.padnetowrk.*;
import net.armcloud.paascenter.openapi.service.IPadNetworkService;
import net.armcloud.paascenter.openapi.utils.CmdUtil;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.client.internal.utils.ContainerFeignUtils.builderHost;
import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.PROXY_DETECTION_PREFIX;
import static net.armcloud.paascenter.openapi.constants.LockConstants.PAD_BIND_IP_LOCK_PREFIX;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.PAD_CODE_NOT_EXIST;
import static net.armcloud.paascenter.common.core.constant.Constants.CUSTOMER_ID_HUMP;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.PROXY_CONFIGURE;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.GET_PAD_NETWORK_PROXY_INFO;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.PAD_SET_NETWORK_PROXY;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PARAMETER_EXCEPTION;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;

@Slf4j
@Service
public class PadNetworkServiceImpl implements IPadNetworkService {
    private final PadMapper padMapper;
    private final CommonPadCommandComponent commonPadCommandComponent;
    private final CommonPadTaskComponent commonPadTaskComponent;
    private final RedissonDistributedLock redissonDistributedLock;
    private final ContainerSystemFeignClient containerSystemFeignClient;
    @Resource
    private RedisService redisService;

    @Value("${proxyDetectionDomain:https://openapi-hk.armcloud.net/openapi/open/network/proxy/getMyIp}")
    private String proxyDetectionDomain;

    @Override
    public List<GeneratePadTaskVO> setProxy(SetPadNetworkProxyDTO param) {
        PadSetProxyCMDDTO padSetProxyCMDDTO = new PadSetProxyCMDDTO()
                .setAccount(param.getAccount())
                .setPassword(param.getPassword())
                .setIp(param.getIp())
                .setPort(param.getPort())
                .setEnable(param.getEnable());

        PadCMDForwardDTO padCMDForwardDTO = padSetProxyCMDDTO.builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                PAD_SET_NETWORK_PROXY, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> setProxyV2(JSONObject param) {
        List<String> padCodes = param.getList("padCodes", String.class);
        if (CollectionUtils.isEmpty(padCodes)) {
            throw new BasicException(PARAMETER_EXCEPTION.getStatus(), "padCodes cannot null");
        }

        Long customerId = param.getLong("customerId");
        param.remove("padCodes");
        param.remove("customerId");
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> {
            PadCMDForwardDTO.PadInfoDTO infoDTO = new PadCMDForwardDTO.PadInfoDTO()
                    .setData(param)
                    .setPadCode(padCode);
            padInfos.add(infoDTO);
        });

        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(PROXY_CONFIGURE);
        padCMDForwardDTO.setSourceCode(PAAS);
        padCMDForwardDTO.setPadInfos(padInfos);
        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(customerId,padCodes, PAD_SET_NETWORK_PROXY, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> proxyInfo(PadCodesDTO param) {
        PadCMDForwardDTO padCMDForwardDTO = new GetPadProxyCMDDTO().builderForwardDTO(param.getPadCodes(), PAAS);
        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(),
                GET_PAD_NETWORK_PROXY_INFO, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> networkInterruptEnable(PadNetworkInterruptEnableDTO param) {
        JSONObject jsonObjectParam = new JSONObject();
        jsonObjectParam.put(CUSTOMER_ID_HUMP, param.getCustomerId());
        jsonObjectParam.put("padCodes",param.getPadCodes());
        jsonObjectParam.put("enable",param.getEnable());
        jsonObjectParam.put("proxyType","proxy");
        //不管enable设置true还是false 下面4个字段都要设置 可以随便设置 如果不设置 则收不到gameserver回调
        jsonObjectParam.put("account","account");
        jsonObjectParam.put("password","password");
        jsonObjectParam.put("ip","***********");
        jsonObjectParam.put("port","2222");
        List<GeneratePadTaskVO> generatePadTaskVOList = setProxyV2(jsonObjectParam);
        //断网指令任务创建成功后 再发送一个指令"杀死所有的app并返回云机首页"
        if(param.getEnable()){
            PadCMDForwardDTO padCMDForwardDTO = new ClearAppHomeCMDDTO().builderForwardDTO(param.getPadCodes(),PAAS);
            commonPadTaskComponent.addPadCMDTask(param.getCustomerId(),param.getPadCodes(), TaskTypeConstants.CLEAR_APP_HOME, padCMDForwardDTO);
        }
        return generatePadTaskVOList;
    }

    @Override
    public ProxyDetectionResponse proxyDetection(ProxyDetectionDTO param) {
        ProxyDetectionResponse proxyDetectionResponse = new ProxyDetectionResponse();
        proxyDetectionResponse.setConnStatus(true);
        proxyDetectionResponse.setExportIp("");
        return proxyDetectionResponse;

//        ProxyDetectionResponse proxyDetectionResponse = null;
//        String cacheKey = PROXY_DETECTION_PREFIX + MD5Utils.generateMD5(JSONObject.toJSONString(param));
//        String obj = redisService.getCacheObject(cacheKey);
//        if(StrUtil.isNotEmpty(obj)){
//            proxyDetectionResponse = JSONObject.parseObject(obj,ProxyDetectionResponse.class);
//            return proxyDetectionResponse;
//        }
//        //padCode为空则在当前环境检测 否则去cms检测
//        /*if(StrUtil.isNotEmpty(param.getPadCode())){
//            List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(param.getPadCode()));
//            if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
//                throw new BasicException("not found cluster");
//            }
//            PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
//            URI host = builderHost(padEdgeClusterVO.getClusterPublicIp());
//            ProxyDetectionRequest proxyDetectionRequest = BeanUtil.copyProperties(param,ProxyDetectionRequest.class);
//            proxyDetectionResponse = FeignUtils.getContent(containerSystemFeignClient.proxyDetection(host,proxyDetectionRequest));
//        }else{*/
//            boolean isS5 = param.getProxyType()==null || param.getProxyType() == 1;
//            proxyDetectionResponse = CmdUtil.proxyDetection(proxyDetectionDomain,param.getProxyHost(),param.getProxyPort(),param.getProxyAccount(),param.getProxyPwd(),isS5);
//        //}
//        //缓存10s 防止并发
//        redisService.setCacheObject(cacheKey,JSONObject.toJSONString(proxyDetectionResponse),10L, TimeUnit.SECONDS);
//        return proxyDetectionResponse;
    }

    public PadNetworkServiceImpl(PadMapper padMapper, CommonPadCommandComponent commonPadCommandComponent,
                                 CommonPadTaskComponent commonPadTaskComponent, RedissonDistributedLock redissonDistributedLock,
                                 ContainerSystemFeignClient containerSystemFeignClient) {
        this.padMapper = padMapper;
        this.commonPadCommandComponent = commonPadCommandComponent;
        this.commonPadTaskComponent = commonPadTaskComponent;
        this.redissonDistributedLock = redissonDistributedLock;
        this.containerSystemFeignClient = containerSystemFeignClient;
    }
}
