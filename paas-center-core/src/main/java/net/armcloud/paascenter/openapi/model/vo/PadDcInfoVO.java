package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;

import java.util.Objects;

@Data
public class PadDcInfoVO {
    /**
     * 机房名称
     */
    private String dcName;

    /**
     * OSS公网接口地址
     */
    private String ossEndpoint;

    /**
     * OSS内网接口地址
     */
    private String ossEndpointInternal;

    public static PadDcInfoVO builder(DcInfoVO dcInfoVO) {
        if (Objects.isNull(dcInfoVO)) {
            return null;
        }

        PadDcInfoVO padDcInfoVO = new PadDcInfoVO();
        padDcInfoVO.setDcName(dcInfoVO.getDcName());
        padDcInfoVO.setOssEndpoint(dcInfoVO.getOssEndpoint());
        padDcInfoVO.setOssEndpointInternal(dcInfoVO.getOssEndpointInternal());
        return padDcInfoVO;
    }
}
