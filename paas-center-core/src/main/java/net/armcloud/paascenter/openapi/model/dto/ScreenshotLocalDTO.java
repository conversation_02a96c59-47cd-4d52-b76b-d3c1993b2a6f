package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class ScreenshotLocalDTO extends BaseDTO {

    @ApiModelProperty(value = "实例id", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于1个")
    private List<String> padCodes;

    /**
     * 截图画面横竖屏旋转
     * <p>
     * 0：截图方向不做处理，默认
     * 1：截图画面旋转为竖屏：
     * 手机竖屏的截图，不做处理
     * 手机横屏的截图，截图顺时针旋转90度
     */
    @ApiModelProperty(value = "旋转方式(默认0)，0：截图方向不做处理；1：截图画面旋转为竖屏")
    @Min(value = 0, message = "rotation参数值不正确")
    @Max(value = 1, message = "rotation参数值不正确")
    private Integer rotation = 0;

    /**
     * 事件是否广播
     */
    @ApiModelProperty(value = "事件是否广播(默认false)")
    private Boolean broadcast = false;

    /**
     * 清晰度 0-100
     */
    @Min(value = 0, message = "清晰度取值范围0-100")
    @Max(value = 100, message = "清晰度取值范围0-100")
    @ApiModelProperty(value = "清晰度")
    private Integer definition;
    /**
     * 分辨率 - 高
     */
    @ApiModelProperty(value = "分辨率 - 高")
    @Min(value = 1, message = "分辨率-高取值应大于等于1")
    @Max(value = Integer.MAX_VALUE, message = "分辨率-高取值过大")
    private Integer resolutionHeight;
    /**
     * 分辨率 - 宽
     */
    @ApiModelProperty(value = "分辨率 - 宽")
    @Min(value = 1, message = "分辨率-宽取值应大于等于1")
    @Max(value = Integer.MAX_VALUE, message = "分辨率-宽取值过大")
    private Integer resolutionWidth;

    @ApiModelProperty(hidden = true)
    private Long customerId;
}
