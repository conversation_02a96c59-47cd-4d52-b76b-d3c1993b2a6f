package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * 根据国家code生成sim卡相关的安卓属性
 */
@Data
public class ReplacePadAndroidPropDTO  extends BaseDTO {
    @NotBlank(message = "padCode cannot null")
    private String padCode;

    /**
     * 安卓属性
     */
    private Map<String, String> props;

    /**
     * 国家code
     */
    private String countryCode;

}
