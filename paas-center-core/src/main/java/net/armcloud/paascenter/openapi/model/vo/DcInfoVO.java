package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DcInfoVO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 机房名称
     */
    private String dcName;

    private String dcCode;

    private String area;

    /**
     * OSS公网接口地址
     */
    private String ossEndpoint;

    /**
     * OSS内网接口地址
     */
    private String ossEndpointInternal;

    /**
     * 访问文件地址
     */
    private String ossFileEndpoint;

    /**
     * 访问截图地址
     */
    private String ossScreenshotEndpoint;
}
