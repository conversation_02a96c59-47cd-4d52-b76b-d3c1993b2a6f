package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;

@Data
public class PadRecordScreenDTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotBlank(message = "padCode cannot null")
    private String padCode;

    @ApiModelProperty(value = "截图选项", required = true)
    @NotBlank(message = "option cannot null")
    private String option;

    @ApiModelProperty(value = "文件名", required = true)
    private String fileName;

    @Max(value = 10800)
    @ApiModelProperty(value = "录屏时长", required = true)
    private Integer timeLimitSeconds;

    @ApiModelProperty(hidden = true)
    private Long customerId;
}
