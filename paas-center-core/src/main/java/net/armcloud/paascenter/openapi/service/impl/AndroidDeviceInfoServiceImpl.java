package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.service.AndroidDeviceInfoService;
import net.armcloud.paascenter.common.model.entity.paas.AndroidDeviceInfo;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.mapper.AndroidDeviceInfoMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AndroidDeviceInfoServiceImpl extends ServiceImpl<AndroidDeviceInfoMapper, AndroidDeviceInfo> implements AndroidDeviceInfoService {


    @Override
    public AndroidDeviceInfo findOneRandom(String av) {
        LambdaQueryWrapper<AndroidDeviceInfo> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(av)){
            wrapper.eq(AndroidDeviceInfo::getAndroidVersion,av);
        }
        wrapper.eq(AndroidDeviceInfo::getStatus, 1)
                .last("ORDER BY RAND() LIMIT 1");
        return this.baseMapper.selectOne(wrapper);
    }

}
