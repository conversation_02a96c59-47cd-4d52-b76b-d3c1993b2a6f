package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.client.internal.dto.DeviceBoardImageWarmupDTO;
import net.armcloud.paascenter.common.client.internal.dto.DeviceQueryDTO;
import net.armcloud.paascenter.common.client.internal.dto.NetDeviceLevelDTO;
import net.armcloud.paascenter.common.client.internal.vo.*;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.vo.api.DeviceCustomerVo;
import net.armcloud.paascenter.common.model.vo.job.DeviceInitStatusVO;
import net.armcloud.paascenter.common.model.vo.job.QiShuoNewCard;
import net.armcloud.paascenter.openapi.model.vo.DeviceItemVO;
import net.armcloud.paascenter.openapi.netpadv2.service.impl.DeviceModelInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    List<String> selectDeviceOutCodeByDeviceCode(@Param("deviceCodes") List<String> deviceCodes, @Param("customerId") Long customerId);

    List<String> selectDeviceOutCodeByPadCode(@Param("padCodes") List<String> padCodes, @Param("deviceCodes") Long customerId);

    List<DeviceInfoVo> selectDeviceInfoByDeviceIpDc(@Param("deviceIps") List<String> deviceIps, @Param("dcId") Long dcId, @Param("customerId") Long customerId);

    List<VirtualizeDeviceInfoVO> selectListVirtualizeInfo(@Param("deviceIps") List<String> deviceIps);
    VirtualizeDeviceInfoVO selectListVirtualizeInfoByNetStorageRes(@Param("deviceId") Long deviceId);


    List<DeviceDestroyVO> selectCanDeviceByDeviceIp(@Param("deviceIps") List<String> deviceIps);

    DeviceCustomerVo getDeviceCustomerByDeviceIp(@Param("deviceIp") String deviceIp,@Param("clusterCode") String clusterCode);

    List<DeviceCustomerVo>  queryDeviceCustomer(@Param("deviceCodes") List<String> deviceCodes);

    int removePadByDeviceIp(@Param("masterTaskStatus") Integer masterTaskStatus, @Param("deviceIp") String deviceIp);

    List<Device> listByIps(@Param("ips") List<String> ips);

    List<DeviceInfoVo> selectDeviceInfoByDeviceCodeDc(@Param("deviceCodes") List<String> deviceCodes, @Param("dcId") Long dcId, @Param("customerId") Long customerId);

    List<Device> listByDeviceCodes(@Param("deviceCodes") List<String> deviceCodes);

    /**
     * 查询挂载方式不是V2版本的板卡
     * @return
     */
    List<DeviceInfoVo> getDeviceMountVersionV1();

    /**
     * 根据查询条件获取设备列表
     * @param param
     * @return
     */
    List<DeviceQueryDTO> listByDeviceQueryDTO(DeviceQueryListVo param);

    List<DeviceDestroyVO> selectCanDeviceByDeviceIpAndCbsUpdate(@Param("deviceIps") List<String> deviceIps);

    void saveDevice(Device device);

    List<DeviceItemVO> getDeviceInfos(@Param("armServiceCode") String armServiceCode, @Param("nodeId") String nodeId, @Param("deviceStatus") Integer deviceStatus);


    List<DeviceVO> getDeviceInfo(@Param("deviceIps") List<String> deviceIps);
    
    List<DeviceVO> getDeviceInfoByCode(@Param("deviceCodes") List<String> deviceCodes);

    List<String> selectPadByDeviceCode(@Param("deviceCodes") List<String> deviceCodes);

    int selectTaskByTaskTypeAndTaskStatus(@Param("pads") List<String> pads,  @Param("statuses") List<Integer> status);


    DeviceInfoVo getDeviceInfoByDeviceCode(@Param("deviceCode") String deviceCode);

    DeviceInitStatusVO selectInitStatusGroupBy(String armServerCode);


    List<QiShuoNewCard> selectQiShuoNewCard();

    List<DeviceInfoVo> selectDeviceInfos();

    List<DeviceInfoVo> selectBatchById(@Param("ids") List<String> deviceIds);

    String selectDeviceIpByPadCode(String padCode);

    List<Device> allDevice();

    DeviceVO getCustomer(@Param("deviceIp") String deviceIp);



    Integer updatePadAllocationStatusById(@Param("deviceIdList")List<Long> list,@Param("padAllocationStatus")Integer padAllocationStatus);

    /**
     * 根据用户ID跟板卡规格获取板卡
     * @param customerId
     * @param deviceLevel
     * @return
     */
    List<DeviceVO> selectDeviceByCustomerId(@Param("customerId") Long customerId,@Param("deviceLevel") String deviceLevel);


    /**
     * 获取网存板卡相关参数
     * @param req
     * @return
     */
    List<Device> netDeviceLevelList(NetDeviceLevelDTO req);

    /**
     * 更换板卡信息
     */
    void updateDeviceLevelByDeviceCode(@Param("subList") List<String> subList ,@Param("deviceLevel") String deviceLevel);

    /**
     * 通过板卡ip获取集群信息
     * @param deviceIp
     * @return 集群code
     */
    String getClusterCodeByIp (@Param("deviceIp") String deviceIp);

    /**
     * 根据用户获取该用户下所有的网存板卡
     * @param lambdaQueryWrapper
     * @return
     */
    List<Device> selectListByCustomerId(DeviceBoardImageWarmupDTO lambdaQueryWrapper);

    /**
     * 根据padcode查询板卡的版本
     * @param padCode
     * @return
     */
    String selectDeviceCbsInfoByPadCode(@Param("padCode") String padCode);

    /**
     * 根据板卡编号查询板卡信息
     * @param deviceCode 板卡编号
     * @return 板卡信息
     */
    DeviceInfoVo getArmServerIdByDeviceCode(@Param("deviceCode") String deviceCode);

    /**
     * 根据设备IP获取armServerId
     * @param deviceIp 设备IP
     * @return armServerId
     */
    Long getArmServerIdByDeviceIp(@Param("deviceIp") String deviceIp);

    /**
     * 根据armServerId获取所有设备IP列表
     * @param armServerId ARM服务器ID
     * @return 设备IP列表
     */
    List<String> getDeviceIpsByArmServerId(@Param("armServerId") Long armServerId);

    List<DeviceModelInfoDTO> getDeviceModelInfo(@Param("deviceCodeList") List<String> deviceCodeList);
}
