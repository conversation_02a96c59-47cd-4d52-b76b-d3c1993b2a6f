package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ADI解析结果VO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI解析结果VO", description = "返回给前端的ADI ZIP解析结果")
public class AdiParseVO {

    @ApiModelProperty(value = "是否解析成功")
    private Boolean success;

    @ApiModelProperty(value = "错误消息")
    private String message;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "设备型号")
    private String model;

    @ApiModelProperty(value = "机型名称")
    private String deviceName;

    @ApiModelProperty(value = "系统指纹")
    private String fingerprint;

    @ApiModelProperty(value = "指纹MD5")
    private String fingerprintMd5;

    @ApiModelProperty(value = "Android版本")
    private Integer androidImageVersion;

    @ApiModelProperty(value = "adi模板版本")
    private String adiTemplateVersion;

    @ApiModelProperty(value = "屏幕宽度(像素)")
    private String screenWidth;

    @ApiModelProperty(value = "屏幕高度(像素)")
    private String screenHeight;

    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    @ApiModelProperty(value = "屏幕密度")
    private String screenDensity;

    @ApiModelProperty(value = "是否存在风险项")
    private Boolean hasRiskItems;

    @ApiModelProperty(value = "风险文件列表")
    private List<String> riskFileList;

    @ApiModelProperty(value = "已创建的模板ID")
    private Long templateId;

    private String workDir;
    private String finalZipPassword; // ADI密码

    @ApiModelProperty(value = "ADI 文件大小")
    private long  adiSizeBytes;

    @ApiModelProperty(value = "机型标识")
    private String modelCode;

    @ApiModelProperty(value = "ADI源文件")
    private String publicUrl;

    /**
     * AOSP版本
     */
    @ApiModelProperty(value = "AOSP版本")
    private String aospVersion;

    @ApiModelProperty(value = "生成后的adi文件",hidden = true)
    private String zipFile;

    @ApiModelProperty(value = "生成后的adi文件",hidden = true)
    private Integer packageType;

}