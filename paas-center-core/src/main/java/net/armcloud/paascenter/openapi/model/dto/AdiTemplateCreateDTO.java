package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ADI模板创建DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ADI模板创建对象", description = "用于创建新的ADI模板")
@NoArgsConstructor
public class AdiTemplateCreateDTO extends AdiTemplateCreateBaseDTO {

    @ApiModelProperty(value = "客户ID集合")
    private List<Long> customerIds;

    public AdiTemplateCreateDTO(AdiTemplateCreateBaseDTO baseDTO) {
        this.setBrand(baseDTO.getBrand());
        this.setModel(baseDTO.getModel());
        this.setDeviceName(baseDTO.getDeviceName());
        this.setFingerprint(baseDTO.getFingerprint());
        this.setFingerprintMd5(baseDTO.getFingerprintMd5());
        this.setScreenLayoutCode(baseDTO.getScreenLayoutCode());
        this.setIsOfficial(baseDTO.getIsOfficial());
        this.setScreenWidth(baseDTO.getScreenWidth());
        this.setScreenHeight(baseDTO.getScreenHeight());
        this.setScreenDensity(baseDTO.getScreenDensity());
        this.setModelCode(baseDTO.getModelCode());
        this.setAdiTemplateVersion(baseDTO.getAdiTemplateVersion());
        this.setPublicUrl(baseDTO.getPublicUrl());
        this.setTestCasesDownloadUrl(baseDTO.getTestCasesDownloadUrl());
        this.setAospVersion(baseDTO.getAospVersion());
        this.setAndroidImageVersion(baseDTO.getAndroidImageVersion());
        this.setAdiPassword(baseDTO.getAdiPassword());
        this.setAdiTemplatePwd(baseDTO.getAdiTemplatePwd());
        this.setProperty(baseDTO.getProperty());
        this.setResourceSpecificationCode(baseDTO.getResourceSpecificationCode());
    }
}