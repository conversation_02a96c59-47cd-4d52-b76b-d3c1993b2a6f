package net.armcloud.paascenter.openapi.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import net.armcloud.paascenter.openapi.mapper.CountryInfoMapper;
import net.armcloud.paascenter.openapi.service.BaseInfoService;
import net.armcloud.paascenter.common.model.entity.paas.CountryInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

@Service
public class BaseInfoServiceImpl implements BaseInfoService {

    @Autowired
    private CountryInfoMapper countryInfoMapper;
    
    @Override
    public List<Map<String, String>> getAllSupportedCountries() {
        // 使用BaseMapper的selectList方法查询所有国家信息，只查询未删除的记录
        QueryWrapper<CountryInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_flag", 0);
        List<CountryInfo> countryInfoList = countryInfoMapper.selectList(queryWrapper);
        
        // 转换为前端需要的格式
        List<Map<String, String>> result = new ArrayList<>();
        for (CountryInfo info : countryInfoList) {
            Map<String, String> map = new HashMap<>();
            map.put("code", info.getCountryCode());
            map.put("name", info.getCountryName());
            result.add(map);
        }
        
        return result;
    }
    
    // 实现其他基础信息查询方法
} 