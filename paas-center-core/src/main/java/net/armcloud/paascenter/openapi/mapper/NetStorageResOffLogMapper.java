package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResOffLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface NetStorageResOffLogMapper extends BaseMapper<NetStorageResOffLog> {

    NetStorageResOffLog getByPadCode(@Param("padCode") String padCode);

    List<NetStorageResOffLog> getByPadCodeList(@Param("padCodes") List<String> padCodes);

    void batchInsert(@Param("netStorageResOffLogList") List<NetStorageResOffLog> netStorageResOffLogList);
}
