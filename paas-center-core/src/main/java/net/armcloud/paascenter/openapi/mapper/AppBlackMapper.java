package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.AppBlack;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppBlackMapper extends BaseMapper<AppBlack> {

    List<String> selectBlackAppPkgList(@Param("customerId") Long customerId, @Param("specificationCode") String specificationCode);
}
