package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.common.client.internal.vo.CustomerCallbackVO;
import net.armcloud.paascenter.common.client.internal.dto.CustomerCallbackDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface CustomerCallbackMapper {
    /**
     * 查询客户回调列表
     *
     * @param customerId
     * @return
     */
    List<CustomerCallbackVO> selectByCustomerIdList(@Param("customerId") Long customerId);



    Integer batchInsert(List<CustomerCallbackDTO> list);

    Integer DeleteCallback(@Param("ids") List<Long> ids);


    void deleteByCustomerIdAndCallBackId(@Param("customerId") Long customerId,@Param("callBackId") Long callBackId);

    void deleteByCustomerId(@Param("customerId") Long customerId );


    int updateCallback(@Param("id") Long id, @Param("callbackUrl") String callbackUrl);

    CustomerCallbackVO selectById(@Param("id") Long id);

    List<CustomerCallbackVO> selectList();


    String selectCallback(@Param("customerId") long userId);
}
