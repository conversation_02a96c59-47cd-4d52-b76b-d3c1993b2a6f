package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/24 19:53
 * @Description:网存存储操作mapper
 */
@Mapper
public interface NetStorageResUnitMapper extends BaseMapper<NetStorageResUnit> {

    BigDecimal getClusterUsedSizeTotal(@Param("ids") List<Long> ids);
}