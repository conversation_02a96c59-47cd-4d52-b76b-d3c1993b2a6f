package net.armcloud.paascenter.openapi.model.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import net.armcloud.paascenter.common.model.entity.paas.WifiListInfo;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/6 14:07
 * @Version 1.0
 */
@Data
public class SetWifiListDTO extends BaseDTO implements Serializable {
    /**
     * 修改的wifi列表
     */
    private List<WifiListInfo> wifiJsonList;


    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于1个")
    private List<String> padCodes;


}
