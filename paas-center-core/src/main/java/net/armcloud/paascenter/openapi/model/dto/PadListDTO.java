package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PadListDTO implements Serializable {
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例")
    private List<String> padCodes;

    /**
     * 物理机子网
     */
    private String deviceSubnet;

    /**
     * 服务器编号
     */
    private String armServerCode;

    /**
     * 集群编号
     */
    private String clusterCode;

    /**
     * 机房id
     */
    private String idc;

    /**
     * 实例类型
     */
    private String padType;

    /**
     * 板卡编号
     */
    private String deviceCode;


    /**
     * 实例分组Ids
     */
    @ApiModelProperty(value = "实例分组Id")
    private List<Integer> groupIds;

    @Min(value = 1, message = "offset最小值为1")
    @ApiModelProperty(value = "起始页,默认1")
    @NotNull(message = "page cannot null")
    private Integer page = 1;

    @Max(value = 100, message = "count最大值为100")
    @ApiModelProperty(value = "查询数量,默认10")
    @NotNull(message = "rows cannot null")
    private Integer rows = 10;

    /**
     * 是否网存集群 0 本地集群 1网存集群
     */
    private Integer netStorageResFlag;

}
