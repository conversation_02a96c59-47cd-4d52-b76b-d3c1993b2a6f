package net.armcloud.paascenter.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paascenter.openapi.model.dto.GatewayDeviceDTO;
import net.armcloud.paascenter.openapi.model.dto.GatewayPadDTO;
import net.armcloud.paascenter.openapi.service.IGatewayDeviceService;
import net.armcloud.paascenter.openapi.service.IGatewayPadService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.GatewayDevice;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/2/11 15:31
 * @Version 1.0
 */
@RestController
@RequestMapping("/openapi/open/gatewaySet")
@Api(tags = "网关设置")
public class GatewaySetController {
    @Resource
    private IGatewayPadService gatewayPadService;

    @Resource
    private IGatewayDeviceService gatewayDeviceService;

    @RequestMapping("/addGatewayPad")
    @ApiOperation(value = "添加实例网关", httpMethod = "POST", notes = "添加实例网关")
    public Result<?> addGatewayPad(@Valid @RequestBody GatewayPadDTO gatewayPadDTO){
        GatewayPad gatewayPad = new GatewayPad();
        BeanUtil.copyProperties(gatewayPadDTO,gatewayPad);
        gatewayPadService.insert(gatewayPad);
        return Result.ok();
    }

    @RequestMapping("/addGatewayDevice")
    @ApiOperation(value = "添加板卡网关", httpMethod = "POST", notes = "添加板卡网关")
    public Result<?> addGatewayDevice(@Valid @RequestBody GatewayDeviceDTO gatewayDeviceDTO){
        GatewayDevice gatewayDevice = new GatewayDevice();
        BeanUtil.copyProperties(gatewayDeviceDTO,gatewayDevice);
        gatewayDeviceService.insert(gatewayDevice);
        return Result.ok();
    }
}
