package net.armcloud.paascenter.openapi.rocketmq;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.dto.api.ContainerInstanceTaskResultDTO;
import net.armcloud.paascenter.common.model.mq.container.ContainerInstanceTaskResultMQ;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;


/**
 * topic:vcp_cms_instance_task_status
 */
@Slf4j
@Service
@AliRocketMQMsgListener(topic = "${mq.container-instance-task-message.topic}", consumerGroup = "${mq.container-instance-task-message.group}")
public class ContainerInstanceTaskResultConsumer  implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private ITaskService taskService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("ContainerInstanceTaskResultConsumer onMessage:{}", str);

        //消息幂等
        String key = RedisKeyPrefix.VIRTUALIZE_DEVICE_TASK_RESULT_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.tryLock(key, 0, 5);
        if (Objects.isNull(lock)) {
            log.info("ContainerInstanceTaskResultConsumer not get lock onMessage:{},MessageId:{}", str, messageView.getMessageId());
            return;
        }
        try {
            ContainerInstanceTaskResultMQ dto = JSON.parseObject(str, ContainerInstanceTaskResultMQ.class);
            ContainerInstanceTaskResultDTO updateDcImageStatusDTO = new ContainerInstanceTaskResultDTO();
            BeanUtils.copyProperties(dto, updateDcImageStatusDTO);
            taskService.updateContainerInstanceTaskResult(updateDcImageStatusDTO);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }
}
