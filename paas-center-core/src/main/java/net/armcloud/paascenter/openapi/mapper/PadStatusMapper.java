package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PadStatusMapper extends BaseMapper<PadStatus> {

    List<PadStatus> listByPadCodes(@Param("padCodes") List<String> padCodes);

    List<PadStatus> listByDeviceIps(@Param("deviceIps") List<String> deviceIps);

    /**
     * 批量更新实例状态
     * @param padStatusList
     */
    void updateBatchPadStatus(List<PadStatus> padStatusList);

    /**
     * 批量插入实例状态
     */
    int batchInsertPadStatus(@Param("statuses") List<PadStatus> statuses);

    void updatePadStatus(@Param("padCode") String padCode, @Param("status") Integer status);
}
