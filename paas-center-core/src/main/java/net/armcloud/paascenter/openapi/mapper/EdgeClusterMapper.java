package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;
import net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EdgeClusterMapper extends BaseMapper<EdgeCluster> {
    EdgeClusterVO selectClusterByArmServerCodeAndStatusAndOnline(@Param("clusterCode") String clusterCode, @Param("online") Integer online, @Param("status") Integer status);

    List<EdgeCluster> selectListEdgeCluster();
    int updateEdgeClusterByClusterCode(@Param("clusterCode")String clusterCode,@Param("serverNum") Integer serverNum,@Param("serverCode") int serverCode);

    EdgeCluster selectClusterByClusterCode(String clusterCode);

    /**
     * 根据padCode查询 集群信息
     *
     * @param padCodes padCode集合
     * @return List<EdgeClusterVO>
     */
    List<EdgeClusterVO> selectEdgeClusterByPadCodes(@Param("padCodes") List<String> padCodes);

    String selectEdgeClusterCodeByPadCodeSingle(@Param("padCode") String padCode);

    void updateOnlineStatusById(@Param("id") long id, @Param("onlineStatus") byte onlineStatus);

}
