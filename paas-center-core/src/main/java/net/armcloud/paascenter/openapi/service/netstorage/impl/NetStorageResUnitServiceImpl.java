package net.armcloud.paascenter.openapi.service.netstorage.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;
import net.armcloud.paascenter.common.model.entity.paas.NetStoragePadUnitDetail;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.mapper.EdgeClusterMapper;
import net.armcloud.paascenter.openapi.mapper.NetStoragePadUnitDetailMapper;
import net.armcloud.paascenter.openapi.mapper.NetStorageResUnitMapper;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResUnitDTO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageResUnitVO;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/3/25 18:36
 * @Description:
 */
@Service
@Slf4j
public class NetStorageResUnitServiceImpl extends ServiceImpl<NetStorageResUnitMapper, NetStorageResUnit> implements NetStorageResUnitService {
    @Resource
    private EdgeClusterMapper edgeClusterMapper;

    @Resource
    private NetStoragePadUnitDetailMapper netStoragePadUnitDetailMapper;

    @Override
    public void updateBatchBindByCodeList(List<String> netStorageResCodeList, Integer bindStatus) {
        // 创建更新的包装器
        LambdaUpdateWrapper<NetStorageResUnit> updateWrapper = new LambdaUpdateWrapper<>();

        // 设置为开机
        updateWrapper.in(NetStorageResUnit::getNetStorageResUnitCode, netStorageResCodeList)
                .set(NetStorageResUnit::getShutdownFlag, 1); // 设置createBy为1
        // 执行更新
        this.update(null, updateWrapper);
    }

    @Override
    public Page<NetStorageResUnitVO> netStoragePadResUnit(NetStorageResUnitDTO param) {
        PageHelper.startPage(param.getPage(),param.getRows());
        LambdaQueryWrapper<NetStorageResUnit> queryWrapper = new LambdaQueryWrapper<>();
        if(Objects.nonNull(param.getCustomerId())){
            queryWrapper.eq(NetStorageResUnit::getCustomerId, param.getCustomerId());
        }
        if(StringUtils.isNotEmpty(param.getPadCode())){
            queryWrapper.eq(NetStorageResUnit::getPadCode, param.getPadCode());
        }
        if(StringUtils.isNotEmpty(param.getNetStorageResUnitCode())){
            queryWrapper.eq(NetStorageResUnit::getNetStorageResUnitCode, param.getNetStorageResUnitCode());
        }
        if(StringUtils.isNotEmpty(param.getRemark())){
            queryWrapper.like(NetStorageResUnit::getRemark,param.getRemark());
        }
        List<NetStorageResUnit> list = this.list(queryWrapper);
        Page<NetStorageResUnitVO> pageResult = new Page<>(list, source -> NetStorageResUnitVO.builder((NetStorageResUnit) source));
        return pageResult;
    }



    @Override
    public EdgeCluster getEdgeClusterByCode(String netStorageResUnitCode) {
        LambdaQueryWrapper<NetStorageResUnit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageResUnit::getNetStorageResUnitCode,netStorageResUnitCode);
        NetStorageResUnit one = this.getOne(wrapper);
        if(Objects.isNull(one)){
            return null;
        }
        LambdaQueryWrapper<EdgeCluster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EdgeCluster::getClusterCode,one.getClusterCode()).last("LIMIT 1");
        return edgeClusterMapper.selectOne(queryWrapper);

    }

    @Override
    public Boolean checkBackup(PadDetailsVO padDetailsVO) {
        if(StringUtils.isEmpty(padDetailsVO.getTargetStorageResId())||StringUtils.isEmpty(padDetailsVO.getNetStorageResId())){
            return true;
        }
        NetStorageResUnit resUnit= null;
        NetStorageResUnit targetResUnit = null;
        try {
            LambdaQueryWrapper<NetStorageResUnit> targetWrapper = new LambdaQueryWrapper<>();
            targetWrapper.eq(NetStorageResUnit::getNetStorageResUnitCode,padDetailsVO.getTargetStorageResId()).last("LIMIT 1");
            targetResUnit = this.getOne(targetWrapper);
            LambdaQueryWrapper<NetStorageResUnit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NetStorageResUnit::getNetStorageResUnitCode,padDetailsVO.getNetStorageResId()).last("LIMIT 1");
            resUnit = this.getOne(wrapper);

            //当前存储大小小于克隆的目标存储大小
            return resUnit.getNetStorageResUnitSize()>=targetResUnit.getNetStorageResUnitSize();
        }catch (Exception ex){
            log.error("NetStorageResUnitService.checkBackup error. e:{}",ex.getMessage(),ex);
            // 判断储存备份是否存在
            if(Objects.isNull(targetResUnit) || Objects.isNull(resUnit)){
                throw new BasicException(PadExceptionCode.NET_SYNC_DATA_NOT_EXISTS);
            }
        }
        return false;

    }

        @Override
        public void updateNetStorageResUnitByCode(List<NetStorageResUnit> netStorageResUnitList) {
            if (CollectionUtils.isEmpty(netStorageResUnitList)) {
                return;
            }
            for (NetStorageResUnit unit : netStorageResUnitList) {
                LambdaUpdateWrapper<NetStorageResUnit> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(NetStorageResUnit::getNetStorageResUnitCode, unit.getNetStorageResUnitCode())
                        .set(NetStorageResUnit::getNetStorageResUnitSize, unit.getNetStorageResUnitSize())
                        .set(NetStorageResUnit::getUpdateTime, new Date());
                this.update(null, wrapper);
                // 更新NetStoragePadUnitDetail
                netStoragePadUnitDetailMapper.update(new LambdaUpdateWrapper<NetStoragePadUnitDetail>()
                        .eq(NetStoragePadUnitDetail::getNetStorageResUnitCode,unit.getNetStorageResUnitCode())
                        .set(NetStoragePadUnitDetail::getNetStorageResApplySize,unit.getNetStorageResUnitSize()));
            }
        }

    @Override
    public NetStorageResUnitVO getByCode(String netStorageResUnitCode) {
        NetStorageResUnit netStorageResUnit = this.getOne(new LambdaQueryWrapper<NetStorageResUnit>().eq(NetStorageResUnit::getNetStorageResUnitCode, netStorageResUnitCode));
        if(Objects.nonNull(netStorageResUnit)){
            return NetStorageResUnitVO.builder(netStorageResUnit);
        }
        return null;
    }
}
