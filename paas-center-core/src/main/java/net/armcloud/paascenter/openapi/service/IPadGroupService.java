package net.armcloud.paascenter.openapi.service;


import net.armcloud.paascenter.openapi.model.dto.PadGroupDTO;
import net.armcloud.paascenter.openapi.model.vo.PadGroupVO;

import java.util.List;

public interface IPadGroupService {

    /**
     * 实例分组列表信息
     *
     * @param param PadGroupDTO
     * @return PadListVO
     */
    List<PadGroupVO> padGroupListService(PadGroupDTO param);

    /**
     * 创建分组
     * @param param
     */
    void addPadGroup(PadGroupDTO param);

    /**
     * 删除分组（该分组下不存在板卡实例等数据）
     * @param ids
     */
    void deletePadGroup(List<Long> ids);

    void movePadGroup(PadGroupDTO param);
}
