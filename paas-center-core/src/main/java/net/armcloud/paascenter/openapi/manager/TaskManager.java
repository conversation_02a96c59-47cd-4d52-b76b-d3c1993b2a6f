package net.armcloud.paascenter.openapi.manager;


import net.armcloud.paascenter.common.client.internal.dto.AddBackupTaskDTO;
import net.armcloud.paascenter.common.client.internal.dto.DelPadBackupDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.GetLatestPadDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.RestoreBackupTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.DataDelDTO;
import net.armcloud.paascenter.common.model.dto.api.DataDelMasterDTO;
import net.armcloud.paascenter.common.model.dto.api.UpdateSubTaskDTO;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.openapi.model.bo.AddBackupTaskBO;
import net.armcloud.paascenter.openapi.model.bo.RestoreBackupTaskBO;
import net.armcloud.paascenter.task.service.IPadTaskService;
import net.armcloud.paascenter.task.service.ITaskService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class TaskManager {
    private final ITaskService taskService;
    private final IPadTaskService padTaskService;

    public List<PadTask> addBackupTask(AddBackupTaskBO addBackupTaskBO) {
        List<AddBackupTaskBO.Pad> pads = addBackupTaskBO.getPads();
        List<AddBackupTaskDTO.Pad> addTakPads = new ArrayList<>(pads.size());

        pads.forEach(pad -> {
            AddBackupTaskDTO.Pad padParam = new AddBackupTaskDTO.Pad();
            padParam.setPadCode(pad.getPadCode());
            padParam.setBackupName(pad.getBackupName());
            padParam.setDeviceId(pad.getDeviceId());
            padParam.setSpecificationCode(pad.getSpecificationCode());
            addTakPads.add(padParam);
        });

        AddBackupTaskDTO addBackupTaskDTO = new AddBackupTaskDTO();
        addBackupTaskDTO.setPads(addTakPads);
        addBackupTaskDTO.setCustomerId(addBackupTaskBO.getCustomerId());
        addBackupTaskDTO.setTaskSource(addBackupTaskBO.getTaskSource());
        return taskService.addBackupTask(addBackupTaskDTO);
    }

    public List<PadTask> addRestoreTask(RestoreBackupTaskBO addBackupTaskBO) {
        List<RestoreBackupTaskBO.Pad> pads = addBackupTaskBO.getPads();
        List<RestoreBackupTaskDTO.Pad> addTakPads = new ArrayList<>(pads.size());

        pads.forEach(pad -> {
            RestoreBackupTaskDTO.Pad padParam = new RestoreBackupTaskDTO.Pad();
            padParam.setPadCode(pad.getPadCode());
            padParam.setBackupId(pad.getBackupId());
            padParam.setDeviceId(pad.getDeviceId());
            addTakPads.add(padParam);
        });

        RestoreBackupTaskDTO addTaskDTO = new RestoreBackupTaskDTO();
        addTaskDTO.setPads(addTakPads);
        addTaskDTO.setCustomerId(addBackupTaskBO.getCustomerId());
        addTaskDTO.setTaskSource(addBackupTaskBO.getTaskSource());
        return taskService.addRestoreTask(addTaskDTO);
    }

    public PadBackupTaskInfo getCustomerLatestPadBackupData(String backupName, Long backupId, Long customerId) {
        GetLatestPadDataDTO dto = new GetLatestPadDataDTO();
        dto.setBackupName(backupName);
        dto.setBackupId(backupId);
        dto.setCustomerId(customerId);
        return padTaskService.getCustomerLatestPadBackupData(dto);
    }

    public List<DataDelDTO> delPadBackupData(DataDelMasterDTO param) {
        DelPadBackupDataDTO delPadBackupDataDTO = new DelPadBackupDataDTO();
        delPadBackupDataDTO.setCustomerId(param.getCustomerId());
        delPadBackupDataDTO.setDataDelDTOS(param.getDataDelDTOS());
        return padTaskService.delPadBackupData(delPadBackupDataDTO);
    }

    public TaskManager(ITaskService taskService,IPadTaskService padTaskService) {
        this.taskService = taskService;
        this.padTaskService = padTaskService;
    }

    public void updateSubTaskStatus(long masterTaskId, long subTaskId, int subTaskStatus) {
        updateSubTaskStatus(masterTaskId, subTaskId, subTaskStatus, "", "");
    }

    public void updateSubTaskStatus(long masterTaskId, long subTaskId, int subTaskStatus, String subTaskResult, String errorMsg) {
        UpdateSubTaskDTO dto = new UpdateSubTaskDTO();
        dto.setMasterTaskId(masterTaskId);
        dto.setSubTaskId(subTaskId);
        dto.setSubTaskStatus(subTaskStatus);
        dto.setSubTaskResult(subTaskResult);
        dto.setErrorMsg(errorMsg);
        taskService.updateSubTaskStatus(dto);
    }
}
