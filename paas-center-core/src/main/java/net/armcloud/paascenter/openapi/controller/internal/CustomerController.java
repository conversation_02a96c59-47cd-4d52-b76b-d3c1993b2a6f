package net.armcloud.paascenter.openapi.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.CheckSdkTokenPadDTO;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.client.internal.facade.CustomerInternalFacade;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.common.client.internal.vo.SDKCustomerVO;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
public class CustomerController implements CustomerInternalFacade {
    private final ICustomerService customerService;

    public Result<SDKCustomerVO> verifyAndGetInfo(@Valid @RequestBody VerifyAndGetSDKCustomerDTO dto) {
        return Result.ok(customerService.verifyAndGetInfo(dto));
    }

    @Override
    public Result<Boolean> sdkTokenBindUuid(@Valid @RequestBody VerifyAndGetSDKCustomerDTO dto) {
        return Result.ok(customerService.sdkTokenBindUuidService(dto));
    }

    @Override
    public Result<Boolean> checkSdkTokenBindCustomerAndPad(@Valid @RequestBody CheckSdkTokenPadDTO dto) {
        return Result.ok(customerService.checkSdkTokenBindCustomerAndPad(dto));
    }

    @Override
    public Result<Boolean> checkSdkTokenBindCustomerAndPadV2(CheckSdkTokenPadDTO dto) {
        return Result.ok(customerService.checkSdkTokenBindCustomerAndPadV2(dto));
    }

    public CustomerController(ICustomerService customerService) {
        this.customerService = customerService;
    }

}
