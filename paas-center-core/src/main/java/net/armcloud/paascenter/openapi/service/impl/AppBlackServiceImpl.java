package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.mapper.AppBlackMapper;
import net.armcloud.paascenter.openapi.service.IAppBlackService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.SetUpBlackListDTO;
import net.armcloud.paascenter.common.model.entity.paas.AppBlack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppBlackServiceImpl extends ServiceImpl<AppBlackMapper, AppBlack> implements IAppBlackService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> setUpBlackList(SetUpBlackListDTO param) {
        this.remove(new QueryWrapper<AppBlack>().lambda().eq(AppBlack::getCustomerId, param.getCustomerId()).eq(AppBlack::getSpecificationCode, param.getPadGrade()));

        List<AppBlack> appBlackList = param.getBlackApps().stream().map(blackAppInfo -> {
            AppBlack appBlack = new AppBlack();
            appBlack.setAppName(blackAppInfo.getAppName());
            appBlack.setAppPkg(blackAppInfo.getAppPkg());
            appBlack.setCustomerId(param.getCustomerId());
            appBlack.setSpecificationCode(param.getPadGrade());
            appBlack.setStatus(true);
            appBlack.setCreateBy(String.valueOf(param.getCustomerId()));
            return appBlack;
        }).collect(Collectors.toList());
        return this.saveBatch(appBlackList) ? Result.ok() : Result.fail();
    }
}
