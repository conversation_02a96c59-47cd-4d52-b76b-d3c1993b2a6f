package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;

@Data
public class DeviceItemVO {
    /**
     * 服务器IP
     */
    private String armIp;

    /**
     * 板卡UUID
     */
    private String deviceOutCode;

    /**
     * 刀片编号
     */
    private String nodeId;

    /**
     * 卡槽编号
     */
    private String position;

    /**
     * 板卡状态
     */
    private Integer deviceStatus;

    /**
     * 板卡Code
     */
    private String deviceCode;

    /**
     * 板卡IP
     */
    private String deviceIp;

    /**
     * 子网掩码
     */
    private String netmask;

    /**
     * 网关设置
     */
    private String gateway;

    /**
     * dns配置
     */
    private String dns;

    /**
     * mac地址
     */
    private String macAddress;
}
