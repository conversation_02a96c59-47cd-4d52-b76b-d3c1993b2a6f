package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ADI模板状态更新DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板状态更新对象", description = "用于启用或禁用ADI模板")
public class AdiTemplateStatusDTO {

    @ApiModelProperty(value = "模板ID", required = true, example = "123")
    @NotNull(message = "模板ID不能为空")
    private Long id;

    @ApiModelProperty(value = "状态(0:禁用 1:启用)", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
} 