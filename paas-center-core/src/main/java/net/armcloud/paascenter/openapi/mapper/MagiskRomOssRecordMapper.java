package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.entity.MagiskRomOssRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Magisk ROM OSS记录 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Mapper
public interface MagiskRomOssRecordMapper extends BaseMapper<MagiskRomOssRecord> {

    /**
     * 根据版本查询记录
     *
     * @param version 版本号
     * @return 记录
     */
    MagiskRomOssRecord selectByVersion(@Param("version") String version);

    /**
     * 查询最新创建的记录
     *
     * @return 最新记录
     */
    MagiskRomOssRecord selectLatest();

    /**
     * 更新OSS地址和替换时间
     *
     * @param version 版本号
     * @param ossUrl OSS地址
     * @param updateBy 更新人
     * @return 更新行数
     */
    int updateOssUrlAndReplaceTime(@Param("version") String version, 
                                   @Param("ossUrl") String ossUrl, 
                                   @Param("updateBy") String updateBy);
}
