package net.armcloud.paascenter.openapi.model.dto.netstorage;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/26 17:43
 * @Description:网存实例备份
 */
@Data
public class NetStorageResPadBackupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long customerId;

    /**
     * 备注信息
     */
    @NotNull(message = "remark cannot null")
    private String remark;

    /**
     * 网存存储code
     */
//    @NotNull(message = "netStorageResUnitCodes cannot null")
//    @Size(min = 1, message = "netStorageResUnitCodes cannot null")
//    @Size(max = 200, message = "备份数量不能超过200个")
//    private List<String> netStorageResUnitCodes;

    /**
     * 实例存储code
     */
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "padCodes cannot null")
    @Size(max = 200, message = "备份数量不能超过200个")
    private List<String> padCodes;
}
