package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.vo.*;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.AddDeviceTaskDTO;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import net.armcloud.paascenter.common.model.vo.api.DeviceCustomerVo;
import net.armcloud.paascenter.openapi.model.dto.ApplyDeviceConnectDTO;
import net.armcloud.paascenter.openapi.model.vo.ApplyDeviceConnectVO;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;

import java.util.HashMap;
import java.util.List;

public interface IDeviceService extends IService<Device> {
    public Result<?> deviceRestart(DeviceRestartDTO param);

    Device selectByPadCode(String padCode);

    public Result<?> powerReset(PowerResetDTO param);

    Device selectByDeviceCode(String deviceCode);

    /**
     * 修改云机状态
     *
     * @param param
     */
    void callbackUpdateDevice(DeviceInfoDTO param);

    Result<?> bmcPowerReset(PowerResetDTO param, List<DeviceInfoVo> deviceInfos);

    Result<?> pullModeBmcPowerReset(PowerResetDTO param, List<DeviceInfoVo> deviceInfos);

    /**
     * 新增创建云机任务
     *
     * @param param
     * @return
     */
    List<GenerateDeviceTaskVO> virtualize(VirtualizeDeviceDTO param);

    /**
     * 新增创建云机任务
     *
     * @param param
     * @param resourceSpecification
     * @param customerUploadImage
     * @param screenLayout
     * @param device
     * @param armIpMap
     * @param deviceTaskMap
     */
    void virtualizeDeviceNew(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification, CustomerUploadImage customerUploadImage, ScreenLayout screenLayout, VirtualizeDeviceInfoVO device, HashMap<Long, List<String>> armIpMap, HashMap<String, AddDeviceTaskVO> deviceTaskMap,Boolean isPullMode);

//        /**
//         * 创建云机
//         * @param param
//         * @param resourceSpecification
//         * @param customerUploadImage
//         * @param screenLayout
//         * @param clusterPublicIp
//         * @param deviceList
//         * @param armIpMap
//         * @return
//         */
//    List<AddDeviceTaskVO> virtualizeDevice(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification, CustomerUploadImage customerUploadImage, ScreenLayout screenLayout, String clusterPublicIp, List<VirtualizeDeviceInfoVO> deviceList, HashMap<Long, List<String>> armIpMap);

    /**
     * 更新物理机CMS任务结果
     *
     * @param dto
     */
    void ContainerDeviceTaskResult(ContainerTaskResultVO dto);

    /**
     * 删除云机任务
     *
     * @param param
     * @return
     */
    List<GenerateDeviceTaskVO> deviceDestroy(DeviceDestroyDTO param);

    /**
     * 修改云机状态，发送云机状态回调消息
     */
    Boolean updateDeviceStatusAndSendDeviceStatusCallback(List<String> deviceCodes, Integer status, Long customerId);

    /**
     * 根据物理机IP获取物理机客户信息
     *
     * @param deviceIp
     * @return
     */
    DeviceCustomerVo getDeviceCustomerByDeviceIp(String deviceIp);

    /**
     * 根据物理机编码获取物理机客户信息
     *
     * @param param
     * @return
     */
    List<Device> selectByDeviceCodes(SelectByDeviceCodesDTO param);

    /**
     * 根据物理机IP获取物理机客户信息
     *
     * @param deviceIp
     * @return
     */
    Device selectByDeviceIp(String deviceIp);

    /**
     * 申请板卡连接信息
     */
    List<ApplyDeviceConnectVO> applyDeviceConnect(ApplyDeviceConnectDTO param);

    /**
     * 设置板卡网关
     *
     * @param param
     * @return
     */
    List<GenerateDeviceTaskVO> setDeviceGateway(SetDeviceGatewayDTO param);

    /**
     * 修改物理机信息
     *
     * @param device
     * @return
     */
    Device updateDeviceByCode(Device device);

    /**
     * 获取板卡的挂载版本
     * @return
     */
    List<DeviceInfoVo> getDeviceMountVersionV1();

    /**
     * 查询板卡列表
     * @param param
     * @return
     */
    Page<DeviceQueryDTO> listByDeviceQueryDTO(DeviceQueryListVo param);

    /**
     * cbs更新
     * @param param
     * @return
     */
    List<GenerateDeviceTaskVO> cbsUpdate(DeviceCbsUpdateDTO param);


    List<DeviceVO> getDeviceInfo(List<String> deviceIps);

    List<DeviceVO> getDeviceInfoByCode(List<String> deviceCodes);

    List<String> selectPadByDeviceCode(List<String> deviceCodes);

    /**
     * 查询实例任务
     * @param pads
     * @param status
     * @return
     */
    int selectTaskByTaskTypeAndTaskStatus(List<String> pads, List<Integer> status);


    DeviceVO selectCustomerByDeviceIp(String deviceIp);


    /**
     * 根据物理机IP获取物理机客户信息
     * @param deviceIp 板卡ip
     * @param clusterCode 集群编号
     * @return
     */
    DeviceCustomerVo getDeviceCustomerByDeviceIpAndClusterCode(String deviceIp,String clusterCode);

    /**
     * 更新板卡分配状态
     * @param deviceCode 板卡编号
     * @param allocationStatus 分配状态
     */
    void updateAllocationStatus(String deviceCode,Integer allocationStatus);

    /**
     * 添加板卡任务
     * @param addDeviceTaskDTO
     * @return
     */
    List<AddDeviceTaskVO> pullModeAddDeviceTask(AddDeviceTaskDTO addDeviceTaskDTO);

    /**
     * 获取网存板卡信息
     * @param req
     * @return
     */
    Page<Device> netDeviceLevelList(NetDeviceLevelDTO req);

    /**
     * 设置板卡规格
     * @param param
     * @return
     */
    String setDeviceLevel(DeviceLevelDTO param);

    /**
     * 板卡预热镜像
     * @param param
     * @return
     */
    List<GenerateDeviceTaskVO> boardImageWarmup(DeviceBoardImageWarmupDTO param, SourceTargetEnum sourceTargetEnum);

    void updateCbsInfoById(Long id, String version);
}
