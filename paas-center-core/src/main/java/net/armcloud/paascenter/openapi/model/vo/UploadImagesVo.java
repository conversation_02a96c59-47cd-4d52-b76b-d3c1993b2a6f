package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;

import java.util.List;

@Data
public class UploadImagesVo {

    /**
     * boot 下载链接
     */
    private String bootUrl;

    /**
     * img 下载链接
     */
    private String imgUrl;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * md5
     */
    private String md5;

    /**
     * 安卓版本
     */
    private String platefrom;

    /**
     * ARM服务器IP地址
     */
    private List<String> armIpList;

    /**
     * 服务器Code
     */
    private List<DeviceIp> deviceIpList;

    @Data
    public static class DeviceIp {
        private String armIp;
        private String deviceCode;
    }
}
