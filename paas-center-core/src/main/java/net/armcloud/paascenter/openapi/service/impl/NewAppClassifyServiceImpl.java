package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paascenter.openapi.mapper.CustomerNewAppClassifyMapper;
import net.armcloud.paascenter.openapi.mapper.CustomerNewAppClassifyRelationMapper;
import net.armcloud.paascenter.openapi.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.NewAppClassifyVO;
import net.armcloud.paascenter.openapi.service.INewAppClassifyService;
import net.armcloud.paascenter.common.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerNewAppClassify;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应用分类业务层
 */
@Service
public class NewAppClassifyServiceImpl extends ServiceImpl<CustomerNewAppClassifyMapper, CustomerNewAppClassify> implements INewAppClassifyService {
    @Resource
    private CustomerNewAppClassifyMapper customerNewAppClassifyMapper;
    @Resource
    private CustomerNewAppClassifyRelationMapper customerNewAppClassifyRelationMapper;

    /**
     * 获取应用分类
     * @param param
     * @return
     */
    @Override
    public List<NewAppClassifyVO> list(NewAppClassifyQueryDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<CustomerNewAppClassify> customerNewAppClassifies = customerNewAppClassifyMapper.selectList(new QueryWrapper<>(CustomerNewAppClassify.class)
                .eq("customer_id",param.getCustomerId())
                .like(StrUtil.isNotEmpty(param.getClassifyName()),"classify_name","%" + param.getClassifyName() + "%")
                .eq(param.getEnable()!=null,"enable",param.getEnable()));
        List<NewAppClassifyVO> newAppClassifyVOList = BeanUtil.copyToList(customerNewAppClassifies,NewAppClassifyVO.class);
        return newAppClassifyVOList;
    }

    /**
     * 通过appid查询应用分类
     * @return
     */
    @Override
    public List<NewAppClassifyNameVO> appListByAppIds(QueryNewAppClassifyNameDTO param) {
        return customerNewAppClassifyRelationMapper.selectAppListByAppIds(param);
    }
}
