package net.armcloud.paascenter.openapi.manager.devicenettraversal.utils;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.core.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.manager.devicenettraversal.feign.response.BaseResponse;

import java.util.Objects;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;

@Slf4j
public class FeignUtils {
    private FeignUtils() {
    }

    public static void versify(BaseResponse response) {
        if (Objects.isNull(response)) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "result is null");
        }

        if (!response.success()) {
            log.error("padip result versify fail >>> response:{}", JSON.toJSONString(response));
            throw new BasicException(PROCESSING_FAILED);
        }
    }
}
