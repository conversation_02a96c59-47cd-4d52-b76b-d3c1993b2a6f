package net.armcloud.paascenter.openapi.netpadv2.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 网存实例V2创建记录表
 * 简化版本，只记录padCode和创建时间
 *
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Data
@TableName("net_pad_v2_create_record")
public class NetPadV2CreateRecord {

    /**
     * 实例编码，作为主键
     */
    @TableId
    private String padCode;

    /**
     * 创建时间
     */
    private Date createTime;
}
