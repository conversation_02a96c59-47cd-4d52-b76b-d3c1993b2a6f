package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyPadDetailCheckVO;
import net.armcloud.paascenter.openapi.model.vo.TriggerAppClassifyPadVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyPadRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户黑白名单实例关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Mapper
public interface CustomerAppClassifyPadRelationMapper extends BaseMapper<CustomerAppClassifyPadRelation> {

    int batchInsert(List<CustomerAppClassifyPadRelation> list);

    List<AppClassifyPadDetailCheckVO> selectPadList(@Param("customerId")Long customerId,@Param("padCodes")List<String> padCodes);

    int existsByClassifyType(@Param("customerId")Long customerId,@Param("classifyType")Integer classifyType);

    List<TriggerAppClassifyPadVO> selectAppClassifyPadList(@Param("customerId")Long customerId,@Param("padCodes")List<String> padCodes,@Param("classifyType")Integer classifyType);

    List<CustomerAppClassifyPadRelation> selectAppClassifyPadListByType(@Param("customerId")Long customerId,@Param("padCodes")List<String> padCodes,@Param("classifyType")Integer classifyType);
}
