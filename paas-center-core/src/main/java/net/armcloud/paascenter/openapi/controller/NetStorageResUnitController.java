package net.armcloud.paascenter.openapi.controller;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResUnitDTO;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageResUnitVO;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/3/27 20:57
 * @Description:
 */

@RestController
@RequestMapping("/openapi/open/netStorage/resUnit")
@Api(tags = "存储单元接口")
@Slf4j
public class NetStorageResUnitController {

    @Resource
    private NetStorageResUnitService netStorageResUnitService;

    @PostMapping("/net/storage/pad/resUnit")
    public Result<Page<NetStorageResUnitVO>> netStoragePadResUnit(@Valid @RequestBody NetStorageResUnitDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(netStorageResUnitService.netStoragePadResUnit(param));
    }
}
