package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.command.*;
import net.armcloud.paascenter.common.client.internal.vo.CustomerAppFileVO;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import net.armcloud.paascenter.common.model.vo.api.PadUninstallAppInfoVO;
import net.armcloud.paascenter.openapi.manager.FileManager;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.bo.OperatePadAppBO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyDetailCheckVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyPadDetailCheckVO;
import net.armcloud.paascenter.openapi.service.IPadAppService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify;
import net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.PAD_INSTALLED_APP_PREFIX;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;

@Slf4j
@Service
public class PadAppServiceImpl implements IPadAppService {
    private final PadMapper padMapper;
    private final FileManager fileManager;
    private final RedisService redisService;
    private final CommonPadTaskComponent commonPadTaskComponent;
    private final PadInstalledAppInformationMapper padInstalledAppInformationMapper;
    private final CustomerAppClassifyMapper customerAppClassifyMapper;
    private final CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper;
    private final CustomerAppClassifyPadRelationMapper customerAppClassifyPadRelationMapper;
    private final PadTaskMapper padTaskMapper;

    @Override
    public List<GeneratePadTaskVO> installApp(PadDownloadAppFileDTO param) {
        long customerId = param.getCustomerId();
        CustomerAppFileVO customerAppFile = fileManager.getCustomerAppFile(customerId, param.getAppId(), param.getAppName(), param.getPkgName());

        if (null == customerAppFile) {
            throw new BasicException(FILE_NOT_AVAILABLE_EXCEPTION);
        }

        fileManager.refreshLastUseTime(customerAppFile.getFileId());

        // 查询重复任务的padCode列表
        List<GeneratePadTaskVO> conflictPadCodes = padTaskMapper.findConflictPadCodesForInstallApp(
                customerId, param.getPadCodes(), customerAppFile.getCustomerFileId());
        log.debug("PadAppServiceImpl.installApp.conflictPadCodes: {}", conflictPadCodes);


        // 转换为Map，便于快速查找
        Set<String> conflictPadCodesSet;
        if(!CollectionUtils.isEmpty(conflictPadCodes)){
            conflictPadCodesSet =   conflictPadCodes.stream().map(GeneratePadTaskVO::getPadCode).collect(Collectors.toSet());
        } else {
            conflictPadCodesSet = new HashSet<>();
        }
        // 创建返回结果列表
        List<GeneratePadTaskVO> resultList = new ArrayList<>();

        // 未冲突的padCode列表，用于创建新任务
        List<String> nonConflictPadCodes = param.getPadCodes().stream()
                .filter(padCode -> !conflictPadCodesSet.contains(padCode))
                .collect(Collectors.toList());

        SourceTargetEnum sourceTarget = Optional.of(param).map(PadDownloadAppFileDTO::getTaskSource).orElse(SourceTargetEnum.PAAS);
        String oprBy = Optional.of(param).map(PadDownloadAppFileDTO::getOprBy).orElse(String.valueOf(customerId));


        List<GeneratePadTaskVO> distinctList = conflictPadCodes.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                GeneratePadTaskVO::getPadCode,  // 以 padCode 作为 key 去重
                                Function.identity(),  // value 为原对象
                                (existing, replacement) -> existing // 冲突时保留第一个
                        ),
                        map -> new ArrayList<>(map.values())
                ));
     /*   // 处理冲突的padCode
        for (PadTask task : distinctList) {
            GeneratePadTaskVO vo = new GeneratePadTaskVO();
            vo.setPadCode(task.getPadCode());
            vo.setTaskStatus(task.getStatus());
            vo.setTaskId(task.getCustomerTaskId());
            resultList.add(vo);
        }*/
        if(!CollectionUtils.isEmpty(distinctList)){
            resultList.addAll(distinctList);
        }

        
        // 只有在有非冲突的padCode时才创建新任务
        if (!nonConflictPadCodes.isEmpty()) {
            String fileName = UuidUtils.generateUuid();
            if(StrUtil.isNotEmpty(customerAppFile.getAppName())){
                fileName = customerAppFile.getAppName();
            }
            String cmdFileName = fileName;
            PadCMDForwardDTO padCmdForward = new PadDownloadAppCMDDTO()
                    .setPath(customerAppFile.getPublicUrl())
                    .setFileId(customerAppFile.getFileId())
                    .setInstall(true)
                    .setFileName(cmdFileName)
                    .setIconUrl(customerAppFile.getIconUrl())
                    .setPackageName(customerAppFile.getPackageName())
                    .setIsAuthorization(param.getIsGrantAllPerm())
                    .builderForwardDTO(nonConflictPadCodes, sourceTarget, oprBy);
            padCmdForward.setCustomerFileId(customerAppFile.getCustomerFileId());

            Consumer<AddPadTaskDTO> padTaskConsumer = addPadTaskDTO -> addPadTaskDTO.setCustomerFileId(customerAppFile.getCustomerFileId());

            PadTaskBO padTaskBO = commonPadTaskComponent.addPadTask(customerId, nonConflictPadCodes, DOWNLOAD_APP, padTaskConsumer, JSON.toJSONString(padCmdForward), sourceTarget,null);
            List<GeneratePadTaskVO> newTaskVOs = GeneratePadTaskVO.builder(padTaskBO);
            
            // 设置新任务的taskStatus为1（任务已添加）
            for (GeneratePadTaskVO vo : newTaskVOs) {
                vo.setTaskStatus(1); // 表示任务已添加
            }
            
            resultList.addAll(newTaskVOs);
        }
        
        return resultList;
    }

    @Override
    public List<GeneratePadTaskVO> uninstallApp(PadUninstallAppFileDTO param) {
        String appId = param.getAppId();
        String packageName = param.getPkgName();
        String appName = param.getAppName();
        List<String> padCodes = param.getPadCodes();
        long customerId = param.getCustomerId();

        PadUninstallAppCMDDTO uninstallAppDto = new PadUninstallAppCMDDTO().setPackageName(param.getPkgName());
        CustomerAppFileVO customerAppFile = null;
        Consumer<AddPadTaskDTO> padTaskConsumer = null;
        /*//管理平台卸载app不校验文件是否存在
        if(!SourceTargetEnum.ADMIN_SYSTEM.equals(param.getTaskSource())){
            customerAppFile = fileManager.getCustomerAppFile(customerId, appId, appName, packageName);
            if (null == customerAppFile) {
                throw new BasicException(UNINSTALL_APP_FAIL_EXCEPTION);
            }
            uninstallAppDto.setFileId(customerAppFile.getFileId());
        }else{
            padTaskConsumer = addPadTaskDTO -> {
                PadUninstallAppInfoVO padUninstallAppInfoVO = new PadUninstallAppInfoVO();
                padUninstallAppInfoVO.setAppName(param.getAppName());
                padUninstallAppInfoVO.setPackageName(param.getPkgName());
                padUninstallAppInfoVO.setVersionName(param.getAppVersionName());
                addPadTaskDTO.setTaskContent(JSON.toJSONString(padUninstallAppInfoVO));
            };
        }*/

        //无appid则不校验文件是否存在
        if(StrUtil.isNotEmpty(param.getAppId())){
            customerAppFile = fileManager.getCustomerAppFile(customerId, appId, appName, packageName);
            if (null == customerAppFile) {
                throw new BasicException(UNINSTALL_APP_FAIL_EXCEPTION);
            }
            uninstallAppDto.setFileId(customerAppFile.getFileId());
            uninstallAppDto.setPackageName(customerAppFile.getPackageName());
            uninstallAppDto.setAppName(customerAppFile.getAppName());
            String dbAppName = customerAppFile.getAppName();
            String dbPkgName = customerAppFile.getPackageName();
            String dbAppVersionName = customerAppFile.getAppVersionName();
            padTaskConsumer = addPadTaskDTO -> {
                PadUninstallAppInfoVO padUninstallAppInfoVO = new PadUninstallAppInfoVO();
                padUninstallAppInfoVO.setAppName(dbAppName);
                padUninstallAppInfoVO.setPackageName(dbPkgName);
                padUninstallAppInfoVO.setVersionName(dbAppVersionName);
                addPadTaskDTO.setTaskContent(JSON.toJSONString(padUninstallAppInfoVO));
            };
        }else{
            padTaskConsumer = addPadTaskDTO -> {
                PadUninstallAppInfoVO padUninstallAppInfoVO = new PadUninstallAppInfoVO();
                padUninstallAppInfoVO.setAppName(param.getAppName());
                padUninstallAppInfoVO.setPackageName(param.getPkgName());
                padUninstallAppInfoVO.setVersionName(param.getAppVersionName());
                addPadTaskDTO.setTaskContent(JSON.toJSONString(padUninstallAppInfoVO));
            };
        }

        SourceTargetEnum sourceTarget = Optional.of(param).map(PadUninstallAppFileDTO::getTaskSource).orElse(SourceTargetEnum.PAAS);
        String oprBy = Optional.of(param).map(PadUninstallAppFileDTO::getOprBy).orElse(String.valueOf(customerId));
        PadCMDForwardDTO padCMDForwardDTO = uninstallAppDto.builderForwardDTO(padCodes, sourceTarget, (customerAppFile != null ?customerAppFile.getCustomerFileId():null), oprBy);
        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(customerId, param.getPadCodes(), UNINSTALL_APP, padCMDForwardDTO,padTaskConsumer);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> startApp(PadAppPackageNameDTO param) {
        PadCMDForwardDTO padCMDForwardDTO = new PadStartAppCMDDTO()
                .setPackageName(param.getPkgName())
                .builderForwardDTO(param.getPadCodes(), PAAS);

        OperatePadAppBO operatePadAppBO = new OperatePadAppBO();
        operatePadAppBO.setPkgName(param.getPkgName());

        padCMDForwardDTO.setTaskContent(JSON.toJSONString(operatePadAppBO));

        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(), START_APP, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> stopApp(PadAppPackageNameDTO param) {
        PadCMDForwardDTO padCMDForwardDTO = new PadStopAppCMDDTO()
                .setPackageName(param.getPkgName())
                .builderForwardDTO(param.getPadCodes(), PAAS);

        OperatePadAppBO operatePadAppBO = new OperatePadAppBO();
        operatePadAppBO.setPkgName(param.getPkgName());

        padCMDForwardDTO.setTaskContent(JSON.toJSONString(operatePadAppBO));

        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(), STOP_APP, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<GeneratePadTaskVO> restartApp(PadAppPackageNameDTO param) {
        PadCMDForwardDTO padCMDForwardDTO = new PadRestartAppCMDDTO()
                .setPackageName(param.getPkgName())
                .builderForwardDTO(param.getPadCodes(), PAAS);

        OperatePadAppBO operatePadAppBO = new OperatePadAppBO();
        operatePadAppBO.setPkgName(param.getPkgName());

        padCMDForwardDTO.setTaskContent(JSON.toJSONString(operatePadAppBO));

        PadTaskBO padTaskBO = commonPadTaskComponent.addPadCMDTask(param.getCustomerId(), param.getPadCodes(), RESTART_APP, padCMDForwardDTO);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<PadInstalledAppVO> listInstalledApp(PadCodesDTO param) {
        long startTime = System.currentTimeMillis();
        List<String> padCodes = param.getPadCodes();

        try {
            // 1. 批量从Redis获取缓存数据
            Map<String, Object> cachedData = batchGetFromRedis(padCodes);

            // 2. 找出缓存未命中的padCode
            List<String> missedPadCodes = padCodes.stream()
                    .filter(padCode -> !cachedData.containsKey(PAD_INSTALLED_APP_PREFIX + padCode))
                    .collect(Collectors.toList());

            // 3. 批量从数据库查询未命中的数据
            Map<String, PadInstalledAppInformation> dbData = Collections.emptyMap();
            if (!missedPadCodes.isEmpty()) {
                dbData = batchGetFromDatabase(missedPadCodes);
                // 4. 批量写入Redis缓存
                batchSetToRedis(dbData, missedPadCodes);
            }

            // 5. 组装结果
            List<PadInstalledAppVO> result = buildResult(padCodes, cachedData, dbData);

            long executionTime = System.currentTimeMillis() - startTime;
            if (executionTime > 5000) {
                log.warn("listInstalledApp执行时间过长: {}ms, padCodes数量: {}", executionTime, padCodes.size());
            }

            return result;
        } catch (Exception e) {
            log.error("listInstalledApp执行异常, padCodes: {}", padCodes, e);
            throw e;
        }
    }

    /**
     * 批量从Redis获取缓存数据
     */
    private Map<String, Object> batchGetFromRedis(List<String> padCodes) {
        List<String> keys = padCodes.stream()
                .map(padCode -> PAD_INSTALLED_APP_PREFIX + padCode)
                .collect(Collectors.toList());

        return redisService.multiGet(keys);
    }

    /**
     * 批量从数据库查询数据
     */
    private Map<String, PadInstalledAppInformation> batchGetFromDatabase(List<String> padCodes) {
        List<PadInstalledAppInformation> dbResults = padInstalledAppInformationMapper.batchGetByPadCodes(padCodes);
        return dbResults.stream()
                .collect(Collectors.toMap(
                    PadInstalledAppInformation::getPadCode,
                    Function.identity(),
                    (existing, replacement) -> existing
                ));
    }

    /**
     * 批量写入Redis缓存
     */
    private void batchSetToRedis(Map<String, PadInstalledAppInformation> dbData, List<String> missedPadCodes) {
        Map<String, String> cacheData = new HashMap<>();

        for (String padCode : missedPadCodes) {
            String key = PAD_INSTALLED_APP_PREFIX + padCode;
            PadInstalledAppInformation info = dbData.get(padCode);

            if (info != null) {
                cacheData.put(key, info.getAppsJSON());
            } else {
                cacheData.put(key, "");
            }
        }

        if (!cacheData.isEmpty()) {
            // 使用不同的过期时间：有数据的缓存1小时，空数据缓存10分钟
            Map<String, String> longTermCache = new HashMap<>();
            Map<String, String> shortTermCache = new HashMap<>();

            cacheData.forEach((key, value) -> {
                if (StringUtils.isNotBlank(value)) {
                    longTermCache.put(key, value);
                } else {
                    shortTermCache.put(key, value);
                }
            });

            if (!longTermCache.isEmpty()) {
                redisService.setBatchCacheObject(longTermCache, 1L, TimeUnit.HOURS);
            }
            if (!shortTermCache.isEmpty()) {
                redisService.setBatchCacheObject(shortTermCache, 10L, TimeUnit.MINUTES);
            }
        }
    }

    /**
     * 构建最终结果
     */
    private List<PadInstalledAppVO> buildResult(List<String> padCodes,
                                               Map<String, Object> cachedData,
                                               Map<String, PadInstalledAppInformation> dbData) {
        return padCodes.stream()
                .map(padCode -> {
                    PadInstalledAppVO vo = new PadInstalledAppVO();
                    vo.setPadCode(padCode);

                    String key = PAD_INSTALLED_APP_PREFIX + padCode;
                    Object cachedObj = cachedData.get(key);

                    if (cachedObj != null) {
                        // 从缓存获取数据
                        String cachedStr = cachedObj.toString();
                        if (StringUtils.isNotBlank(cachedStr)) {
                            vo.setApps(JSONArray.parseArray(cachedStr, PadInstalledAppVO.App.class));
                        } else {
                            vo.setApps(Collections.emptyList());
                        }
                    } else {
                        // 从数据库获取数据
                        PadInstalledAppInformation dbInfo = dbData.get(padCode);
                        if (dbInfo != null && StringUtils.isNotBlank(dbInfo.getAppsJSON())) {
                            vo.setApps(JSON.parseArray(dbInfo.getAppsJSON(), PadInstalledAppVO.App.class));
                        } else {
                            vo.setApps(Collections.emptyList());
                        }
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 黑白名单过滤
     * @param param
     * @return key=padCode value=任务备注  禁止执行的padCode 需要生成任务但无需执行的实例
     */
    private Map<String,String> appClassifyFilter(PadDownloadAppFileDTO param){
        //校验黑白名单
        //规则
        //一切规则生效的前提，是该实例有没有绑定黑名单/白名单的分类
        //黑名单：抖音
        //白名单：抖音、淘宝
        //1、AC01实例关联了黑名单，未关联白名单
        //结果：AC01实例不可安装抖音，其他应用均可安装
        //2、AC01实例关联了白名单，未关联黑名单
        //结果：AC01实例仅能安装抖音、淘宝，其他应用均无法安装
        //3、AC01实例既关联了黑名单，也关联了白名单
        //结果：AC01实例不能安装抖音，只能安装淘宝
        //判断当前用户是否配置了黑白名单
        Map<String,String> prohibitPadCodeMap = null;
        Long targetCustomerId = param.getCustomerId();

        Boolean customerAppClassifyExists = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("customer_id",targetCustomerId)
                .eq("delete_flag",0));
        if(customerAppClassifyExists){
            //客户是否配置黑白名单实例
            Integer whiteCustomerAppClassifyPadExist = customerAppClassifyPadRelationMapper.existsByClassifyType(targetCustomerId,1);
            Integer blackCustomerAppClassifyPadExist = customerAppClassifyPadRelationMapper.existsByClassifyType(targetCustomerId,2);

            //黑白名单均未配置 则直接返回
            if(whiteCustomerAppClassifyPadExist == 0 && blackCustomerAppClassifyPadExist == 0){
                return null;
            }
            String errMsg = "未命中黑白名单规则";
            //需要过滤的padCode
            prohibitPadCodeMap = new HashMap<>();

            //查询出该用户下所有的分类实例 然后校验实例编号
            List<AppClassifyPadDetailCheckVO> appClassifyPadDetailCheckVOList = customerAppClassifyPadRelationMapper.selectPadList(targetCustomerId,param.getPadCodes());

            //这批padCode中配置了白名单的关联实例
            List<AppClassifyPadDetailCheckVO> whiteAppClassifyPadDetailCheckVOList = new ArrayList<>();
            //这批padCode中配置了白名单的关联实例 用于判断是否padCode是否配置了白名单
            Map<String,String> whiteAppClassifyPadDetailCheckVOMap = new HashMap<>();
            //这批padCode中配置了黑名单的关联实例
            List<AppClassifyPadDetailCheckVO> blackAppClassifyPadDetailCheckVOList = new ArrayList<>();
            //这批padCode中配置了黑名单的关联实例 用于判断是否padCode是否配置了黑名单
            Map<String,String> blackAppClassifyPadDetailCheckVOMap = new HashMap<>();
            //记录padCode所有关联的黑白名单id
            Map<String,List<Long>> padCodeAppClassifyMap = new HashMap<>();
            if(CollUtil.isNotEmpty(appClassifyPadDetailCheckVOList)){
                for(AppClassifyPadDetailCheckVO appClassifyPadDetailCheckVO : appClassifyPadDetailCheckVOList){
                    if(appClassifyPadDetailCheckVO.getClassifyType() == 1){
                        whiteAppClassifyPadDetailCheckVOList.add(appClassifyPadDetailCheckVO);
                        whiteAppClassifyPadDetailCheckVOMap.put(appClassifyPadDetailCheckVO.getPadCode(),"1");
                    }else if(appClassifyPadDetailCheckVO.getClassifyType() == 2){
                        blackAppClassifyPadDetailCheckVOList.add(appClassifyPadDetailCheckVO);
                        blackAppClassifyPadDetailCheckVOMap.put(appClassifyPadDetailCheckVO.getPadCode(),"2");
                    }
                    if(padCodeAppClassifyMap.get(appClassifyPadDetailCheckVO.getPadCode()) == null){
                        padCodeAppClassifyMap.put(appClassifyPadDetailCheckVO.getPadCode(),new ArrayList<>());
                    }
                    padCodeAppClassifyMap.get(appClassifyPadDetailCheckVO.getPadCode()).add(appClassifyPadDetailCheckVO.getAppClassifyId());
                }
            }

            for(String padCode : param.getPadCodes()){
                //是否允许安装
                Boolean padIsAllowInstall = true;
                //命中实例黑白名单 则去校验应用黑白名单
                Boolean isWhite = whiteAppClassifyPadDetailCheckVOMap.containsKey(padCode);
                Boolean isBlack = blackAppClassifyPadDetailCheckVOMap.containsKey(padCode);
                if(isWhite || isBlack){
                    //拿到这个padCode所有黑白名单id
                    List<Long> appClassifyIds = padCodeAppClassifyMap.get(padCode);
                    //查询该padCode下所有关联分类id的关联应用
                    //todo 如果padCodes量很大 这里可能会造成总查询时间过长 后续可优化
                    List<AppClassifyDetailCheckVO> appClassifyDetailCheckVOList = customerAppClassifyRelationMapper.selectAppList(targetCustomerId,appClassifyIds);
                    Map<Long,String> whiteAppClassifyDetailCheckVOMap = new HashMap<>();
                    Map<Long,String> blackAppClassifyDetailCheckVOMap = new HashMap<>();
                    if(CollUtil.isNotEmpty(appClassifyPadDetailCheckVOList)){
                        for(AppClassifyDetailCheckVO appClassifyDetailCheckVO : appClassifyDetailCheckVOList){
                            if(appClassifyDetailCheckVO.getClassifyType() == 1){
                                whiteAppClassifyDetailCheckVOMap.put(appClassifyDetailCheckVO.getAppId(),"");
                            }else if(appClassifyDetailCheckVO.getClassifyType() == 2){
                                blackAppClassifyDetailCheckVOMap.put(appClassifyDetailCheckVO.getAppId(),"");
                            }
                        }

                    }

                    Long appId = Long.parseLong(param.getAppId());
                    //关联了黑名单 未关联白名单 -> 命中黑名单 则该应用不能安装
                    //关联了白名单 未关联黑名单 -> 未命中白名单 则该应用不能安装
                    //关联了白名单 关联了黑名单 -> 命中黑名单 则不能安装该应用 未命中黑名单 则只能安装命中白名单的应用
                    if(blackAppClassifyDetailCheckVOMap.containsKey(appId)){
                        padIsAllowInstall = false;
                    }
                    if(padIsAllowInstall && CollUtil.isNotEmpty(whiteAppClassifyDetailCheckVOMap) && !whiteAppClassifyDetailCheckVOMap.containsKey(appId)){
                        padIsAllowInstall = false;
                    }
                }

                if(!padIsAllowInstall){
                    prohibitPadCodeMap.put(padCode,errMsg);
                }
            }

        }
        return prohibitPadCodeMap;
    }

    public PadAppServiceImpl(FileManager fileManager, PadMapper padMapper, RedisService redisService,
                             CommonPadTaskComponent commonPadTaskComponent, PadInstalledAppInformationMapper padInstalledAppInformationMapper,
                             CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper,
                             CustomerAppClassifyPadRelationMapper customerAppClassifyPadRelationMapper,
                             CustomerAppClassifyMapper customerAppClassifyMapper,
                             PadTaskMapper padTaskMapper) {
        this.fileManager = fileManager;
        this.padMapper = padMapper;
        this.redisService = redisService;
        this.commonPadTaskComponent = commonPadTaskComponent;
        this.padInstalledAppInformationMapper = padInstalledAppInformationMapper;
        this.customerAppClassifyRelationMapper = customerAppClassifyRelationMapper;
        this.customerAppClassifyPadRelationMapper = customerAppClassifyPadRelationMapper;
        this.customerAppClassifyMapper = customerAppClassifyMapper;
        this.padTaskMapper = padTaskMapper;
    }
}
