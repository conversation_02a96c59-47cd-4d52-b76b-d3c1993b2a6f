package net.armcloud.paascenter.openapi.model.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 导入通话记录请求参数
 */
@Data
public class CallRecordsDTO {

    private Long customerId;
    
    /**实例编号 必填*/
    @Size(min = 1, max = 200, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;
    
    /**通话记录列表*/
    @Size(min = 1, message = "通话记录数量不少于1个")
    @NotNull(message = "callRecords cannot null")
    private List<CallRecord> callRecords;

    @Data
    @Valid
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CallRecord {
        /**电话号码*/
        @NotNull(message = "number cannot null")
        private String number;
        
        /**
         * 通话类型
         * 1: 拨出
         * 2: 接听
         * 3: 未接
         */
        @NotNull(message = "inputType cannot null")
        private Integer inputType;
        
        /**通话时长(秒)*/
        @NotNull(message = "duration cannot null")
        private Integer duration;
        
        /**通话时间字符串 格式：yyyy-MM-dd HH:mm:ss*/
        private String timeString;
    }
} 