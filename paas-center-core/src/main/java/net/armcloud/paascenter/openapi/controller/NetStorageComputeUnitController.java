package net.armcloud.paascenter.openapi.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.NetStorageComputeDTO;
import net.armcloud.paascenter.openapi.model.vo.NetStorageComputeVO;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/26 11:27
 * @Description:
 */
@RestController
@RequestMapping("/openapi/open/netStorage/computeUnit")
@Api(tags = "算力单元接口")
@Slf4j
public class NetStorageComputeUnitController {
    @Resource
    private NetStorageComputeUnitService netStorageComputeUnitService;

//    @RequestMapping(value = "/ retrieve", method = RequestMethod.POST)
//    @ApiOperation(value = "获取算力使用详情", httpMethod = "POST", notes = "获取算力使用详情")
//    public List<NetStorageComputeVO> retrieveNetStorageComputeUsage(@RequestBody @Valid NetStorageComputeDTO param){
//        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
//       return netStorageComputeUnitService.retrieveNetStorageComputeUsage(param);
//
//    }


}
