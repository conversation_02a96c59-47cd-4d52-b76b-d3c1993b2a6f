package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.DefaultRootAppConfig;
import net.armcloud.paascenter.openapi.model.vo.DefaultRootAppVO;

/**
 * 默认root应用
 */
public interface IDefaultRootAppConfigService extends IService<DefaultRootAppConfig> {

    /**
     * 获取默认root应用列表
     * @return
     */
    DefaultRootAppVO getDefaultRootAppList();
}
