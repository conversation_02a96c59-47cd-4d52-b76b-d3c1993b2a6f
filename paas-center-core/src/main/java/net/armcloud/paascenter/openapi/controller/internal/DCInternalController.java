package net.armcloud.paascenter.openapi.controller.internal;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.facade.DCInternalFacade;
import net.armcloud.paascenter.openapi.service.IDCService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class DCInternalController implements DCInternalFacade {

    private final IDCService dcService;

    @Override
    public Result<DcInfo> getByPadCode(String padCode) {
        return Result.ok(dcService.getByPadCode(padCode));
    }

    @Override
    public Result<List<DcInfo>> listByCustomerId(long customerId) {
        log.error("DCInternalController listByCustomerId ?????????????");
        return Result.ok();
//        return Result.ok(dcService.listByCustomerId(customerId));
    }

    @Override
    public Result<DcInfo> getByDcId(long dcId) {
        return Result.ok(dcService.getByDcId(dcId));
    }

    @Override
    public Result<List<DcInfo>> selectList() {
        return Result.ok(dcService.list());
    }

    public DCInternalController(IDCService dcService) {
        this.dcService = dcService;
    }
}
