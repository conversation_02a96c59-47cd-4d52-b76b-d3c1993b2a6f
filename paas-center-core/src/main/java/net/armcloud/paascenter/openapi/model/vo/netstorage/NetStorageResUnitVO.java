package net.armcloud.paascenter.openapi.model.vo.netstorage;

import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/28 13:48
 * @Description:
 */
@Data
public class NetStorageResUnitVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long netStorageResUnitId; // 网络存储详情 ID

    private Integer shutdownFlag; // 是否有实例开关机 0: 关机,1: 开机

    private String netStorageResUnitCode; // 网络存储详情 Code

    private String clusterCode; // 集群 Code

    private String padCode; // 实例 Code

    private Long netStorageResUnitSize; // 网络存储大小 (单位:GB)

    public static NetStorageResUnitVO builder(NetStorageResUnit netStorageResUnitDTO){
        NetStorageResUnitVO unitVO = new NetStorageResUnitVO();
        unitVO.setNetStorageResUnitId(netStorageResUnitDTO.getNetStorageResUnitId());
//        unitVO.setCustomerId(netStorageResUnitDTO.getCustomerId());
        unitVO.setShutdownFlag(netStorageResUnitDTO.getShutdownFlag());
        unitVO.setNetStorageResUnitCode(netStorageResUnitDTO.getNetStorageResUnitCode());
        unitVO.setClusterCode(netStorageResUnitDTO.getClusterCode());
        unitVO.setPadCode(netStorageResUnitDTO
                .getPadCode());
        unitVO.setNetStorageResUnitSize(netStorageResUnitDTO.getNetStorageResUnitSize());
        return unitVO;

    }

}
