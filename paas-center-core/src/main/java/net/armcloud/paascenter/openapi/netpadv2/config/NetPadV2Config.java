package net.armcloud.paascenter.openapi.netpadv2.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.netpadv2.utils.SnowflakeIdGeneratorV3;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 网存实例V2配置类
 *
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Slf4j
@Data
@Configuration
public class NetPadV2Config {

    @Resource
    private RedisService redisService;


    /**
     * 配置雪花算法ID生成器V3 - 16位优化版
     */
    @Bean
    public SnowflakeIdGeneratorV3 snowflakeIdGeneratorV3() {
        log.info("初始化雪花算法ID生成器V3 (16位优化版)");
        return SnowflakeIdGeneratorV3.getInstance();
    }
}
