package net.armcloud.paascenter.openapi.netpadv2.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 网存实例开机参数记录表
 * 记录网存实例开机时使用的存储、算力、板卡编号、板卡IP、集群、服务器，
 * 以及开机是否成功、开机耗时、开机时间、关机时间
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Data
@TableName("net_pad_last_on_param")
@ApiModel(value = "NetPadLastOnParam", description = "网存实例开机参数记录")
public class NetPadLastOnParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码，主键
     */
    @ApiModelProperty(value = "实例编码")
    @TableId(value = "pad_code")
    private String padCode;

    /**
     * 板卡编号
     */
    @ApiModelProperty(value = "板卡编号")
    private String deviceCode;

    /**
     * 板卡IP
     */
    @ApiModelProperty(value = "板卡IP")
    private String deviceIp;

    /**
     * 集群编码
     */
    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    /**
     * 服务器编码
     */
    @ApiModelProperty(value = "服务器编码")
    private String armServerCode;

    /**
     * 算力编号
     */
    @ApiModelProperty(value = "算力编号")
    private String computeUnitCode;

    /**
     * 开机是否成功 0:失败 1:成功
     */
    @ApiModelProperty(value = "开机是否成功 0:失败 1:成功")
    private Integer bootOnSuccess;

    /**
     * 开机耗时（毫秒）
     */
    @ApiModelProperty(value = "开机耗时（毫秒）")
    private Long bootOnRunTime;

    /**
     * 开机时间
     */
    @ApiModelProperty(value = "开机时间")
    private Date bootOnTime;

    /**
     * 关机时间
     */
    @ApiModelProperty(value = "关机时间")
    private Date offTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建工厂方法
     */
    public static NetPadLastOnParam create(String padCode, String deviceCode, String deviceIp,
                                          String clusterCode, String armServerCode, String computeUnitCode) {
        NetPadLastOnParam entity = new NetPadLastOnParam();
        entity.setPadCode(padCode);
        entity.setDeviceCode(deviceCode);
        entity.setDeviceIp(deviceIp);
        entity.setClusterCode(clusterCode);
        entity.setArmServerCode(armServerCode);
        entity.setComputeUnitCode(computeUnitCode);
        entity.setBootOnTime(new Date());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        return entity;
    }

    /**
     * 更新开机成功状态和耗时
     */
    public void updateBootOnSuccess(boolean success, Long runTime) {
        this.bootOnSuccess = success ? 1 : 0;
        this.bootOnRunTime = runTime;
        this.updateTime = new Date();
    }

    /**
     * 更新关机时间
     */
    public void updateOffTime() {
        this.offTime = new Date();
        this.updateTime = new Date();
    }
}
