package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyDetailVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyPadDetailVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyVO;

import java.util.List;

/**
 * 黑白名单业务层 - 接口
 */
public interface IAppClassifyService extends IService<CustomerAppClassify> {

    List<AppClassifyVO> list(AppClassifyQueryDTO param);

    /**
     * 获取黑白名单详情
     * @param id
     * @return
     */
    AppClassifyDetailVO detail(Long id, Long customerId);

    /**
     * 保存编辑黑白名单
     * @param param
     * @return
     */
    Long save(AppClassifySaveDTO param);

    /**
     * 添加黑白名单app
     * @param param
     * @return
     */
    void addApp(AppClassifyAddAppDTO param);

    /**
     * 黑白名单关联实例详情
     * @param id
     * @return
     */
    AppClassifyPadDetailVO padDetail(Long id, Long customerId);

    /**
     * 黑白名单关联实例保存编辑
     * @param param
     * @return
     */
    void padSave(AppClassifyPadSaveDTO param);

    /**
     * 增加黑白名单实例关联
     * @param param
     * @return
     */
    void addPad(AppClassifyAddPadDTO param);

    /**
     * 删除黑白名单
     * @param id
     * @return
     */
    void del(Long id,Long customerId);

    /**
     * 删除黑白名单实例关联
     * @param param
     * @return
     */
    void delPad(AppClassifyDelPadDTO param);
}
