package net.armcloud.paascenter.openapi.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.service.BaseInfoService;

@RestController
@RequestMapping("/openapi/open/info")
@Api(tags = "开放信息查询接口")
public class OpenInfoController {

    @Autowired
    private BaseInfoService baseInfoService;

    @GetMapping("/country")
    @ApiOperation(value = "获取支持的国家列表", notes = "返回支持的国家编码和国家名称")
    public Result<List<Map<String, String>>> getSupportedCountries() {
        List<Map<String, String>> countries = baseInfoService.getAllSupportedCountries();
        return Result.ok(countries);
    }
    
    // 可以在这里添加其他信息查询接口
    // 例如：/openapi/open/info/language, /openapi/open/info/currency 等
} 