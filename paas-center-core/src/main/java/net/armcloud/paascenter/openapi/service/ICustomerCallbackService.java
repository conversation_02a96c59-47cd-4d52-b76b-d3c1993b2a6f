package net.armcloud.paascenter.openapi.service;

import net.armcloud.paascenter.common.client.internal.dto.CustomerCallbackDTO;
import net.armcloud.paascenter.common.client.internal.vo.CustomerCallbackVO;
import net.armcloud.paascenter.common.core.domain.Result;

import java.util.List;

public interface ICustomerCallbackService {
    Result<?> insertCallback(List<CustomerCallbackDTO> list, Long customerId);

    Integer DeleteCallback(List<Long> ids);

    Result<?> updateCallback(List<CustomerCallbackDTO> list,Long customerId);

    List<CustomerCallbackVO> selectList();

    Result<?> selectCallback(long userId);
}
