package net.armcloud.paascenter.openapi.model.vo;

import net.armcloud.paascenter.common.model.entity.paas.PadPropertiesSub;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PadPropertiesVO implements Serializable {

    /**
     * 云机编号
     */
    @ApiModelProperty(value = "实例编号")
    private String padCode;

    /**
     * Modem-属性列表
     */
    private List<PadPropertiesSub> modemPropertiesList;

    /**
     * 系统-属性列表
     */
    private List<PadPropertiesSub> systemPropertiesList;

    /**
     * setting-属性列表
     */
    private List<PadPropertiesSub> settingPropertiesList;

    /**
     * oaid-属性列表
     */
    private List<PadPropertiesSub> oaidPropertiesList;


}
