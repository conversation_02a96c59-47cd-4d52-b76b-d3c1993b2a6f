package net.armcloud.paascenter.openapi.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.MessageFormat;

@Slf4j
public class CmdUtil {

    public static ProxyDetectionResponse proxyDetection(String url, String ip, Integer port, String username, String password, boolean isS5){
        ProxyDetectionResponse proxyDetectionResponse = new ProxyDetectionResponse();
        proxyDetectionResponse.setConnStatus(false);
        String curlCommand = "curl --connect-timeout 5 ";
        if(isS5){
            curlCommand = curlCommand + " --socks5 {0}:{1} ";
        }else{
            curlCommand = curlCommand + " -x {0}:{1} ";
        }
        curlCommand = MessageFormat.format(curlCommand, ip, String.valueOf(port));
        if(StrUtil.isNotEmpty(username) && StrUtil.isNotEmpty(password)){
            curlCommand = curlCommand + " --proxy-user " + username+":"+password;
        }
        curlCommand = curlCommand + " " + url;
        log.debug("CmdUtil proxyDetection cmd:{}",curlCommand);

        try {
            // 执行curl命令
            Process process = Runtime.getRuntime().exec(curlCommand);
            // 读取标准输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder sbReader = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sbReader.append(line);
            }

            // 读取标准错误
            StringBuilder sbError = new StringBuilder();
            BufferedReader stdError = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            while ((line = stdError.readLine()) != null) {
                sbError.append(line);
            }

            // 等待命令执行完成
            process.waitFor();
            String strError = stdError.toString();
            if(StrUtil.isNotEmpty(strError)){
                try{
                    //97表示未连接上代理服务器 28表示已连接上但是指定的网站未接收到数据
                   if(strError.contains("curl: (97)")){
                       proxyDetectionResponse.setConnStatus(false);
                   }else if(strError.contains("curl: (28)")){
                       proxyDetectionResponse.setConnStatus(true);
                   }
                }catch (Exception e){

                }
            }

            String strReader = sbReader.toString();
            if(StrUtil.isNotEmpty(strReader)){
                try{
                    String exportIp = JSONObject.parseObject(strReader).getOrDefault("ip","") + "";
                    proxyDetectionResponse.setConnStatus(true);
                    proxyDetectionResponse.setExportIp(exportIp);
                }catch (Exception e){

                }
            }

        } catch (Exception e) {
            log.error("CmdUtil proxyDetection error",e);
        }
        return proxyDetectionResponse;
    }
}
