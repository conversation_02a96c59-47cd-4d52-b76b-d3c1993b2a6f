package net.armcloud.paascenter.openapi.netpadv2.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 实例与算力单元关联关系表
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Data
@TableName("net_pad_compute_unit_relation")
public class NetPadComputeUnitRelationDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 实例编码
     */
    private String padCode;

    /**
     * 算力单元编码
     */
    private String netStorageComputeUnitCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
