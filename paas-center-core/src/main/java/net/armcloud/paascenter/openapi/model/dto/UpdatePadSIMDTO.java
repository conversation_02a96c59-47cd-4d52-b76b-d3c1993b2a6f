package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePadSIMDTO extends PadCodesDTO {
    private String imei;
    private String imeisv;
    private String meid;
    private String operatorLongname;
    private String operatorShortnam;
    private String operatorNumeric;
    private String spn;
    private String iccid;
    private String imsi;
    private String phonenum;
    private String netCountry;
    private String simCountry;

    @NotBlank(message = "type cannot null")
    private String type;

    @NotBlank(message = "mcc cannot null")
    private String mcc;

    @NotBlank(message = "mnc cannot null")
    private String mnc;

    @NotBlank(message = "tac cannot null")
    private String tac;

    @NotBlank(message = "cellid cannot null")
    private String cellid;

    @NotBlank(message = "narfcn cannot null")
    private String narfcn;

    @NotBlank(message = "physicalcellid cannot null")
    private String physicalcellid;
}
