package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import net.armcloud.paascenter.common.client.internal.vo.SDKCustomerVO;
import net.armcloud.paascenter.openapi.mapper.CustomerPoliciesMapper;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.common.client.internal.dto.CheckSdkTokenPadDTO;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.model.entity.paas.CustomerPolicies;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.Constants.CONSOLE_UUID;

@Service
@Slf4j
public class CustomerServiceImpl implements ICustomerService {
    private final CustomerPoliciesMapper customerPoliciesMapper;
    private final RedisService redisService;
    private final IPadService padService;

    @Override
    public SDKCustomerVO verifyAndGetInfo(VerifyAndGetSDKCustomerDTO dto) {
        String token = dto.getSdkToken();
        String key = RedisKeyPrefix.SDK_TOKEN + token;
        String value = redisService.getCacheObject(key);
        if (StringUtils.isBlank(value)) {
            throw new BasicException(BasicExceptionCode.INVALID_TOKEN);
        }

        //sdkToken与uuid绑定且检查是否一致
        if (!sdkTokenBindUuidService(dto)) {
            throw new BasicException(BasicExceptionCode.UUID_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_UUID);
        }

        long customerId = Long.parseLong(value.split(Constants.UNDERLINE)[0]);
        SDKCustomerVO sdkCustomerVO = new SDKCustomerVO();
        sdkCustomerVO.setCustomerId(customerId);

        List<Integer> customerPoliciesTypes = customerPoliciesMapper.getByCustomerIdAndType(customerId).stream()
                .map(CustomerPolicies::getType).collect(Collectors.toList());
        sdkCustomerVO.setPoliciesTypes(customerPoliciesTypes);
        return sdkCustomerVO;
    }

    @Override
    public Boolean sdkTokenBindUuidService(VerifyAndGetSDKCustomerDTO dto) {
        String key = RedisKeyPrefix.SDK_TOKEN_CLIENT_UUID + dto.getSdkToken();
        String clientUuid = redisService.getCacheObject(key);

        if (ObjectUtil.isNull(clientUuid)) {
            String tokenKey = RedisKeyPrefix.SDK_TOKEN + dto.getSdkToken();
            long expire = redisService.getExpire(tokenKey);
            if (dto.getUuid().startsWith(CONSOLE_UUID)) {
                expire = NumberConsts.SIXTY;
            }
            redisService.setCacheObject(key, dto.getUuid(), expire, TimeUnit.SECONDS);
        } else {
            return clientUuid.equals(dto.getUuid());
        }
        return true;
    }

    @Override
    public Boolean checkSdkTokenBindCustomerAndPad(CheckSdkTokenPadDTO dto) {
        String key = RedisKeyPrefix.SDK_TOKEN + dto.getSdkToken();
        String value = redisService.getCacheObject(key);
        if (StringUtils.isBlank(value)) {
            throw new BasicException(BasicExceptionCode.INVALID_TOKEN);
        }
        long customerId = Long.parseLong(value.split(Constants.UNDERLINE)[0]);

         return padService.checkPadListOwnerService(customerId, Collections.singletonList(dto.getPadCode()));
    }

    @Override
    public Boolean checkSdkTokenBindCustomerAndPadV2(CheckSdkTokenPadDTO dto) {
        String key = RedisKeyPrefix.SDK_TOKEN + dto.getSdkToken();
        String value = redisService.getCacheObject(key);
        if (StringUtils.isBlank(value)) {
            throw new BasicException(BasicExceptionCode.INVALID_TOKEN);
        };
        String[] split = value.split(Constants.UNDERLINE);
        int length = split.length;
        String padCode = split[length - 1];
        // 判断验证的实例信息跟token绑定的实例信息是否一致
        if(!Objects.equals(padCode, dto.getPadCode())){
            log.error("CustomerServiceImpl.checkSdkTokenBindCustomerAndPadV2 check pad result is false");
            return false;
        }
        long customerId = Long.parseLong(value.split(Constants.UNDERLINE)[0]);
        return padService.checkPadListOwnerService(customerId, Collections.singletonList(dto.getPadCode()));

    }

    public CustomerServiceImpl(CustomerPoliciesMapper customerPoliciesMapper, RedisService redisService, IPadService padService) {
        this.customerPoliciesMapper = customerPoliciesMapper;
        this.redisService = redisService;
        this.padService = padService;
    }

}
