package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.common.model.entity.paas.NetPad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface NetPadMapper {

    List<String> selectIpv4CidrsByArmServer(@Param("armServerId") Long armServerId);

    List<NetPad> selectByIds(@Param("netPadIds") List<Long> netPadIds);


    void updateNetDeviceBindFlag(@Param("deviceSubnet")String deviceSubnet,@Param("bindFlag") byte bindFlag);

    void updateNetPadBindFlag(@Param("netPadIds")List<Long> netPadIds, @Param("bindFlag")Byte bindFlag);

    List<NetPad> selectNetPadByIpv4OrNameExcludingId(@Param("ipv4Cidr")String ipv4Cidr, @Param("name")String name, @Param("id")Long id);


    void saveNetPad(NetPad param);

}
