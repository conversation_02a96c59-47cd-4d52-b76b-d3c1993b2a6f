package net.armcloud.paascenter.openapi.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import net.armcloud.paascenter.common.client.internal.dto.CustomerCallbackDTO;
import net.armcloud.paascenter.common.client.internal.dto.CustomerCallbackPassDTO;
import net.armcloud.paascenter.common.client.internal.vo.CustomerCallbackVO;
import net.armcloud.paascenter.common.client.internal.vo.CustomerSelectCallbackVO;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.service.ICustomerCallbackService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/openapi/open/config")
@Api(tags = "服务器配置")
@Slf4j
public class ConfigController {

    @Resource
    private ICustomerCallbackService customerCallbackService;
//    @RequestMapping(value = "/insertCallback", method = RequestMethod.POST)
//    @ApiOperation(value = "新增回调" ,httpMethod = "POST",notes = "新增回调")
//    public Result<?> insertCallback(@RequestBody List<CustomerCallbackDTO> list) {
//        long userId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
//        return customerCallbackService.insertCallback(list,userId);
//    }



    @RequestMapping(value = "/insertCallback", method = RequestMethod.POST)
    @ApiOperation(value = "新增回调" ,httpMethod = "POST",notes = "新增回调")
    public Result<?> insertCallbackIds(@RequestBody CustomerCallbackPassDTO customerCallbackPassDTO) {
        List<CustomerCallbackVO> customerCallbackVOS = customerCallbackService.selectList();
        if(CollectionUtils.isEmpty(customerCallbackVOS)){
            return Result.fail("无可用的回调type");
        };
        List<Long> requestIdList = customerCallbackPassDTO.getCallbackIdList();
        List<Long> typeIdList = customerCallbackVOS.stream().map(customerCallbackVO -> customerCallbackVO.getId()).collect(Collectors.toList());
        Set<Long> requestIdSet = requestIdList.stream().collect(Collectors.toSet());
        Set<Long> typeIdSet = typeIdList.stream().collect(Collectors.toSet());

        // 求差集：requestIdList - typeIdList
        Set<Long> difference = Sets.difference(requestIdSet, typeIdSet);
        if(CollectionUtils.isNotEmpty(difference)){
            return Result.fail("回调iD【"+difference.toString()+"】不存在");
        };
        long userId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        List<CustomerCallbackDTO>  insertList = new ArrayList<>();
        customerCallbackPassDTO.getCallbackIdList().forEach(id ->{
            CustomerCallbackDTO customerCallbackDTO = new CustomerCallbackDTO();
            customerCallbackDTO.setCustomerId(customerCallbackPassDTO.getCustomerId());
            customerCallbackDTO.setCallbackId(id);
            customerCallbackDTO.setCallbackUrl(customerCallbackPassDTO.getCallbackUrl());
            insertList.add(customerCallbackDTO);
        });
        return customerCallbackService.insertCallback(insertList,userId);
    }
    @RequestMapping(value = "/deleteCallback", method = RequestMethod.POST)
    @ApiOperation(value = "删除回调" ,httpMethod = "POST",notes = "删除回调(传用户ID)")
    public Result<?> DeleteCallback() {
        long userId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        return Result.ok(customerCallbackService.DeleteCallback(Lists.newArrayList(userId)));
    }

    @RequestMapping(value = "/updateCallback", method = RequestMethod.POST)
    @ApiOperation(value = "修改回调" ,httpMethod = "POST",notes = "修改回调")
    public Result<?> updateCallback(@RequestBody CustomerCallbackPassDTO customerCallbackPassDTO) {
        List<CustomerCallbackVO> customerCallbackVOS = customerCallbackService.selectList();
        if(CollectionUtils.isEmpty(customerCallbackVOS)){
            return Result.fail("无可用的回调type");
        };
        List<Long> requestIdList = customerCallbackPassDTO.getCallbackIdList();
        List<Long> typeIdList = customerCallbackVOS.stream().map(customerCallbackVO -> customerCallbackVO.getId()).collect(Collectors.toList());
        Set<Long> requestIdSet = requestIdList.stream().collect(Collectors.toSet());
        Set<Long> typeIdSet = typeIdList.stream().collect(Collectors.toSet());

        // 求差集：requestIdList - typeIdList
        Set<Long> difference = Sets.difference(requestIdSet, typeIdSet);
        if(CollectionUtils.isNotEmpty(difference)){
            return Result.fail("回调iD【"+difference.toString()+"】不存在");
        };
        long userId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        List<CustomerCallbackDTO>  insertList = new ArrayList<>();
        customerCallbackPassDTO.getCallbackIdList().forEach(id ->{
            CustomerCallbackDTO customerCallbackDTO = new CustomerCallbackDTO();
            customerCallbackDTO.setCustomerId(customerCallbackPassDTO.getCustomerId());
            customerCallbackDTO.setCallbackId(id);
            customerCallbackDTO.setCallbackUrl(customerCallbackPassDTO.getCallbackUrl());
            insertList.add(customerCallbackDTO);
        });
        return customerCallbackService.updateCallback(insertList,userId);
    }

    @RequestMapping(value = "/selectCallback", method = RequestMethod.GET)
    @ApiOperation(value = "查询回调地址" ,httpMethod = "GET",notes = "查询回调地址")
    public Result<?> selectCallback() {
        long userId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        return customerCallbackService.selectCallback( userId);
    }

    @RequestMapping(value = "/selectList", method = RequestMethod.GET)
    @ApiOperation(value = "回调列表" ,httpMethod = "GET",notes = "回调列表")
    public Result<List<CustomerSelectCallbackVO>> selectList() {
        log.info("selectListselectListselectListselectList");
        List<CustomerCallbackVO> customerCallbackVOS = customerCallbackService.selectList();
        List<CustomerSelectCallbackVO> vos = Optional.ofNullable(customerCallbackVOS)
                .orElse(Collections.emptyList())
                .stream()
                .map(customerCallbackVO -> {
                    CustomerSelectCallbackVO vo = new CustomerSelectCallbackVO();
                    vo.setId(customerCallbackVO.getId());
                    vo.setCallbackType(customerCallbackVO.getCallbackType());
                    vo.setCallbackName(customerCallbackVO.getCallbackName());
                    return vo;
                })
                .collect(Collectors.toList());
        return Result.ok(vos);
    }
}
