package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.openapi.model.dto.NetServerDTO;
import net.armcloud.paascenter.openapi.model.vo.NetServerVO;
import net.armcloud.paascenter.common.model.entity.paas.NetServer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NetServerMapper {
    List<NetServerVO> listNetServer(NetServerDTO param);

    void saveNetServer(NetServer param);

    void updateNetServer(NetServer param);

    void deleteNetServer(Long id);

    List<NetServer> selectListNetServer();

    NetServer selectNetServerByIpv4(String ipv4Cidr);
    List<NetServer> selectNetServerByIpv4OrNameExcludingId(@Param("ipv4Cidr")String ipv4Cidr,@Param("name")String name,@Param("id")Long id);

    void updateNetServerBindFlag(@Param("deviceSubnet") String deviceSubnet,@Param("bindFlag") Byte bindFlag);

    NetServer selectById(Long id);

    NetServerVO selectVoById(Long id);

    List<NetServerVO> listNetServerNotBind();
}
