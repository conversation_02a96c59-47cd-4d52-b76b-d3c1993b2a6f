package net.armcloud.paascenter.openapi.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 网络存储单元删除记录表
 * <AUTHOR>
 * @Date 2025/6/25
 * @Description: 用于记录被删除的网络存储单元信息
 */
@Data
@TableName("net_storage_res_unit_deleted")
public class NetStorageResUnitDeleted implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 原记录ID
     */
    private Long originalId;

    /**
     * 网络存储详情ID
     */
    private Long netStorageResUnitId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 是否有实例开关机 0: 关机,1: 开机
     */
    private Integer shutdownFlag;

    /**
     * 网络存储详情Code
     */
    private String netStorageResUnitCode;

    /**
     * 集群Code
     */
    private String clusterCode;

    /**
     * 实例Code
     */
    private String padCode;

    /**
     * 网络存储大小(单位:GB)
     */
    private Long netStorageResUnitSize;

    /**
     * 备注
     */
    private String remark;

    /**
     * 原记录创建时间
     */
    private Date createTime;

    /**
     * 原记录更新时间
     */
    private Date updateTime;

    /**
     * 原记录创建者
     */
    private String createBy;

    /**
     * 原记录更新者
     */
    private String updateBy;

    /**
     * 删除时间
     */
    private Date deletedTime;

    /**
     * 删除操作者
     */
    private String deletedBy;

    /**
     * 删除原因
     */
    private String deleteReason;
}
