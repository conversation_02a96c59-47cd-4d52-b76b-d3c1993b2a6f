package net.armcloud.paascenter.openapi.model.dto.padnetowrk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ProxyDetectionDTO {
    @ApiModelProperty(hidden = true)
    private Long customerId;
    private String padCode;
    @NotBlank(message = "proxyHost cannot null")
    private String proxyHost;
    @NotNull(message = "proxyPort cannot null")
    private Integer proxyPort;
    private String proxyAccount;
    private String proxyPwd;

    /**
     * 代理类型 1socks5 2http
     */
    private Integer proxyType;
}
