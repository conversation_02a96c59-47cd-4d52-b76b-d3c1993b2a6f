package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import net.armcloud.paascenter.common.client.internal.UpdateDeviceVersionDTO;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.service.CbsService;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.CARD_DOES_NOT_EXIST;

/**
 * <AUTHOR>
 * @Date 2025/2/17 17:53
 * @Version 1.0
 */
@Service
@Slf4j
public class CbsServiceImpl implements CbsService {

    private final DeviceMapper deviceMapper;

    public CbsServiceImpl(DeviceMapper deviceMapper) {
        this.deviceMapper = deviceMapper;
    }

    @Override
    public void updateDevice(UpdateDeviceVersionDTO updateDeviceVersionDTO) {
        log.info("CBS主动更新板卡版本设备IP：{}", updateDeviceVersionDTO.getDeviceIp());
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_ip", updateDeviceVersionDTO.getDeviceIp());
        queryWrapper.eq("delete_flag", NumberConsts.ZERO);
        Device device = deviceMapper.selectOne(queryWrapper);
        log.info("CBS主动更新板卡版本息：{}", device);
        if(device == null){
           throw new BasicException(CARD_DOES_NOT_EXIST);
       }
        LambdaUpdateWrapper<Device> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Device::getId, device.getId());
        lambdaUpdateWrapper.set(Device::getDebianBootInfo, updateDeviceVersionDTO.getDebianBootInfo());
        lambdaUpdateWrapper.set(Device::getDebianSysInfo, updateDeviceVersionDTO.getDebianSysInfo());
        lambdaUpdateWrapper.set(Device::getCbsInfo, updateDeviceVersionDTO.getCbsInfo());
        lambdaUpdateWrapper.set(Device::getExtLifeTimeInfo, updateDeviceVersionDTO.getExtLifeTimeInfo());

        int updateResult = deviceMapper.update(lambdaUpdateWrapper);
        log.info("CBS主动更新板卡版本设备更新结果：{}", updateResult);
    }
}
