package net.armcloud.paascenter.openapi.netpadv2.factory;

import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * NetPad V2 初始化数据工厂类
 * 用于创建开机相关的各种实体对象和DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-07
 */
@Component
public class NetPadV2InitDataFactory {


    /**
     * 创建DevicePad实体
     */
    public DevicePad createDevicePad(Long deviceId, Long padId) {
        DevicePad devicePad = new DevicePad();
        devicePad.setDeviceId(deviceId);
        devicePad.setPadId(padId);
        return devicePad;
    }

    /**
     * 创建NetStorageResOffLog实体
     */
    public NetStorageResOffLog createNetStorageResOffLog(PadDetailsVO padDetailsVO, String deviceIp) {
        NetStorageResOffLog storageResOffLog = new NetStorageResOffLog();
        storageResOffLog.setPadCode(padDetailsVO.getPadCode());
        storageResOffLog.setDeviceIp(deviceIp);
        storageResOffLog.setNetStorageResId(padDetailsVO.getNetStorageResId());
        storageResOffLog.setCreateTime(new Date());
        return storageResOffLog;
    }

    /**
     * 创建Pad实体用于更新IP
     */
    public Pad createPadForIpUpdate(Long padId, String padCode, String padIp, String armServerCode) {
        Pad pad = new Pad();
        pad.setId(padId);
        pad.setPadCode(padCode);
        pad.setPadIp(padIp);
        pad.setArmServerCode(armServerCode);
        return pad;
    }

    public NetPadLastOnParam createNetPadLastOnParam(String padCode, String deviceCode, String deviceIp,
                                                     String clusterCode, String armServerCode, String computeUnitCode) {
        return NetPadLastOnParam.create(padCode, deviceCode, deviceIp, clusterCode, armServerCode, computeUnitCode);
    }
}
