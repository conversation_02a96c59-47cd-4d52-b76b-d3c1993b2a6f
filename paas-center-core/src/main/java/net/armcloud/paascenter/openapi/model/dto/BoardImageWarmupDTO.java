package net.armcloud.paascenter.openapi.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/19 14:50
 * @Description:
 */
@Data
@Builder
public class BoardImageWarmupDTO {
    /**
     * 镜像下载地址
     */
    String url;

    /**
     * harbor账号
     */
    String harborUserName;

    /**
     * harbor密码
     */
    String harborAuthPassWord;

    /**
     * 等待时间
     */
    long sleepTime;
}
