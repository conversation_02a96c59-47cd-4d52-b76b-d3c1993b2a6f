package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Magisk ROM OSS记录新增请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@ApiModel(value = "MagiskRomOssRecordCreateDTO", description = "Magisk ROM OSS记录新增请求")
public class MagiskRomOssRecordCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * oss地址
     */
    @ApiModelProperty(value = "oss地址", required = true, example = "https://example.oss.com/magisk/v1.0.0.zip")
    @NotBlank(message = "oss地址不能为空")
    private String ossUrl;

    /**
     * 包版本
     */
    @ApiModelProperty(value = "包版本", required = true, example = "v1.0.0")
    @NotBlank(message = "包版本不能为空")
    private String version;
}
