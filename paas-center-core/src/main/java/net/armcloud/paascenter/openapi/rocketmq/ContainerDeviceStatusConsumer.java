package net.armcloud.paascenter.openapi.rocketmq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants;
import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceStatusMQ;
import net.armcloud.paascenter.common.model.vo.api.DeviceCustomerVo;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.task.service.ITaskService;
import net.armcloud.paascenter.task.service.impl.TaskService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Objects;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

/**
 * topic : vcp_cms_device_status
 */
@Slf4j
@Service
@AliRocketMQMsgListener(topic = "${mq.container-device-status-message.topic}", consumerGroup = "${mq.container-device-status-message.group}")
public class ContainerDeviceStatusConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private TaskService taskService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("ContainerDeviceStatusConsumer onMessage:{},MessageId:{}", str, messageView.getMessageId());

        ContainerDeviceStatusMQ dto = JSON.parseObject(str, ContainerDeviceStatusMQ.class);
        //消息幂等
        String key = RedisKeyPrefix.VIRTUALIZE_DEVICE_STATUS_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.tryLock(key, 0, 5);
        if (Objects.isNull(lock)) {
            log.info("ContainerDeviceStatusConsumer not get lock onMessage:{},MessageId:{}", str, messageView.getMessageId());
            return;
        }
        try {
            DeviceCustomerVo deviceCustomerVo = deviceService.getDeviceCustomerByDeviceIpAndClusterCode(dto.getDeviceIp(),dto.getClusterCode());
            if (isNotEmpty(deviceCustomerVo)) {
                if(!deviceCustomerVo.getDeviceStatus().equals(dto.getDeviceStatus())){
                    deviceService.updateDeviceStatusAndSendDeviceStatusCallback(Collections.singletonList(deviceCustomerVo.getDeviceCode()), dto.getDeviceStatus(), deviceCustomerVo.getCustomerId());
                }
                //板卡上线 更新相应的任务状态
               /* if(DeviceStatusConstants.DEVICE_SUCCESS.getStatus().equals(dto.getDeviceStatus())){
                    taskService.updateSpecialDeviceTaskStatus(deviceCustomerVo.getDeviceCode());
                }*/

                if (StrUtil.isNotBlank(dto.getVersion()) && !Objects.equals(deviceCustomerVo.getCbsInfo(), dto.getVersion())) {
                    deviceService.updateCbsInfoById(deviceCustomerVo.getId(),dto.getVersion());
                }
            }

        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }
}
