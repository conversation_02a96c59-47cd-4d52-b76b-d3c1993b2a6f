package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.NetPadMapper;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.service.INetPadService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.NetPad;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.NAME_OR_IPV4CIDR_EXIST;

@Service
public class NetPadServiceImpl implements INetPadService {
    @Resource
    private NetPadMapper netPadMapper;
    @Resource
    private CustomerMapper customerMapper;

    @Override
    public Result<?> saveNetPad(NetPad param) {
        List<NetPad> netPads = netPadMapper.selectNetPadByIpv4OrNameExcludingId(param.getIpv4Cidr(), param.getName(),null);
        if (CollUtil.isNotEmpty(netPads)) {
            throw new BasicException(NAME_OR_IPV4CIDR_EXIST);
        }
        param.setBindFlag(ClusterAndNetConstant.NOT_BOUND);
        param.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
        param.setCreateBy(getCustomerInfoById(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest())).getCustomerName());
        param.setCreateTime(new Date());
        netPadMapper.saveNetPad(param);
        return Result.ok();
    }
    private CustomerInfoVo getCustomerInfoById(Long customerId) {
        return customerMapper.getCustomerInfoById(customerId);
    }

}
