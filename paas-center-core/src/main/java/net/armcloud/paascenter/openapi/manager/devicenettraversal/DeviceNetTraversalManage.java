package net.armcloud.paascenter.openapi.manager.devicenettraversal;

import net.armcloud.paascenter.openapi.manager.devicenettraversal.feign.response.DeviceNetTraversalResponse;
import net.armcloud.paascenter.openapi.manager.devicenettraversal.utils.FeignUtils;
import org.springframework.stereotype.Component;

@Component
public class DeviceNetTraversalManage {

    public DeviceNetTraversalResponse traversal(long customerId, String deviceIp) {
        String username = "" + System.currentTimeMillis() + customerId;
        // todo  待接入
        DeviceNetTraversalResponse deviceNetTraversalResponse = new DeviceNetTraversalResponse();
        deviceNetTraversalResponse.setCommand("ssh -oHostKeyAlgorithms=+ssh-rsa ************_changsha_1720175813@*************** -p 40021 -L 5555:************:5555 -Nf");
        deviceNetTraversalResponse.setKey("ls2QnjQJLv0pvpeB5oVAuRWMVrrPV6xaTxe6cFqR3QQPgFwTXMaIjzZTDmN9uQCZh1xWADazAQ5zFDvbsCjOJg==");
        deviceNetTraversalResponse.setUsername(username);
        deviceNetTraversalResponse.setStatus(200);

        FeignUtils.versify(deviceNetTraversalResponse);
        return deviceNetTraversalResponse;
    }

}
