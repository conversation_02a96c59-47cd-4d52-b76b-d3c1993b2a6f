package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.SimInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * sim卡信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Mapper
public interface SimInfoMapper extends BaseMapper<SimInfo> {

   // 根据国家随机一个sim卡信息
   SimInfo selectRandomSimInfo(@Param("countryCode") String countryCode);

}
