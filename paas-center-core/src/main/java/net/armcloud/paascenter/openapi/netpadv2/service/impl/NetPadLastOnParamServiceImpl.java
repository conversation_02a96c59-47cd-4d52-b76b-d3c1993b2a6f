package net.armcloud.paascenter.openapi.netpadv2.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam;
import net.armcloud.paascenter.openapi.netpadv2.mapper.NetPadLastOnParamMapper;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadLastOnParamService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 网存实例开机参数记录服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Slf4j
@Service
public class NetPadLastOnParamServiceImpl implements NetPadLastOnParamService {

    @Resource
    private NetPadLastOnParamMapper netPadLastOnParamMapper;

    @Override
    public void recordBootOnParams(String padCode, String deviceCode, String deviceIp,
                                  String clusterCode, String armServerCode, String computeUnitCode) {
        try {
            NetPadLastOnParam record = NetPadLastOnParam.create(padCode, deviceCode, deviceIp,
                                                               clusterCode, armServerCode, computeUnitCode);
            netPadLastOnParamMapper.insert(record);
            log.info("记录网存实例开机参数成功，padCode: {}, deviceCode: {}, deviceIp: {}, clusterCode: {}, armServerCode: {}, computeUnitCode: {}",
                    padCode, deviceCode, deviceIp, clusterCode, armServerCode, computeUnitCode);
        } catch (Exception e) {
            log.error("记录网存实例开机参数失败，padCode: {}, deviceCode: {}, deviceIp: {}, clusterCode: {}, armServerCode: {}, computeUnitCode: {}",
                     padCode, deviceCode, deviceIp, clusterCode, armServerCode, computeUnitCode, e);
        }
    }

    @Override
    public void batchRecordBootOnParams(List<NetPadLastOnParam> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        
        try {
            netPadLastOnParamMapper.batchInsertOrUpdate(records);
            log.info("批量记录网存实例开机参数成功，记录数量: {}", records.size());
        } catch (Exception e) {
            log.error("批量记录网存实例开机参数失败，记录数量: {}", records.size(), e);
        }
    }

    @Override
    public void updateBootOnResult(String padCode, boolean success, Date startTime) {
        try {
            Long runTime = null;
            if (success && startTime != null) {
                runTime = System.currentTimeMillis() - startTime.getTime();
            }
            
            int result = netPadLastOnParamMapper.updateBootOnSuccess(padCode, success ? 1 : 0, runTime);
            if (result > 0) {
                log.info("更新网存实例开机结果成功，padCode: {}, success: {}, runTime: {}ms", 
                        padCode, success, runTime);
            } else {
                log.warn("更新网存实例开机结果失败，未找到记录，padCode: {}", padCode);
            }
        } catch (Exception e) {
            log.error("更新网存实例开机结果失败，padCode: {}, success: {}", padCode, success, e);
        }
    }

    @Override
    public void batchUpdateBootOnResult(List<String> padCodes, boolean success) {
        if (CollectionUtils.isEmpty(padCodes)) {
            return;
        }
        
        try {
            for (String padCode : padCodes) {
                netPadLastOnParamMapper.updateBootOnSuccess(padCode, success ? 1 : 0, null);
            }
            log.info("批量更新网存实例开机结果成功，实例数量: {}, success: {}", padCodes.size(), success);
        } catch (Exception e) {
            log.error("批量更新网存实例开机结果失败，实例数量: {}, success: {}", padCodes.size(), success, e);
        }
    }

    @Override
    public void updateOffTime(String padCode) {
        try {
            int result = netPadLastOnParamMapper.updateOffTime(padCode, new Date());
            if (result > 0) {
                log.info("更新网存实例关机时间成功，padCode: {}", padCode);
            } else {
                log.warn("更新网存实例关机时间失败，未找到记录，padCode: {}", padCode);
            }
        } catch (Exception e) {
            log.error("更新网存实例关机时间失败，padCode: {}", padCode, e);
        }
    }

    @Override
    public void batchUpdateOffTime(List<String> padCodes) {
        if (CollectionUtils.isEmpty(padCodes)) {
            return;
        }
        
        try {
            int result = netPadLastOnParamMapper.batchUpdateOffTime(padCodes, new Date());
            log.info("批量更新网存实例关机时间成功，实例数量: {}, 影响行数: {}", padCodes.size(), result);
        } catch (Exception e) {
            log.error("批量更新网存实例关机时间失败，实例数量: {}", padCodes.size(), e);
        }
    }

    @Override
    public void deleteByPadCode(String padCode) {
        try {
            int result = netPadLastOnParamMapper.deleteByPadCode(padCode);
            if (result > 0) {
                log.info("删除网存实例开机参数记录成功，padCode: {}", padCode);
            } else {
                log.warn("删除网存实例开机参数记录失败，未找到记录，padCode: {}", padCode);
            }
        } catch (Exception e) {
            log.error("删除网存实例开机参数记录失败，padCode: {}", padCode, e);
        }
    }

    @Override
    public void batchDeleteByPadCodes(List<String> padCodes) {
        if (CollectionUtils.isEmpty(padCodes)) {
            return;
        }
        
        try {
            int result = netPadLastOnParamMapper.batchDeleteByPadCodes(padCodes);
            log.info("批量删除网存实例开机参数记录成功，实例数量: {}, 影响行数: {}", padCodes.size(), result);
        } catch (Exception e) {
            log.error("批量删除网存实例开机参数记录失败，实例数量: {}", padCodes.size(), e);
        }
    }

    @Override
    public NetPadLastOnParam getByPadCode(String padCode) {
        try {
            return netPadLastOnParamMapper.selectByPadCode(padCode);
        } catch (Exception e) {
            log.error("查询网存实例开机参数记录失败，padCode: {}", padCode, e);
            return null;
        }
    }

    @Override
    public List<NetPadLastOnParam> getByPadCodes(List<String> padCodes) {
        if (CollectionUtils.isEmpty(padCodes)) {
            return null;
        }
        
        try {
            return netPadLastOnParamMapper.selectByPadCodes(padCodes);
        } catch (Exception e) {
            log.error("批量查询网存实例开机参数记录失败，实例数量: {}", padCodes.size(), e);
            return null;
        }
    }
}
