package net.armcloud.paascenter.openapi.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class GeneratePreviewDTO {
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于1个")
    private List<String> padCodes;

    /**
     * 截图画面横竖屏旋转
     * <p>
     * 0：截图方向不做处理，默认
     * 1：截图画面旋转为竖屏：
     * 手机竖屏的截图，不做处理
     * 手机横屏的截图，截图顺时针旋转90度
     */
    @Min(value = 0, message = "rotation参数值不正确")
    @Min(value = 1, message = "rotation参数值不正确")
    private Integer rotation = 0;

    /**
     * 事件是否广播
     */
    private Boolean broadcast = false;
}
