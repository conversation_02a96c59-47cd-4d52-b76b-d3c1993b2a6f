package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.cms.model.request.InstanceResetRequest;
import net.armcloud.paascenter.common.client.internal.dto.NetStorageResDetailDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.client.internal.vo.PadImageDetailVO;
import net.armcloud.paascenter.common.client.internal.vo.PadInfoVO;
import net.armcloud.paascenter.common.model.dto.api.NetStorageResMoreCompatiblePaasDTO;
import net.armcloud.paascenter.openapi.model.dto.PadListDTO;
import net.armcloud.paascenter.openapi.model.dto.PadListOptimizedDTO;
import net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo;
import net.armcloud.paascenter.openapi.model.vo.*;
import net.armcloud.paascenter.common.model.dto.api.PadCustomerDTO;
import net.armcloud.paascenter.common.model.dto.api.PadLayoutCodeDto;
import net.armcloud.paascenter.common.model.dto.api.UpdatePadOnlineDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO;
import net.armcloud.paascenter.openapi.model.dto.PadDetailsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface PadMapper extends BaseMapper<Pad> {

    /**
     * 根据padCode查询pad详情
     *
     * @param param PadDetailsDTO
     * @return PadDetailsVO
     */
    List<PadDetailsVO> selectDetailsByPadCode(PadDetailsDTO param);

    void updateOnline(UpdatePadOnlineDTO param);

    Pad getByCloudVendorTypeAndPadOutCode(@Param("cloudVendorType") Integer cloudVendorType, @Param("padOutCode") String padOutCode);

    /**
     * 查询pad列表信息
     *
     * @param param
     * @return List<PadListVO>
     */
    List<PadListVO> selectPadListVO(PadListDTO param);

    /**
     * 查询pad列表信息（优化分页版本）
     *
     * @param param
     * @return List<PadListVO>
     */
    List<PadListVO> selectPadListVOOptimized(PadListOptimizedDTO param);

    /**
     * 根据padCode查询pad详情
     *
     * @param padCode
     * @return
     */
    PadInfoVO selectPadInfoByCode(@Param("padCode") String padCode);
    List<PadInfoVO> selectPadInfoByCodeList(@Param("padCodeList") List<String> padCodeList);


    /**
     * 查询空闲实例列表
     *
     * @return
     */
    List<PadIdleListVO> padIdleListVO();

    /**
     * 根据padOutCode查询dcId
     *
     * @param padOutCode
     * @return
     */
    Integer getDcIdByPadOutCode(@Param("padOutCode") String padOutCode);

    List<Pad> listAll();

    List<String> getPadCodesByGroupIds(@Param("padCodes") List<String> padCodes, @Param("groupIds") List<Integer> groupIds,
                                       @Param("customerId") Long customerId);

    List<Pad> getPadByGroupIds(@Param("padCodes") List<String> padCodes, @Param("groupIds") List<Integer> groupIds,
                                       @Param("customerId") Long customerId);

    List<ConsoleDcInfoVO> getDcIdGroupByPadCodes(@Param("padCodes") List<String> padCodes);

    List<PadInfoVO> selectPadInfosByDeviceId(@Param("deviceId") Long deviceId, @Param("padNumber") Integer padNumber);

    List<String> selectUsePadIps(@Param("ips") List<String> ips);

    int removePadByDeviceIp(@Param("padCodes") List<String> padCodes);

    int enablePadByDeviceIp(@Param("deviceIp") String deviceIp, @Param("customerId") Long customerId);

    /**
     * 根据pad编号集合和集群编号查询实例重置请求参数
     *
     * @param clusterCode 集群编号
     * @param padCodes    pad编号集合
     * @return List<InstanceRestartRequest>
     */
    List<InstanceResetRequest> selectResetByPadCodesAndCode(@Param("clusterCode") String clusterCode, @Param("padCodes") List<String> padCodes);

    List<String> queryRemovePadsByDeviceIp(@Param("deviceIp") String deviceIp);

    List<PadInfoVO> queryPadCustomerId(@Param("padCodes") List<String> padCodes);

    int deletePadByCodes(@Param("padCodes") List<String> padCodes);

    List<String> getPadCodesByDeviceLevel(@Param("padCodes") List<String> padCodes, @Param("padGrade") String padGrade, @Param("customerId") Long customerId);

    int countByPadIpAndCustomerId(@Param("padIp") String padIp, @Param("customerId") long customerId);

    Pad selectPadByPadCustomerDTO(PadCustomerDTO padCustomerDTO);

    List<String> getPadIpsByDeviceId(@Param("deviceId") Long deviceId);

    List<Pad> selectPadByDeviceCode(@Param("deviceInfos") List<DeviceInfoVo> deviceInfos, @Param("status") List<Integer> status);
    List<PadInfoVO> selectPadByDeviceCodeList(@Param("deviceCodeList") List<String> deviceCodeList);

    List<GetStreamTypeVO> listStreamTypeByPadCodes(@Param("padCodes") List<String> padCodes);

    List<PadEdgeClusterVO> listEdgeClusterInfosByPadCodes(@Param("padCodes") List<String> padCodes);

    int updateMacById(@Param("id") long id, @Param("mac") String mac);

    List<PadEdgeClusterVO> listPadEdgeClusterInfo(@Param("padCodes") List<String> padCodes);

    void updateTypeByIds(@Param("ids") List<Long> ids, @Param("type") String type);

    void updateAdiCertificateIdById(@Param("id") long id, @Param("adiCertificateId") long adiCertificateId);

    List<Pad> listByPadCodes(@Param("padCodes") List<String> padCodes);

    void cancelAdiCertificate(@Param("padCodes") List<String> padCodes);

    int updateScreenLayoutCodeByIds(@Param("ids") List<Long> ids, @Param("screenLayoutCode") String screenLayoutCode);

    /**
     * 修改实例布局编码
     * @param param
     * @return
     */
    Integer updatePadLayoutCode(PadLayoutCodeDto param);

    List<PadEdgeClusterVO> getPadEdgeClusterInfosByPadCodes(@Param("padCodes") List<String> padCodes);

    /**
     * 获取用户下所有有效的实例编号
     * @param customerId
     * @return
     */
    List<PadInfoVO> selectValidPadCodeByCustomerId(@Param("customerId") Long customerId);

    Pad getByPadCode(@Param("padCode") String padCode);

    /**
     * 根据padCode查询pad集群物理机信息
     * @param padCode
     * @return
     */
    PadEdgeClusterVO getPadEdgeClusterInfoByPadCode(@Param("padCode") String padCode);

    int existPadByCode(@Param("padCode") String padCode);

    void updateAdiCertificateIdByPadCode(@Param("padCode") String padCode, @Param("adiCertificateId") long adiCertificateId);

    void updatePadDns(@Param("padCode") String padCode, @Param("dns") String dns);

    /**
     * 根据实例编号查询板卡信息
     * @param padCode
     * @return
     */
    PadAndDeviceInfoVO selectPadAndDeviceInfo(@Param("padCode") String padCode);

    /**
     * 获取离线板卡对应的padcode
     * @param padCodes
     * @return
     */
    List<String> getPadCodeByOfflineDevice(@Param("padCodes")List<String> padCodes);

    Pad selectPadByPadIp(@Param("padIp") String padIp,@Param("clusterCode") String clusterCode);

    Integer updatePad(NetStorageResMoreCompatiblePaasDTO param);

    void batchUpdatePadIp(@Param("list") List<Pad> padList);

    /**
     * 批量更新 Pad 表
     * @param padList 需要更新的 Pad 记录
     * @return 更新成功的行数
     */
    int batchUpdatePads(@Param("list") List<Pad> padList);

   List<NetPadDeviceVO> groupNetPadByDeviceLevel (NetStorageResDetailDTO param);

   List<Pad> selectPadByPadCodes(@Param("padCodes")List<String> padCodes);

   Pad selectPadByPadCode(@Param("padCode")String padCode);
   Integer countPullModePadByPadCodes(@Param("padCodes")List<String> padCodes);

    void updateTaskModeById(@Param("id")Long id,@Param("taskMode")Integer taskMode);

    PadImageDetailVO getImageIdByPadCode (@Param("padCode") String padCode );

    /**
     * 批量插入Pad实例
     */
    int batchInsertPads(@Param("pads") List<Pad> pads);

    /**
     * 根据armServerId查询Pad实例Ip
     *
     * @param armServerId armServerId
     * @return Pad实例Ip
     */
    List<String> selectUsePadIpsByArmServer(@Param("armServerId") Long armServerId);

    Set<String> selectByMacList(@Param("macSet") Set<String> macSet);
}
