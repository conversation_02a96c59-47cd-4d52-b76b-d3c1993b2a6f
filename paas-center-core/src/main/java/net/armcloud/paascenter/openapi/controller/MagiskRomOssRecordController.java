package net.armcloud.paascenter.openapi.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.model.dto.MagiskRomOssRecordCreateDTO;
import net.armcloud.paascenter.openapi.model.dto.MagiskRomOssRecordQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.MagiskRomOssRecordVO;
import net.armcloud.paascenter.openapi.service.IMagiskRomOssRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Magisk ROM OSS记录控制器
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@RestController
@RequestMapping("/openapi/open/magisk/rom/oss/record")
@Api(tags = "Magisk ROM OSS记录管理")
public class MagiskRomOssRecordController {

    @Resource
    private IMagiskRomOssRecordService magiskRomOssRecordService;

    /**
     * 新增或更新记录
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增或更新Magisk ROM OSS记录", notes = "如果版本已存在则更新，否则新增")
    public Result<Void> createOrUpdate(@Valid @RequestBody MagiskRomOssRecordCreateDTO createDTO) {
        return magiskRomOssRecordService.createOrUpdate(createDTO);
    }

    /**
     * 查询记录
     */
    @PostMapping("/query")
    @ApiOperation(value = "查询Magisk ROM OSS记录", notes = "如果version为空则返回最新记录，否则根据version查询")
    public Result<MagiskRomOssRecordVO> queryRecord(@Valid @RequestBody MagiskRomOssRecordQueryDTO queryDTO) {
        return magiskRomOssRecordService.queryRecord(queryDTO);
    }
}
