package net.armcloud.paascenter.openapi.netpadv2.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import org.redisson.api.RLock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/07/06
 * @description 网存实例V2MAC管理服务
 */
@Slf4j
@Component
@AllArgsConstructor
public class PadMacManager {

    private final RedisTemplate<String, String> redisTemplate;

    private final PadMapper padMapper;

    private final RedissonDistributedLock redissonDistributedLock;

    private final static String macKey = "netpadv2:mac";

    private void generateMacAddress() {
        // 每次生成1000个mac地址，并到pad表中进行批量查询去重
        Set<String> macSet = new HashSet<>();
        for (int i = 0; i < 1000; i++) {
            macSet.add(MACUtils.generateMacAddress());
        }
        Set<String> existMacSet = padMapper.selectByMacList(macSet);
        macSet.removeAll(existMacSet);
        // 缓存到redis中
        String cacheKey = RedisKeyUtils.cacheKey(macKey);
        redisTemplate.opsForSet().add(cacheKey, macSet.toArray(new String[0]));
        // 设置过期时间
        redisTemplate.expire(cacheKey, 1, TimeUnit.DAYS);
    }

    public String getMac() {
        // 从redis获取
        String cacheKey = RedisKeyUtils.cacheKey(macKey);
        String mac = redisTemplate.opsForSet().pop(cacheKey);
        if (mac != null) {
            return mac;
        }
        // 加锁
        String lockKey = RedisKeyUtils.lockKey(macKey);
        RLock lock = redissonDistributedLock.tryLock(lockKey, 10, 30);
        if (lock == null) {
            log.warn("获取mac锁失败");
            throw new BasicException(PadExceptionCode.GET_PAD_MAC_EXCEPTION);
        }
        try {
            // 再次从redis获取
            mac = redisTemplate.opsForSet().pop(cacheKey);
            if (mac != null) {
                return mac;
            }
            // 生成新的mac地址
            generateMacAddress();
            return redisTemplate.opsForSet().pop(cacheKey);
        } finally {
            lock.unlock();
        }
    }


}
