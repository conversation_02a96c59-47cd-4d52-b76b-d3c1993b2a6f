package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResPad;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.openapi.mapper.NetStorageResMapper;
import net.armcloud.paascenter.openapi.service.NetStorageResPadService;
import net.armcloud.paascenter.openapi.service.NetStorageResService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/3/6 20:52
 * @Description: NetStorageResServiceImpl
 */
@Service
@Slf4j
public class NetStorageResServiceImpl extends ServiceImpl<NetStorageResMapper, NetStorageRes> implements NetStorageResService {

    @Autowired
    private NetStorageResPadService netStorageResPadService;

    @Override
    public Boolean deductsTheSizeOfTheResourcesUsedByResUnit(Long customerId, List<NetStorageResUnit> netStorageResUnitList) {
        if (CollectionUtils.isEmpty(netStorageResUnitList)) {
            return true; // 没有需要扣减的资源单元，直接返回
        }

        // 查询 customerId 关联的所有 NetStorageRes 记录
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId, customerId);
        List<NetStorageRes> netStorageList = list(wrapper);

        // 如果没有找到存储资源，返回 false
        if (CollectionUtils.isEmpty(netStorageList)) {
            return false;
        }

        // **按可用余额（storageCapacity - storageCapacityUsed）从小到大排序**
        netStorageList.sort(Comparator.comparingLong(res -> res.getStorageCapacity() - res.getStorageCapacityUsed()));

        // **使用 Map 记录最终更新的 NetStorageRes**
        Map<Long, NetStorageRes> updatedNetStorageMap = new HashMap<>();
        List<NetStorageResPad> netStorageResPadList = new ArrayList<>();

        for (NetStorageResUnit netStorageResUnit : netStorageResUnitList) {
            long dataSizeInGB = netStorageResUnit.getNetStorageResUnitSize(); // 需要扣减的大小

            for (NetStorageRes netStorageRes : netStorageList) {
                long availableBalance = netStorageRes.getStorageCapacity() - netStorageRes.getStorageCapacityUsed();
                if (availableBalance <= 0) {
                    continue; // 可用存储为 0，跳过
                }

                NetStorageResPad storageResPad = new NetStorageResPad();
                storageResPad.setNetStorageResId(netStorageRes.getNetStorageResId());
                storageResPad.setPadCode(netStorageResUnit.getPadCode());

                if (dataSizeInGB <= availableBalance) {
                    // **当前 NetStorageRes 足够扣减**

                    netStorageRes.setStorageCapacityUsed(netStorageRes.getStorageCapacityUsed() + dataSizeInGB);
                    dataSizeInGB = 0;
                    netStorageResPadList.add(storageResPad);
                } else {
                    // **当前 NetStorageRes 扣不完，需要从下一个 NetStorageRes 继续扣**
                    netStorageRes.setStorageCapacityUsed(netStorageRes.getStorageCapacity()); // 用完
                    dataSizeInGB -= availableBalance;
                    if (dataSizeInGB == 0) {
                        netStorageResPadList.add(storageResPad);
                    }
                }
                // **递减逻辑,只保留 NetStorageResId 最新的修改**
                updatedNetStorageMap.put(netStorageRes.getNetStorageResId(), netStorageRes);

                if (dataSizeInGB == 0) {
                    break; // 该 NetStorageResUnit 处理完，跳出循环
                }
            }
            // **如果资源不足，返回 false**
            if (dataSizeInGB > 0) {
                return false;
            }
        }
        // **批量更新数据库**
        if (!updatedNetStorageMap.isEmpty()) {
            updateBatchById(new ArrayList<>(updatedNetStorageMap.values())); // 只更新最终状态
        }
        //备份不写入db
//        if (!netStorageResPadList.isEmpty()) {
//            netStorageResPadService.saveBatch(netStorageResPadList);
//        }
        return true;
    }

    public Boolean deductTheSpecifiedBalanceSize(Long customerId, Long size) {
        // 查询 customerId 关联的所有 NetStorageRes 记录
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId, customerId);
        List<NetStorageRes> netStorageList = list(wrapper);

        // 如果没有找到存储资源，返回 false
        if (CollectionUtils.isEmpty(netStorageList)) {
            return false;
        }

        // 按可用余额（storageCapacity - storageCapacityUsed）从小到大排序
        netStorageList.sort(Comparator.comparingLong(res -> res.getStorageCapacity() - res.getStorageCapacityUsed()));

        long remainingSize = size; // 需要扣减的总量
        List<NetStorageRes> updatedResList = new ArrayList<>(); // 记录需要更新的存储资源

        for (NetStorageRes res : netStorageList) {
            long availableSize = res.getStorageCapacity() - res.getStorageCapacityUsed(); // 当前资源的可用大小

            if (availableSize >= remainingSize) {
                // 当前资源足够扣减

                res.setStorageCapacityUsed(res.getStorageCapacityUsed() + remainingSize);
                updatedResList.add(res);
                remainingSize = 0;
                break; // 扣减完成，跳出循环
            } else {
                // 当前资源不够扣减，先用掉当前资源，然后继续找下一个

                res.setStorageCapacityUsed(res.getStorageCapacityUsed() + availableSize);
                updatedResList.add(res);
                remainingSize -= availableSize;
            }
        }

/*        // 如果仍然有剩余未扣减的大小，说明资源不足，返回 false
        if (remainingSize > 0) {
            return false;
        }*/

        // 批量更新数据库（避免多次 I/O 影响性能）
        this.updateBatchById(updatedResList);

        return true;
    }


    @Override
    public Boolean canDeductSize(Long customerId,String clusterCode, Long size) {
        // 查询指定 customerId 的所有 NetStorageRes 记录
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getCustomerId, customerId).eq(NetStorageRes::getClusterCode, clusterCode).last("LIMIT 1");
        NetStorageRes netStorageRes = getOne(wrapper);

        // 如果没有找到符合条件的记录，则返回false
        if (Objects.isNull(netStorageRes)) {
            return false;
        }
        // 计算所有资源的可用余额总和
            Long storageCapacity = netStorageRes.getStorageCapacity();
            Long storageCapacityUsed = netStorageRes.getStorageCapacityUsed();
            if (Objects.isNull(storageCapacityUsed)) {
                storageCapacityUsed = 0L;
            }
            // 计算每个资源的可用余额
            long availableBalance = storageCapacity - storageCapacityUsed;

            // 累加到总可用余额
//            totalAvailableBalance += availableBalance;

            // 如果总余额已经足够，则提前返回true
            //不允许跨存储扣,那么只拿有一个能扣的.
            if (availableBalance >= size) {
                return true;
            }

        // 如果总可用余额小于要扣减的size，返回false
        return false;
    }


    @Override
    public Boolean deductsTheSizeOfTheResourcesUsed(Long customerId,String clusterCode, List<Pad> padList) {
        if (CollectionUtils.isEmpty(padList)) {
            return true; // 如果没有 Pad 需要扣减，直接返回 true
        }

        // 查询 customerId 关联的所有 NetStorageRes 记录
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        //只会有一条
        wrapper.eq(NetStorageRes::getCustomerId, customerId).eq(NetStorageRes::getClusterCode, clusterCode).last("LIMIT 1");
        NetStorageRes netStorageRes = getOne(wrapper);

        // 如果没有找到存储资源，返回 false
        if (Objects.isNull(netStorageRes)) {
            log.error("找不到对应的 NetStorageRes 记录，customerId: {} ,clusterCode:{}", customerId,clusterCode);
            return false;
        }
     /*   long availableBalance = netStorageRes.getStorageCapacity() - netStorageRes.getStorageCapacityUsed();

        if (availableBalance <= 0) {
            log.error("availableBalance 余额不足。customerId: {} ,clusterCode:{}", customerId,clusterCode);
            return false;
        }*/
        List<NetStorageResPad> netStorageResPadList = new ArrayList<>();

        for (Pad pad : padList) {
//            long dataSize = pad.getDataSize();
               // long dataSizeInGB = 0L; // 转换为 GB
                NetStorageResPad storageResPad = new NetStorageResPad();
                storageResPad.setNetStorageResId(netStorageRes.getNetStorageResId());
                storageResPad.setPadCode(pad.getPadCode());
                netStorageResPadList.add(storageResPad);
                /*if (dataSizeInGB <= availableBalance) {
                    // 当前 NetStorageRes 足够扣减
                    netStorageRes.setStorageCapacityUsed(netStorageRes.getStorageCapacityUsed() + dataSizeInGB);

                } else {
                    log.error("存储资源不足，无法扣减。customerId: {} ,clusterCode:{}", customerId, clusterCode);
                    return false;
                }*/
            }

        netStorageRes.setStorageCapacityUsed(netStorageRes.getStorageCapacityUsed());
        //更新余额
        updateById(netStorageRes);
        if (!netStorageResPadList.isEmpty()) {
            netStorageResPadService.saveBatch(netStorageResPadList);
        }
        return true;

    }
};


