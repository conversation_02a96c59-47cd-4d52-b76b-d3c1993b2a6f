package net.armcloud.paascenter.openapi.mapper.typehandle;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import net.armcloud.paascenter.common.model.entity.paas.PadPropertiesSub;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Type;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class PadPropertiesTypeHandler extends BaseTypeHandler<List<PadPropertiesSub>> {
    private final static Type JSON_LIST_TYPE = new TypeReference<List<PadPropertiesSub>>() {
    }.getType();

    private final static Type[] TYPES = new Type[]{JSON_LIST_TYPE};
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<PadPropertiesSub> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSONUtil.toJsonStr(parameter));
    }

    @Override
    public List<PadPropertiesSub> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return JSON.parseObject(rs.getString(columnName), JSON_LIST_TYPE);
    }

    @Override
    public List<PadPropertiesSub> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return JSON.parseObject(rs.getString(columnIndex), JSON_LIST_TYPE);
    }

    @Override
    public List<PadPropertiesSub> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return JSON.parseObject(cs.getString(columnIndex), JSON_LIST_TYPE);
    }
}
