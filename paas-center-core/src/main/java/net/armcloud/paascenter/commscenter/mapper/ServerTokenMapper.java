package net.armcloud.paascenter.commscenter.mapper;

import net.armcloud.paascenter.common.model.entity.comms.ServerToken;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ServerTokenMapper {
    int insertSelective(ServerToken record);

    ServerToken getLatestByPadCode(@Param("padCode") String padCode, @Param("versionCode") Long versionCode);

    @Delete("delete from server_token where pad_code = #{padCode} ")
    void deleteByPadCode(String padCode);

    List<ServerToken> listByServerId(@Param("serverId") long serverId);

    @Select("select token from server_token where comms_server_id = #{serverId} ")
    List<String> listTokenByServerId(@Param("serverId") long serverId);

    @Delete("delete from server_token where comms_server_id = #{serverId} ")
    void deleteByServerId(@Param("serverId") long serverId);
}