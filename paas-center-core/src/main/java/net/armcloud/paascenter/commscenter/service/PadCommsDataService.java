package net.armcloud.paascenter.commscenter.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum;
import net.armcloud.paascenter.common.core.constant.comms.CommsConstant;
import net.armcloud.paascenter.common.model.entity.comms.AppCmdRecord;
import net.armcloud.paascenter.common.model.entity.comms.CmdDownloadQueue;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.manager.PadCmdManager;
import net.armcloud.paascenter.commscenter.mapper.AppCmdRecordMapper;
import net.armcloud.paascenter.commscenter.mapper.CmdDownloadQueueMapper;
import net.armcloud.paascenter.commscenter.mapper.CmdRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.REQUEST_ID;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.RESULT;


@Slf4j
@Service
public class PadCommsDataService {
    private final PadCmdManager padCmdManager;
    private final CmdRecordMapper cmdRecordMapper;
    private final AppCmdRecordMapper appCmdRecordMapper;
    private final CmdDownloadQueueMapper cmdDownloadQueueMapper;

    @Async
    public void updateCmdResult(PadCmdResultMessage resultMessage) {
        String padCode = resultMessage.getPadCode();
        padCmdManager.deleteCmdRunningLimit(padCode);
        if (StringUtils.isBlank(resultMessage.getJsonData())) {
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(resultMessage.getJsonData());
        String requestId = jsonObject.getString(REQUEST_ID);
        if (StringUtils.isBlank(requestId)) {
            handlerAppRecord(resultMessage);
            log.debug("discard requestId is blank>>>>resultMessage:{}", JSON.toJSONString(resultMessage));
            return;
        }

        log.debug("start handler requestId:{}", requestId);
        CmdRecord cmdRecord = new CmdRecord();
        cmdRecord.setCommand(resultMessage.getCommand());
        cmdRecord.setRequestId(requestId);
        cmdRecord.setStatus(resultMessage.getStatus());
        cmdRecord.setResult(jsonObject.getString(RESULT));
        cmdRecord.setErrorMsg(StrUtil.sub(resultMessage.getMsg(), 0, 200));
        cmdRecord.setEndTime(new Date());
        cmdRecordMapper.batchUpdateByRequestId(null, Collections.singletonList(cmdRecord));
        appCmdRecordMapper.batchUpdateSyncStatusDoneByRequestId(null, Collections.singletonList(requestId));

        updateCmdDownloadQueue(cmdRecord);
    }

    private void updateCmdDownloadQueue(CmdRecord cmdRecord) {
        List<String> updateCommandCodes = Arrays.asList(CommsCommandEnum.DOWNLOAD_FILE_APP_CMD.getCommand(),
                CommsCommandEnum.DOWNLOAD_FILE_CMD.getCommand());
        if (!updateCommandCodes.contains(cmdRecord.getCommand())) {
            return;
        }

        cmdDownloadQueueMapper.updateStatusByRequestId(cmdRecord.getRequestId(), cmdRecord.getStatus());
    }

    private void handlerAppRecord(PadCmdResultMessage resultMessage) {
        List<String> handlerCommands = Arrays.asList(CommsCommandEnum.DOWNLOAD_FILE_APP_CMD.getCommand(), CommsCommandEnum.START_APP_CMD.getCommand(), CommsCommandEnum.STOP_APP_CMD.getCommand(), CommsCommandEnum.RESTART_APP_CMD.getCommand(), CommsCommandEnum.UNINSTALL_APP_CMD.getCommand());

        if (!handlerCommands.contains(resultMessage.getCommand())) {
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(resultMessage.getJsonData());
        String packageName = jsonObject.getString(CommsConstant.DataField.PACKAGE_NAME);
        String padCode = resultMessage.getPadCode();
        List<AppCmdRecord> appCmdRecords = appCmdRecordMapper.listByPadCodeAndPackageName(null, padCode, packageName);
        if (CollectionUtils.isEmpty(appCmdRecords)) {
            return;
        }

        List<String> requestIds = appCmdRecords.stream().map(AppCmdRecord::getRequestId).collect(Collectors.toList());
        cmdRecordMapper.batchUpdateExecutingStatusByRequestId(null, resultMessage.getStatus(), requestIds);
        List<Long> appCmdRecordIds = appCmdRecords.stream().map(AppCmdRecord::getId).collect(Collectors.toList());
        appCmdRecordMapper.batchUpdateSyncStatusDoneById(null, appCmdRecordIds);
    }

    public List<CommsTransmissionResultVO> forward(PadCMDForwardDTO request) {
        return padCmdManager.send(request);
    }

    public List<CommsTransmissionResultVO> asyncForward(PadCMDForwardDTO request) {
        log.info("asyncForward request:{}>>>>", JSON.toJSONString(request));
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = request.getPadInfos();
        List<CmdDownloadQueue> addCmdDownloadQueues = new ArrayList<>(padInfos.size());
        List<CommsTransmissionResultVO> resultVOS = new ArrayList<>(padInfos.size());

        padInfos.forEach(padInfo -> {
            PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
            BeanUtils.copyProperties(request, padCMDForwardDTO);
            padCMDForwardDTO.setPadInfos(Collections.singletonList(padInfo));

            String padCode = padInfo.getPadCode();
            CmdDownloadQueue cmdDownloadQueue = new CmdDownloadQueue();
            cmdDownloadQueue.setPadCode(padCode);
            cmdDownloadQueue.setCmdData(JSON.toJSONString(padCMDForwardDTO));
            addCmdDownloadQueues.add(cmdDownloadQueue);
            resultVOS.add(CommsTransmissionResultVO.builderWithSendSuccess(padCode));
        });

        cmdDownloadQueueMapper.batchInsert(addCmdDownloadQueues);

        return resultVOS;
    }

    public PadCommsDataService(CmdRecordMapper cmdRecordMapper, PadCmdManager padCmdManager, AppCmdRecordMapper appCmdRecordMapper, CmdDownloadQueueMapper cmdDownloadQueueMapper) {
        this.cmdRecordMapper = cmdRecordMapper;
        this.padCmdManager = padCmdManager;
        this.appCmdRecordMapper = appCmdRecordMapper;
        this.cmdDownloadQueueMapper = cmdDownloadQueueMapper;
    }
}
