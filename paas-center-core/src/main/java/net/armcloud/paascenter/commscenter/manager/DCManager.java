package net.armcloud.paascenter.commscenter.manager;

import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.openapi.service.IDCService;
import org.springframework.stereotype.Component;

@Component
public class DCManager {

    private final IDCService dcService;

    public DcInfo getByPadCode(String padCode) {
        return dcService.getByPadCode(padCode);
    }

    public DCManager(IDCService dcService) {
        this.dcService = dcService;
    }
}
