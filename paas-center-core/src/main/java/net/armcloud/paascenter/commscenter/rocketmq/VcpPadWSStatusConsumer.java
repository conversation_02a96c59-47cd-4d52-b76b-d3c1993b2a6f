package net.armcloud.paascenter.commscenter.rocketmq;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.commscenter.manager.PadCmdManager;
import net.armcloud.paascenter.commscenter.service.CommsceterPadService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

import static net.armcloud.paascenter.commscenter.constant.LockConstants.CONSUME_PAD_WS_STATUS_MESSAGE_PREFIX;

//已合并到 CallbackVcpPadWSStatusConsumer
@Slf4j
//@Service
//@AliRocketMQMsgListener(consumerGroup = "${mq.vcp-pad-ws-status.group}", topic = "${mq.vcp-pad-ws-status.topic}")
public class VcpPadWSStatusConsumer implements AliRocketMQListener<MessageView> {
    private final CommsceterPadService commsceterPadService;
    private final PadCmdManager padCmdManager;
    private final RedissonDistributedLock redissonDistributedLock;

    @Override
    public void onMessage(MessageView messageView) {
        log.debug("VcpPadWSStatusConsumer messageId:{}>>>>>>>>>", messageView.getMessageId());
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        PadWSStatusMessage message = JSON.parseObject(messageStr, PadWSStatusMessage.class);

        String key = CONSUME_PAD_WS_STATUS_MESSAGE_PREFIX + message.getPadCode();
        RLock lock = redissonDistributedLock.tryLock(key, 3, 30);
        try {
            log.info("start handler padCode:{} ws status messageStr:{}", message.getPadCode(), messageStr);
            padCmdManager.deleteCmdRunningLimit(message.getPadCode());
            if (Boolean.TRUE.equals(message.getConnected())) {
                commsceterPadService.reportConnected(message);
                return;
            }

            commsceterPadService.reportDisconnected(message);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    public VcpPadWSStatusConsumer(CommsceterPadService commsceterPadService, PadCmdManager padCmdManager, RedissonDistributedLock redissonDistributedLock) {
        this.commsceterPadService = commsceterPadService;
        this.padCmdManager = padCmdManager;
        this.redissonDistributedLock = redissonDistributedLock;
    }

}
