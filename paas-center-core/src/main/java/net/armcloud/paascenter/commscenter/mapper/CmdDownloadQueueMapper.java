package net.armcloud.paascenter.commscenter.mapper;

import net.armcloud.paascenter.common.model.entity.comms.CmdDownloadQueue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CmdDownloadQueueMapper {
    //@TableNameConcat(tableNamePrefix = "cmd_download_queue")
    void batchInsert( @Param("list") List<CmdDownloadQueue> list);

    void updateStatusByRequestId(@Param("requestId") String requestId, @Param("status") int status);
}
