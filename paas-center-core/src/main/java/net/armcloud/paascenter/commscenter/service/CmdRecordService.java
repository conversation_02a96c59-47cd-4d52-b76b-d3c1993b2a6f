package net.armcloud.paascenter.commscenter.service;

import net.armcloud.paascenter.common.core.constant.comms.CommsConstant;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.commscenter.manager.PadCmdManager;
import net.armcloud.paascenter.commscenter.mapper.CmdRecordMapper;
import org.springframework.stereotype.Service;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

@Service
public class CmdRecordService {

    private final CmdRecordMapper cmdRecordMapper;
    private final PadCmdManager padCmdManager;

    public CmdRecord getBySubTaskId(long subTaskId) {
        return cmdRecordMapper.getBySubTaskId(null, subTaskId);
    }

    public CmdRecordService(CmdRecordMapper cmdRecordMapper, PadCmdManager padCmdManager) {
        this.cmdRecordMapper = cmdRecordMapper;
        this.padCmdManager = padCmdManager;
    }

    public void updateCommandRecordBySubTaskId(long subTaskId, int status) {
        cmdRecordMapper.updateCmdResultBySubTaskId(null, subTaskId, status);
        String padCode = cmdRecordMapper.getPadCodeBySubTaskId(null, subTaskId);
        if(isNotEmpty(padCode) && status != CommsConstant.CommsCmdStatus.EXECUTING_COMMS_CMD_RECORD_STATUS){
            padCmdManager.deleteCmdRunningLimit(padCode);
        }
    }
}
