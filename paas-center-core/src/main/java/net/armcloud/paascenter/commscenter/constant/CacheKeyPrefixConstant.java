package net.armcloud.paascenter.commscenter.constant;

public class CacheKeyPrefixConstant {

    private static final String PROJECT_NAME = "armcloud-paas-comms-center:";

    /**
     * token关联连接信息缓存key
     */
    public static final String TOKEN_CONNECT_INFO_CACHE_KEY_PREFIX = PROJECT_NAME + "token-connect-info:";


    /**
     * 第三方pad标识关联token缓存key
     */
    public static final String OUT_VENDOR_RELATION_TOKEN_CACHE_KEY_PREFIX = PROJECT_NAME + "out-pad-token:";

    /**
     * pad连接信息
     */
    public static final String PAD_CODE_CONNECTION_KEY_PREFIX = PROJECT_NAME + "pad-code-connection:";

    /**
     * pad运行命令中key
     */
    public static final String PAD_CMD_RUNNING_KEY_PREFIX = PROJECT_NAME + "pad-cmd-running:";

    public static final String TASK_TIMEOUT_CONFIG_KEY = PROJECT_NAME + "task-timeout-config";

    public static final String APPLY_CERTIFICATE_KEY_PREFIX = PROJECT_NAME + "apply-certificate:";

    /**
     * padCode关联映射列表
     */
    public static final String PAD_OUT_CODE_REF_PAD_LIST_KEY = PROJECT_NAME + "pad_out_code_ref_pad_list";

    public class CacheHashFieldKey {
        public static final String SERVER_CACHE_HASH_FIELD_KEY = "server";
    }

    public static final String PAD_REPORTED_DING_TALK = "PAD_REPORTED_DING_TALK:";
}
