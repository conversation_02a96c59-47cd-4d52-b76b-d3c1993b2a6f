package net.armcloud.paascenter.commscenter.manager;


import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import net.armcloud.paascenter.task.service.ITaskService;
import net.armcloud.paascenter.task.service.impl.PadTaskServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CommscenterTaskManager {

    private final PadTaskServiceImpl padTaskService;
    private final ITaskService taskService;

    public List<TaskTimeoutConfig> listTimeoutConfig() {
        return taskService.listTimeoutConfig();
    }

    public CommscenterTaskManager(PadTaskServiceImpl padTaskService,ITaskService taskService) {
        this.padTaskService = padTaskService;
        this.taskService = taskService;
    }

    public Object updateTaskMsg(List<CmdRecord> cmdRecords) {
        return padTaskService.updateTaskMsg(cmdRecords);
    }
}
