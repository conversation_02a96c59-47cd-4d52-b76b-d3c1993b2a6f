package net.armcloud.paascenter.commscenter.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.facade.CommsCenterPadCMDInternalFacade;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.CmdRecordService;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class CommandInternalController implements CommsCenterPadCMDInternalFacade {
    private final PadCommsDataService padCommsDataService;
    private final CmdRecordService cmdRecordService;

    @Override
    public Result<List<CommsTransmissionResultVO>> forward(@RequestBody PadCMDForwardDTO request) {
        return Result.ok(padCommsDataService.forward(request));
    }

    @Override
    public Result<List<CommsTransmissionResultVO>> asyncForward(PadCMDForwardDTO request) {
        return Result.ok(padCommsDataService.asyncForward(request));
    }

    @Override
    public Result<?> updateCmdResult(PadCmdResultMessage resultMessage) {
        padCommsDataService.updateCmdResult(resultMessage);
        return Result.ok();
    }

    @Override
    public Result<CmdRecord> getRecordBySubTaskId(long subTaskId) {
        return Result.ok(cmdRecordService.getBySubTaskId(subTaskId));
    }

    @Override
    public Result<?> updateCommandRecordBySubTaskId(long subTaskId, int status) {
        cmdRecordService.updateCommandRecordBySubTaskId(subTaskId, status);
        return Result.ok();
    }

    public CommandInternalController(PadCommsDataService padCommsDataService, CmdRecordService cmdRecordService) {
        this.padCommsDataService = padCommsDataService;
        this.cmdRecordService = cmdRecordService;
    }
}
