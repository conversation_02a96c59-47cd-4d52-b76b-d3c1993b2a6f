package net.armcloud.paascenter.commscenter.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.CleanServerDTO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.commscenter.dto.ReportDisconnectedDTO;
import net.armcloud.paascenter.common.model.dto.commscenter.dto.ReportPadConnectedDTO;
import net.armcloud.paascenter.commscenter.service.PadMappingService;
import net.armcloud.paascenter.commscenter.service.CommsceterPadService;
import net.armcloud.paascenter.commscenter.service.ServerService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CommscenterPadInternalController {
    private final CommsceterPadService commsceterPadService;
    private final ServerService serverService;
    private final PadMappingService padMappingService;

    /**
     * 改为监听MQ连接消息处理
     */
    @Deprecated
    @PostMapping(value = "/comms-center/internal/pad/reportConnected")
    public Result<String> reportConnected(@Validated @RequestBody ReportPadConnectedDTO request) {
        return Result.ok(commsceterPadService.reportConnected(request));
    }

    /**
     * 改为监听MQ断开消息处理
     */
    @Deprecated
    @PostMapping(value = "/comms-center/internal/pad/reportDisconnected")
    public Result<?> reportDisconnected(@Validated @RequestBody ReportDisconnectedDTO request) {
        commsceterPadService.reportDisconnected(request);
        return Result.ok();
    }

    /**
     * 使用{ServerInternalFacade#cleanServer}接口代替
     */
    @Deprecated
    @PostMapping(value = "/comms-center/internal/pad/cleanServer")
    public Result<?> cleanServer(@Validated @RequestBody CleanServerDTO request) {
        serverService.cleanServer(request);
        return Result.ok();
    }

    @PostMapping(value = "/comms-center/internal/pad/refreshMappings")
    public Result<?> refreshMappings() {
        padMappingService.refresh();
        return Result.ok();
    }

    public CommscenterPadInternalController(CommsceterPadService commsceterPadService, PadMappingService padMappingService, ServerService serverService) {
        this.commsceterPadService = commsceterPadService;
        this.padMappingService = padMappingService;
        this.serverService = serverService;
    }
}
