package net.armcloud.paascenter.commscenter.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.CleanServerDTO;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.bo.comms.ServerCertificateCacheBO;
import net.armcloud.paascenter.common.model.entity.comms.PadBindComms;
import net.armcloud.paascenter.common.model.entity.comms.Server;
import net.armcloud.paascenter.common.model.entity.comms.ServerToken;
import net.armcloud.paascenter.common.model.entity.comms.ServerTokenLog;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.commscenter.manager.DCManager;
import net.armcloud.paascenter.commscenter.manager.PadConnectionManager;
import net.armcloud.paascenter.commscenter.manager.CommscenterPadManager;
import net.armcloud.paascenter.commscenter.manager.ServerCertificateManager;
import net.armcloud.paascenter.commscenter.mapper.*;
import net.armcloud.paascenter.commscenter.model.dto.ApplyCertificateDTO;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.function.Supplier;

import static java.time.LocalDateTime.now;
import static java.time.ZoneId.systemDefault;
import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.APPLY_CERTIFICATE_KEY_PREFIX;
import static net.armcloud.paascenter.commscenter.exception.code.ServerExceptionCode.CONNECTION_PERMISSION_DENIED_EXCEPTION;
import static net.armcloud.paascenter.commscenter.exception.code.ServerExceptionCode.NO_SERVER_AVAILABLE_EXCEPTION;

@Slf4j
@Service
public class ServerService {
    private final DCManager dcManager;
    private final CommscenterPadManager commscenterPadManager;
    private final ServerMapper serverMapper;
    private final ServerPadMapper serverPadMapper;
    private final ServerTokenMapper serverTokenMapper;
    private final ApplicationContext applicationContext;
    private final PadConnectionManager padConnectionManager;
    private final ServerTokenLogMapper serverTokenLogMapper;
    private final RedissonDistributedLock redissonDistributedLock;
    private final ServerCertificateManager serverCertificateManager;
    private final PadBindCommsMapper padBindCommsMapper;

    /**
     * tolen有效时长（单位：秒）
     */
    public static final int TOKEN_EFFECTIVE_DURATION_SECOND = 3600;

    /**
     * 获取连接指令通讯服务器信息
     * <p>
     * token生成规则：
     * 1.现有token未失效则返回未失效token
     * 2.现有token失效则生成新token并记录到token日志中
     */
    public ServerCertificateCacheBO.ClientCertificate applyCertificate(ApplyCertificateDTO dto) {
        String padOutCode = dto.getPadOutCode();
        Integer vendorType = dto.getCloudVendorType();

        RLock lock = redissonDistributedLock.mustLocked(APPLY_CERTIFICATE_KEY_PREFIX + padOutCode + ":" + vendorType);
        try {
            ServerCertificateCacheBO certificate = serverCertificateManager.getCertificateByVendor(vendorType, padOutCode, dto.getVersionCode());
            if (Objects.nonNull(certificate)) {
                return certificate.getClientCertificate();
            }

            ServerCertificateCacheBO certificateCache = serverCertificateManager.cacheCertificateByVendorInfo(vendorType,
                    padOutCode, generateCertificateSupplier(dto), TOKEN_EFFECTIVE_DURATION_SECOND);
            return certificateCache.getClientCertificate();
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    private Supplier<ServerCertificateCacheBO> generateCertificateSupplier(ApplyCertificateDTO dto) {
        return () -> {
            Pad pad = commscenterPadManager.getByCloudVendor(dto.getCloudVendorType(), dto.getPadOutCode());
            if (Objects.isNull(pad)) {
//                log.warn("generateCertificateSupplier >>> pad is null dto:{}", JSON.toJSONString(dto));
                throw new BasicException(CONNECTION_PERMISSION_DENIED_EXCEPTION);
            }

            String padCode = pad.getPadCode();
            ServerToken serverToken = serverTokenMapper.getLatestByPadCode(padCode, dto.getVersionCode());
            // 使用未过期token
            boolean notExpired = serverToken!=null && serverToken.getExpirationTime().after(new Date());
            if (notExpired) {

                // 因之前GameServer之前首次拿到Token后，之后不会再重新调接口拿新Token。所以token统一都设置的缓存时间非常长。
                // 现在GameServer修复了这个问题，为保证TokenToken信息及时刷新需取消Token设置非常久的问题。此段代码为处理历史缓存数据，待之后需删除此段代码
                // 如当前token有效期超过最大值则重新生成新token
                Date maxExpireTime = Date.from(LocalDateTime.now().plusSeconds(TOKEN_EFFECTIVE_DURATION_SECOND)
                        .atZone(ZoneId.systemDefault()).toInstant());
                if (serverToken.getExpirationTime().before(maxExpireTime)) {
                    Server server = serverMapper.getById(serverToken.getCommsServerId());
                    return ServerCertificateCacheBO.convert(serverToken, server);
                }
            }

            Server server = null;

            PadBindComms padBindComms = padBindCommsMapper.selectOne(new QueryWrapper<>(PadBindComms.class)
                    .eq("pad_out_code",dto.getPadOutCode())
                    .eq("enable",true)
                    .last("limit 1"));
            if(padBindComms != null){
                //可指定comms服务器 目前用于灰度 一般这台不会对外开放 也就是禁用状态
                server = serverMapper.getById(padBindComms.getCommsServerId());
                if (Objects.isNull(server)) {
                    throw new BasicException(NO_SERVER_AVAILABLE_EXCEPTION);
                }
            }else{
                DcInfo dcInfo = dcManager.getByPadCode(padCode);
                if (Objects.isNull(dcInfo)) {
                    throw new BasicException(NO_SERVER_AVAILABLE_EXCEPTION);
                }

                server = serverMapper.chooseServerByDcId(dcInfo.getId());
                if (Objects.isNull(server)) {
                    throw new BasicException(NO_SERVER_AVAILABLE_EXCEPTION);
                }
            }

            serverToken = applicationContext.getBean(ServerService.class).generationAndSaveNewToken(padCode, server, dto);
            return ServerCertificateCacheBO.convert(serverToken, server);
        };
    }

    @Transactional(rollbackFor = Exception.class)
    public ServerToken generationAndSaveNewToken(String padCode, Server server, ApplyCertificateDTO dto) {
        serverTokenMapper.deleteByPadCode(padCode);

        ServerToken serverToken = new ServerToken();
        serverToken.setPadCode(padCode);
        serverToken.setCommsServerId(server.getId());
        // token改为直接使用第三方code
        String newToken = dto.getPadOutCode();
        serverToken.setToken(newToken);
        Date expirationTime = Date.from(now().plusSeconds(TOKEN_EFFECTIVE_DURATION_SECOND).atZone(systemDefault()).toInstant());
        serverToken.setExpirationTime(expirationTime);
        serverToken.setImageId(dto.getImageId());
        serverToken.setVersionCode(dto.getVersionCode());
        serverToken.setVersionName(dto.getVersionName());
        serverTokenMapper.insertSelective(serverToken);

        ServerTokenLog serverTokenLog = new ServerTokenLog();
        BeanUtils.copyProperties(serverToken, serverTokenLog);
        serverTokenLog.setImageId(dto.getImageId());
        serverTokenLog.setVersionCode(dto.getVersionCode());
        serverTokenLog.setVersionName(dto.getVersionName());
        serverTokenLogMapper.insertSelective(serverTokenLog);
        return serverToken;
    }

    public void cleanServer(CleanServerDTO request) {
        long serverId = request.getServerId();
        log.warn("start cleanServer:{}......", serverId);
        padConnectionManager.deleteCacheAndSendCloseMessage(serverId);
        serverPadMapper.deleteByServerId(serverId);
        serverMapper.cleanConnectSize(serverId);
    }

    public ServerService(ServerTokenMapper serverTokenMapper, CommscenterPadManager commscenterPadManager,
                         ServerCertificateManager serverCertificateManager, ServerMapper serverMapper,
                         ServerTokenLogMapper serverTokenLogMapper, ApplicationContext applicationContext, RedissonDistributedLock redissonDistributedLock, DCManager dcManager, ServerPadMapper serverPadMapper, PadConnectionManager padConnectionManager,
                         PadBindCommsMapper padBindCommsMapper) {
        this.serverTokenMapper = serverTokenMapper;
        this.commscenterPadManager = commscenterPadManager;
        this.serverCertificateManager = serverCertificateManager;
        this.serverMapper = serverMapper;
        this.serverTokenLogMapper = serverTokenLogMapper;
        this.applicationContext = applicationContext;
        this.redissonDistributedLock = redissonDistributedLock;
        this.dcManager = dcManager;
        this.serverPadMapper = serverPadMapper;
        this.padConnectionManager = padConnectionManager;
        this.padBindCommsMapper = padBindCommsMapper;
    }
}
