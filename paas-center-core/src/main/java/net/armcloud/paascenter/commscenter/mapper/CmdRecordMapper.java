package net.armcloud.paascenter.commscenter.mapper;

import net.armcloud.paascenter.common.core.annotation.TableNameConcat;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CmdRecordMapper {
    @TableNameConcat(tableNamePrefix = "cmd_record_")
    void batchUpdateByRequestId(@Param("tableName") String tableName, @Param("list") List<CmdRecord> list);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    void batchInsert(@Param("tableName") String tableName, @Param("list") List<CmdRecord> list);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    void batchUpdateError(@Param("tableName") String tableName, @Param("list") List<CmdRecord> failRecords);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    String getLastNotTimeoutResetOrRestartRequestId(@Param("tableName") String tableName, @Param("padCode") String padCode);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    CmdRecord getBySubTaskId(@Param("tableName") String tableName, @Param("subTaskId") long subTaskId);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    void batchUpdateExecutingStatusByRequestId(@Param("tableName") String tableName, @Param("status") int status,
                                               @Param("requestIds") List<String> requestIds);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    void updateCmdResultBySubTaskId(@Param("tableName") String tableName, @Param("subTaskId") long subTaskId, @Param("status") int status);

    @TableNameConcat(tableNamePrefix = "cmd_record_")
    String getPadCodeBySubTaskId(@Param("tableName") String tableName, @Param("subTaskId") long subTaskId);
}
