package net.armcloud.paascenter.task.manager.executor.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceRestartRequest;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 实例重启
 */
@Slf4j
@Component
public class PadRestartTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;


    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        InstanceRestartRequest req = new InstanceRestartRequest();

        InstanceRestartRequest.Instance instance = new InstanceRestartRequest.Instance();
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setPadCode(padCode);
        req.setInstances(Collections.singletonList(instance));
        return req;
    }


    public PadRestartTaskExecutorStrategy(PadMapper padMapper) {
        this.padMapper = padMapper;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.RESTART.getType(), "padRestartTaskExecutorStrategy");
    }
}
