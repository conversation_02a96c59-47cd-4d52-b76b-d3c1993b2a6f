package net.armcloud.paascenter.task.service;

import net.armcloud.paascenter.common.client.internal.dto.CancelFileTaskDTO;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.api.UpdateFileSubTaskDTO;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;

public interface IFileTaskService {
    /**
     * 修改子任务状态
     * <p>
     * 子任务状态更新后会联动刷新主任务状态
     *
     * @throws BasicException 任务不存在异常
     */
    void updateSubTaskStatus(UpdateFileSubTaskDTO dto) throws BasicException;

    FileUploadTask getById(long id);

    void cancelFileTask(CancelFileTaskDTO dto);
}
