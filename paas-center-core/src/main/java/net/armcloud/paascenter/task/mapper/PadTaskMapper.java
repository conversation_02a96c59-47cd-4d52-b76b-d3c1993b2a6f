package net.armcloud.paascenter.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.ContainerPadTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.SelectPadCodeAndTaskIdDTO;
import net.armcloud.paascenter.common.model.dto.api.TaskDetailsInfoDTO;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.vo.api.HasPadTaskVo;
import net.armcloud.paascenter.common.model.vo.api.PadTaskViewVO;
import net.armcloud.paascenter.common.model.vo.api.SubTaskVo;
import net.armcloud.paascenter.common.model.vo.task.PadTaskIdVO;
import net.armcloud.paascenter.task.annotation.CalculateTaskTimeout;
import net.armcloud.paascenter.task.model.vo.PadEdgeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface PadTaskMapper extends BaseMapper<PadTask> {
    List<PadTaskViewVO> listVOByMasterTaskIdAndUniqueIds(@Param("masterTaskId") long masterTaskId,
                                                         @Param("uniqueIds") List<String> uniqueIds);
    @CalculateTaskTimeout
    int insertBatch(@Param("padTasks") List<PadTask> padTasks);

    @CalculateTaskTimeout
    int updateNotEndTaskStatusById(PadTask padTask);

    List<PadTask> listByTaskId(@Param("taskId") long taskId);

    void batchUpdateUniqueId(@Param("list") List<PadTask> list);

    PadTask getLatestTask(@Param("padCode") String padCode,@Param("taskTypeList") List<Integer> taskTypeList);

    /**
     * 获取前N次已完成的任务
     * @param padCode
     * @return
     */
    List<PadTask> getLastNCompletedTasks(@Param("padCode") String padCode);

    PadTask getById(@Param("id") long id);

    List<PadTaskViewVO> listVOByCustomerTaskIdsAndCustomerId(TaskDetailsInfoDTO taskDetailsDTO);

    int setStartTime(@Param("id") long id, @Param("startTime") Date startTime);

    PadTask getPodTaskByUniqueId(@Param("uniqueId") String uniqueId);

    List<String> getFilterExecutionTaskPads(@Param("type") Integer type, @Param("list") List<String> padCodes);

    @Update("update pad_task set container_task_id = #{containerTaskId} where id = #{id}")
    void updateContainerIdById(@Param("id") long id, @Param("containerTaskId") long containerTaskId);

    /**
     * 获取进行中的任务(只查询任务，待执行与执行中的数据)
     * @param padCode
     * @return
     */
    HasPadTaskVo getHasPadTask(@Param("padCode") String padCode);

    int countByPadCodesStatus(@Param("padCodes") List<String> padCodes, @Param("statusList") List<Integer> statusList);

    /**
     * 查询指定任务状态数据
     * @param customerId
     * @param padCodes
     * @return
     */
    List<SelectPadCodeAndTaskIdDTO> selectPadCodeAndTaskId(@Param("customerId") Long customerId,@Param("padCodes") List<String> padCodes);


    int batchUpdateContainerById(@Param("list") List<ContainerPadTaskDTO> successList);


    @Update("update pad_task set status = #{status},start_time = #{nowTime},end_time = #{nowTime} where id = #{id} and delete_flag = false")
    void updateStatusAndTimeById(@Param("id") long id, @Param("status") int status, @Param("nowTime") Date nowTime);


    List<HasPadTaskVo> getExecTaskPads(@Param("type") Integer type, @Param("list") List<String> padCodes);

    Long selectTheTypeDoingPadTask(@Param("padCode") String padCode,@Param("type") Integer type);

    int pullModeUpdateTimeout(@Param("id") Long id,@Param("timeoutTime") Date timeoutTime,@Param("errorMsg") String errorMsg);

    /**
     * 查询安装应用重复的padCode列表
     * @param customerId 客户ID
     * @param padCodes 待查询的padCode列表
     * @param customerFileId 客户文件ID
     * @return 冲突的padCode列表
     */
    List<GeneratePadTaskVO> findConflictPadCodesForInstallApp(@Param("customerId") long customerId,
                                                              @Param("padCodes") List<String> padCodes,
                                                              @Param("customerFileId") long customerFileId);


    List<PadEdgeVO> queryPadClusterInfo(@Param("padCodes")List<String> padCodes);

    /**
     * 查询是否存在执行中或等待执行的指定类型任务
     * 
     * @param type 任务类型
     * @param padCode 实例编号
     * @param statusList 任务状态列表
     * @return 存在的任务列表
     */
    List<HasPadTaskVo> checkTasksByTypeAndPadCode(@Param("type") Integer type, 
                                                    @Param("padCode") String padCode,
                                                    @Param("statusList") List<Integer> statusList);
                                                    
    /**
     * 根据子任务ID、任务类型、任务状态查询结果为空的任务
     * 
     * @param subTaskId 子任务ID
     * @param type 任务类型
     * @param status 任务状态
     * @return 匹配的任务
     */
    PadTask getEmptyResultTaskByTypeAndStatus(@Param("id") Long subTaskId, 
                                            @Param("type") Integer type, 
                                            @Param("status") Integer status);

    /**
     * 根据类型和padcode查询最近的任务
     *
     * @param type 任务类型
     * @param padCode 实例编号
     * @return 存在的任务列表
     */
    PadTask getLatestPadTaskByType(@Param("type") Integer type,@Param("padCode") String padCode);
}
