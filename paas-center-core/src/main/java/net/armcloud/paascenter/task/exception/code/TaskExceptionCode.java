package net.armcloud.paascenter.task.exception.code;

import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskExceptionCode implements ExceptionCode {
    TASK_DOES_NOT_EXIST(130001, "任务不存在，请检查任务ID是否正确"),
    TASK_TYPE_NOT_EXIST(130002, "类型不能为空"),

    ;

    private final int status;
    private final String msg;
}
