package net.armcloud.paascenter.task.manager;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.task.config.TaskTaskStatusMessageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.SUCCESS;

/**
 * topic:vcp_task_status
 */
@Slf4j
@Component
public class MessageNotifyManager {
    private final DefaultRocketMqProducerWrapper mqProducerService;
    private final TaskTaskStatusMessageConfig taskTaskStatusMessageConfig;

    /**
     * 状态同步callback
     * @param task
     * @param padTask
     */
    public void sendPadTaskStatusChangeMessage(Task task, PadTask padTask) {
        log.info("sendPadTaskStatusChangeMessage padCode:{},task:{},padTask:{}", padTask.getPadCode(), JSON.toJSONString(task), JSON.toJSONString(padTask));
        PadStatusTaskMessageMQ messageMQ = new PadStatusTaskMessageMQ();
        messageMQ.setTaskBusinessType(task.getType());
        messageMQ.setTaskId(padTask.getCustomerTaskId());
        messageMQ.setTaskStatus(padTask.getStatus());
        messageMQ.setPadCode(padTask.getPadCode());
        messageMQ.setCustomerId(task.getCustomerId());
        messageMQ.setEndTime(padTask.getEndTime() != null ? padTask.getEndTime().getTime() : null);
        messageMQ.setTaskContent(padTask.getTaskContent() != null ? padTask.getTaskContent() : "");
        messageMQ.setTaskResult(SUCCESS.getStatus().equals(padTask.getStatus()) ? "Success" : "Fail");

        mqProducerService.producerNormalMessage(taskTaskStatusMessageConfig.getTopic(), JSON.toJSONString(messageMQ));
    }

    public MessageNotifyManager(DefaultRocketMqProducerWrapper mqProducerService, TaskTaskStatusMessageConfig taskTaskStatusMessageConfig) {
        this.mqProducerService = mqProducerService;
        this.taskTaskStatusMessageConfig = taskTaskStatusMessageConfig;
    }
}
