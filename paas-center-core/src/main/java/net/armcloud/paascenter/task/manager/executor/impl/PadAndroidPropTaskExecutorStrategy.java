package net.armcloud.paascenter.task.manager.executor.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceUpdatePropRequest;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.bo.task.quque.UpdatePadAndroidPropTaskQueueBO;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * 修改安卓改机属性
 */
@Slf4j
@Component
public class PadAndroidPropTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        UpdatePadAndroidPropTaskQueueBO queueBO = JSON.parseObject(taskQueue.getContentJson(), UpdatePadAndroidPropTaskQueueBO.class);
        InstanceUpdatePropRequest updatePropRequest = new InstanceUpdatePropRequest();
        updatePropRequest.setRestart(queueBO.getRestart());

        InstanceUpdatePropRequest.Instance instance = new InstanceUpdatePropRequest.Instance();
        PadEdgeClusterVO padEdgeClusterInfo = padEdgeClusterVOS.get(0);
        instance.setDeviceIp(padEdgeClusterInfo.getDeviceIp());
        instance.setPadCode(padEdgeClusterInfo.getPadCode());
        instance.setProps(queueBO.getProps());
        updatePropRequest.setInstances(Collections.singletonList(instance));
        return updatePropRequest;
    }

    public PadAndroidPropTaskExecutorStrategy(PadMapper padMapper) {
        this.padMapper = padMapper;

        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.UPDATE_PAD_ANDROID_PROP.getType(), "padAndroidPropTaskExecutorStrategy");
    }
}
