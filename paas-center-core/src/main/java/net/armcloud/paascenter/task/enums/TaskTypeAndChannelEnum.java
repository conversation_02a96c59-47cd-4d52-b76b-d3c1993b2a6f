package net.armcloud.paascenter.task.enums;

import lombok.Getter;
import net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;

import static net.armcloud.paascenter.common.core.constant.device.PadAllocationStatusConstants.ALLOCATING;
import static net.armcloud.paascenter.common.core.constant.device.PadAllocationStatusConstants.DELETING;


@Getter
public enum TaskTypeAndChannelEnum {
    //cbs任务 - 实例重启
    RESTART(TaskTypeConstants.RESTART.getType(), TaskChannelEnum.CBS.getCode(),7,PadStatusConstant.RESTARTING,null,null,TaskTypeEnum.INSTANCE_RESTART,false,false),
    //cbs任务 - 实例重置
    RESET(TaskTypeConstants.RESET.getType(), TaskChannelEnum.CBS.getCode(),7,PadStatusConstant.RESETTING,null,null,TaskTypeEnum.INSTANCE_RESET,false,false),
    //gs任务 - pod执行命令
    EXECUTE_COMMAND(TaskTypeConstants.EXECUTE_COMMAND.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.ADB_CMD,null,null,false,false),
    //gs任务 - 推送armcloud流
    PUSH_ARMCLOUD_FLOW(TaskTypeConstants.GS_PUSH_ARMCLOUD_FLOW.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.PUSH_ARMCLOUD_FLOW,null,null,true,false),
    //gs任务 - 通知Pad进入火山RTC房间推流
    PUSH_VOLCANO_FLOW(TaskTypeConstants.GS_PUSH_VOLCANO_FLOW.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.PUSH_VOLCANO_FLOW,null,null,true,false),
    //gs任务 - 加入Armcloud共享房间
    JOIN_ARMCLOUD_SHARE_ROOM(TaskTypeConstants.GS_JOIN_ARMCLOUD_SHARE_ROOM.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.JOIN_ARMCLOUD_SHARE_ROOM,null,null,true,false),
    //gs任务 - 加入火山共享房间
    JOIN_VOLCANO_SHARE_ROOM(TaskTypeConstants.GS_JOIN_VOLCANO_SHARE_ROOM.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.JOIN_VOLCANO_SHARE_ROOM,null,null,true,false),
    //gs任务 - 销毁火山房间
    DESTROY_VOLCANO_ROOM(TaskTypeConstants.GS_DESTROY_VOLCANO_ROOM.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.DESTROY_VOLCANO_ROOM,null,null,true,false),
    //gs任务 - 应用安装
    DOWNLOAD_APP(TaskTypeConstants.DOWNLOAD_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.DOWNLOAD_FILE_APP_CMD,null,null,false,false),
    //gs任务 - 卸载应用
    UNINSTALL_APP(TaskTypeConstants.UNINSTALL_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.UNINSTALL_APP_CMD,null,null,false,false),
    //gs任务 - 应用停止
    STOP_APP(TaskTypeConstants.STOP_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.STOP_APP_CMD,null,null,false,false),
    //gs任务 - 应用重启
    RESTART_APP(TaskTypeConstants.RESTART_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.RESTART_APP_CMD,null,null,false,false),
    //gs任务 - 应用启动
    START_APP(TaskTypeConstants.START_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.START_APP_CMD,null,null,false,false),
    //gs任务 - 本地截图
    SCREENSHOT_LOCAL(TaskTypeConstants.SCREENSHOT_LOCAL.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.LOCAL_SCREENSHOT,null,null,false,false),
    //gs任务 - 下载文件
    DOWNLOAD_FILE(TaskTypeConstants.DOWNLOAD_FILE.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.DOWNLOAD_FILE_CMD,null,null,false,false),
    //gs任务 - 修改实例属性
    UPDATE_PAD_PROPERTIES(TaskTypeConstants.UPDATE_PAD_PROPERTIES.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.UPDATE_PROPERTIES,null,null,false,false),
    //gs任务 - 修改WIFI
    SET_WIFI_LIST(TaskTypeConstants.SET_WIFI_LIST.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.SET_WIFI_LIST,null,null,false,false),

    //gs任务 - 查询已安装应用
    LIST_INSTALLED_APP(TaskTypeConstants.LIST_INSTALLED_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.LIST_INSTALL_APP,null,null,false,false),
    //cbs任务 - 升级镜像
    UPGRADE_IMAGE(TaskTypeConstants.UPGRADE_IMAGE.getType(), TaskChannelEnum.CBS.getCode(),0,PadStatusConstant.UPGRADING,null,null,TaskTypeEnum.INSTANCE_UPGRADE_IMAGE,false,false),
    //gs任务 - 应用清理  该指令未使用
    CLEAN_APP(TaskTypeConstants.CLEAN_APP.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,null,null,null,false,false),
    //gs任务 - 应用黑名单
    APP_BLACK_LIST(TaskTypeConstants.APP_BLACK_LIST.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.UPDATE_BLACK_LIST,null,null,false,false),
    //cbs任务 - 实例限速
    LIMIT_BANDWIDTH(TaskTypeConstants.LIMIT_BANDWIDTH.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,TaskTypeEnum.INSTANCE_NETWORK_LIMIT,false,false),
    //gs任务 - 设置经纬度
    GPS_INJECT_INFO(TaskTypeConstants.GPS_INJECT_INFO.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.GPS_INJECT_INFO,null,null,false,false),
    //gs任务 - 设置代理
    PAD_SET_NETWORK_PROXY(TaskTypeConstants.PAD_SET_NETWORK_PROXY.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.PROXY_CONFIGURE,null,null,false,false),
    //gs任务 - 查询代理信息
    GET_PAD_NETWORK_PROXY_INFO(TaskTypeConstants.GET_PAD_NETWORK_PROXY_INFO.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.PROXY_INFO,null,null,false,false),
    //gs任务 - 切换语言
    CHANGE_LANGUAGE(TaskTypeConstants.CHANGE_LANGUAGE.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.CHANGE_LANGUAGE,null,null,false,false),
    //gs任务 - 切换时区
    CHANGE_TIME_ZONE(TaskTypeConstants.CHANGE_TIME_ZONE.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.CHANGE_TIME_ZONE,null,null,false,false),
    //gs任务 - 切换SIM卡
    UPDATE_SIM(TaskTypeConstants.UPDATE_SIM.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.UPDATE_SIM,null,null,false,false),
    //cbs任务 - 修改改机属性
    UPDATE_PAD_ANDROID_PROP(TaskTypeConstants.UPDATE_PAD_ANDROID_PROP.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,TaskTypeEnum.INSTANCE_SET_ANDROID_PROP,false,false),
    //cbs任务 - 升级真机镜像
    VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE(TaskTypeConstants.VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), TaskChannelEnum.CBS.getCode(),0,PadStatusConstant.UPGRADING,null,null,TaskTypeEnum.INSTANCE_UPGRADE_IMAGE,false,false),
    //cbs任务 - 升级虚拟镜像
    REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE(TaskTypeConstants.REAL_VIRTUAL_SWITCH_UPGRADE_IMAGE.getType(), TaskChannelEnum.CBS.getCode(),0,PadStatusConstant.UPGRADING,null,null,TaskTypeEnum.INSTANCE_UPGRADE_IMAGE,false,false),
    //gs任务 - 清除所有app进程并返回云机首页
    CLEAR_APP_HOME(TaskTypeConstants.CLEAR_APP_HOME.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.CLEAR_APP_HOME,null,null,false,false),
    //cbs任务 - 升级真机adi模板
    REPLACE_REAL_ADB(TaskTypeConstants.REPLACE_REAL_ADB.getType(), TaskChannelEnum.CBS.getCode(),0,PadStatusConstant.UPGRADING,null,null,TaskTypeEnum.INSTANCE_REPLACE_REAL_ADB,false,false),
    //cbs任务 - 一键新机
    REPLACE_PAD(TaskTypeConstants.REPLACE_PAD.getType(), TaskChannelEnum.CBS.getCode(),8,PadStatusConstant.RESETTING,null,null,TaskTypeEnum.INSTANCE_REPLACE_PROP,false,false),
    //cbs任务 - 修改实例属性
    MODIFY_PROPERTIES_PAD(TaskTypeConstants.MODIFY_PROPERTIES_PAD.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,TaskTypeEnum.INSTANCE_MODIFY_PROPERTIES_PROP,false,false),
    //cbs任务 - 数据备份
    BACKUP_PAD(TaskTypeConstants.BACKUP_PAD.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,TaskTypeEnum.INSTANCE_BACKUP_DATA,false,false),
    //cbs任务 - 备份数据还原
    RESTORE_PAD(TaskTypeConstants.RESTORE_PAD.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,TaskTypeEnum.INSTANCE_RESTORE_BACKUP_DATA,false,false),
    //gs任务 - 修改通讯录
    UPDATE_CONTACTS(TaskTypeConstants.UPDATE_CONTACTS.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.UPDATE_CONTACTS,null,null,false,false),
    //gs任务 - 应用白名单
    APP_WHITE_LIST(TaskTypeConstants.APP_WHITE_LIST.getType(), TaskChannelEnum.GAMESERVER.getCode(),0,null,CommsCommandEnum.UPDATE_WHITE_LIST,null,null,false,false),
    //cbs任务 - 开启/关闭ADB
    OPEN_ONLINE_PAD(TaskTypeConstants.OPEN_ONLINE_PAD.getType(), TaskChannelEnum.CBS.getCode(),8,null,null,null,null,false,false),
    //
    FILE_UPLOAD(TaskTypeConstants.FILE_UPLOAD.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,null,false,false),
    //
    FILE_DELETE(TaskTypeConstants.FILE_DELETE.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,null,null,false,false),
    //cbs任务 - 重启板卡(软)
    DEVICE_RESTART(TaskTypeConstants.DEVICE_RESTART.getType(), TaskChannelEnum.CBS.getCode(),9,null,null,"device",TaskTypeEnum.DEVICE_RESTART,false,false),
    //bmc任务 - 断电重启(硬)
    POWER_RESET(TaskTypeConstants.POWER_RESET.getType(), TaskChannelEnum.BMC.getCode(),9,null,null,null,null,false,false),
    BOARD_IMAGE_WARMUP(TaskTypeConstants.BOARD_IMAGE_WARMUP.getType(), TaskChannelEnum.CBS.getCode(),9,null,null,"device",TaskTypeEnum.DEVICE_BOARD_IMAGE_WARMUP,false,false),
    //cbs任务 - 创建实例
    CONTAINER_VIRTUALIZE(TaskTypeConstants.CONTAINER_VIRTUALIZE.getType(), TaskChannelEnum.CBS.getCode(),9,ALLOCATING.getStatus(),null,"device",TaskTypeEnum.DEVICE_CREATE,false,false),
    //网存实例开机
    NET_WORK_PAD_ON(TaskTypeConstants.CONTAINER_NET_STORAGE_ON.getType(), TaskChannelEnum.CBS.getCode(),9,PadStatusConstant.ON_RUN,null,null,TaskTypeEnum.INSTANCE_NET_WORK_ON,false,false),
    NET_WORK_PAD_OFF(TaskTypeConstants.CONTAINER_NET_STORAGE_OFF.getType(), TaskChannelEnum.CBS.getCode(),8,PadStatusConstant.OFF_RUN,null,null,TaskTypeEnum.INSTANCE_NET_WORK_OFF,false,false),
    NET_WORK_PAD_DELETE(TaskTypeConstants.CONTAINER_NET_STORAGE_DELETE.getType(), TaskChannelEnum.CBS.getCode(),9,null,null,null,TaskTypeEnum.INSTANCE_NET_WORK_BACKUP,false,false),
    NET_WORK_PAD_BACKUP(TaskTypeConstants.CONTAINER_NET_STORAGE_BACKUP.getType(), TaskChannelEnum.CBS.getCode(),9,null,null,null,TaskTypeEnum.INSTANCE_NET_WORK_BACKUP,false,false),
    NET_WORK_PAD_RES_UNIT_DELETE(TaskTypeConstants.CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType(), TaskChannelEnum.CBS.getCode(),9,null,null,null,TaskTypeEnum.INSTANCE_NET_WORK_ON,false,false),
    /**
     * 网存2.0
     */
    // 网存开机
    NET_PAD_ON(TaskTypeConstants.NET_PAD_ON.getType(), TaskChannelEnum.CBS.getCode(),9,PadStatusConstant.ON_RUN,null,null,TaskTypeEnum.INSTANCE_NET_WORK_ON,false,false),
    NET_PAD_OFF(TaskTypeConstants.NET_PAD_OFF.getType(), TaskChannelEnum.CBS.getCode(),9,PadStatusConstant.OFF_RUN,null,null,TaskTypeEnum.INSTANCE_NET_WORK_OFF,false,false),
    NET_PAD_DEL(TaskTypeConstants.NET_PAD_DEL.getType(), TaskChannelEnum.CBS.getCode(),9,null,null,null,TaskTypeEnum.INSTANCE_NET_WORK_DELETE,false,false),
    //cbs任务 - 重置板卡
    CONTAINER_DEVICE_DESTROY(TaskTypeConstants.CONTAINER_DEVICE_DESTROY.getType(), TaskChannelEnum.CBS.getCode(),9,DELETING.getStatus(),null,"device",TaskTypeEnum.DEVICE_DELETE,false,false),
    //bmc任务 - 设置板卡网关
    SET_GATEWAY(TaskTypeConstants.SET_GATEWAY.getType(), TaskChannelEnum.BMC.getCode(),0,null,null,null,null,false,false),
    //cbs任务 - cbs自更新  暂时没用
    CBS_SELF_UPDATE(TaskTypeConstants.CBS_SELF_UPDATE.getType(), TaskChannelEnum.CBS.getCode(),0,null,null,"device",TaskTypeEnum.DEVICE_CBS_SELF_UPDATE,false,false),
    //bmc任务 - 创建板卡
    CREATE_DEVICE(TaskTypeConstants.CREATE_DEVICE.getType(), TaskChannelEnum.BMC.getCode(),0,null,null,"arm",null,false,false),
    //bmc任务 - arm服务器自检
    CREATE_DEVICE_SELF_INSPECTION(TaskTypeConstants.CREATE_DEVICE_SELF_INSPECTION.getType(), TaskChannelEnum.BMC.getCode(),0,null,null,"arm",null,false,false),
    //bmc任务 - 刷Debian内核
    BRUSH_CORE_ARM(TaskTypeConstants.BRUSH_CORE_ARM.getType(), TaskChannelEnum.BMC.getCode(),0,null,null,"arm",null,false,false),
    //gs任务 - 模拟触控
    SIMULATE_TOUCH(TaskTypeConstants.SIMULATE_TOUCH.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.SIMULATE_TOUCH,null,null,false,false),
    //gs任务 - 模拟触控
    UPLOAD_PREVIEW(TaskTypeConstants.UPLOAD_PREVIEW.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.UPLOAD_PREVIEW,null,null,false,true),
    //gs任务 - 编辑通话记录
    EDIT_CALL_LOGS(TaskTypeConstants.ADD_PHONE_RECORD.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.ADD_PHONE_RECORD,null,null,false,false),
    //gs任务 - 云机文本信息输入
    SET_COMMIT_TEXT(TaskTypeConstants.SET_COMMIT_TEXT.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.SET_COMMIT_TEXT,null,null,false,false),
    //gs任务 - 重置 GAID
    RESET_GAID(TaskTypeConstants.RESET_GAID.getType(), TaskChannelEnum.GAMESERVER.getCode(),9, null, CommsCommandEnum.RESET_GAID,null,TaskTypeEnum.INSTANCE_RESTART_GAID,false,false),

    //cbs任务-网存同步备份
    NET_SYNC_BACKUP(TaskTypeConstants.NET_SYNC_BACKUP.getType(), TaskChannelEnum.CBS.getCode(), 9, null, CommsCommandEnum.NET_SYNC_BACKUP, null, TaskTypeEnum.INSTANCE_NET_SYNC_BACKUP, true, false, true,true),

    //cbs任务-强制关机
    FORCE_POWER_OFF(TaskTypeConstants.FORCE_POWER_OFF.getType(), TaskChannelEnum.CBS.getCode(), 9, PadStatusConstant.OFF, null, null, TaskTypeEnum.INSTANCE_FORCE_POWER_OFF, false, false),

    //gs任务 - 注入音频到实例麦克风
    INJECTION_AUDIO(TaskTypeConstants.INJECTION_AUDIO.getType(), TaskChannelEnum.GAMESERVER.getCode(),9,null,CommsCommandEnum.INJECTION_AUDIO,null,null,false,false),


    ;
    /**任务类型*/
    public final Integer taskCode;
    /**设备类型*/
    public final String channel;
    /**优先级0-9*/
    public final Integer priority;
    /**实例状态 PadStatusConstant*/
    public final Integer padStatusConstant;
    /**gs cmd对应的指令*/
    public final CommsCommandEnum commsCommandEnum;
    /**任务类型细分类型 pad、device 为空则按默认pad处理*/
    public final String smallerType;
    /**cbs任务类型*/
    public final TaskTypeEnum cbsTaskTypeEnum;
    /**是否直接发送指令到边缘 带任务*/
    public final Boolean sendDirectly;
    /**是否直接发送指令到边缘 无任务*/
    public final Boolean sendDirectlyNotTask;
    /**是否需要重试*/
    private final Boolean needRetry;
    /**任务通知成功后是否需要等待回调（设为执行中而非完成）*/
    private final Boolean needWaitCallback;

    TaskTypeAndChannelEnum(Integer taskCode, String channel, Integer priority, Integer padStatusConstant, CommsCommandEnum commsCommandEnum, String smallerType,
                           TaskTypeEnum cbsTaskTypeEnum, Boolean sendDirectly, Boolean sendDirectlyNotTask) {
        this(taskCode, channel, priority, padStatusConstant, commsCommandEnum, smallerType, cbsTaskTypeEnum, sendDirectly, sendDirectlyNotTask, false,false);
    }

    TaskTypeAndChannelEnum(Integer taskCode, String channel, Integer priority, Integer padStatusConstant, CommsCommandEnum commsCommandEnum, String smallerType,
                           TaskTypeEnum cbsTaskTypeEnum, Boolean sendDirectly, Boolean sendDirectlyNotTask, Boolean needRetry, Boolean needWaitCallback) {
        this.taskCode = taskCode;
        this.channel = channel;
        this.priority = priority;
        this.padStatusConstant = padStatusConstant;
        this.commsCommandEnum = commsCommandEnum;
        this.smallerType = smallerType;
        this.cbsTaskTypeEnum = cbsTaskTypeEnum;
        this.sendDirectly = sendDirectly;
        this.sendDirectlyNotTask = sendDirectlyNotTask;
        this.needRetry = needRetry;
        this.needWaitCallback = needWaitCallback;
    }

    public static TaskTypeAndChannelEnum fromCode(Integer code) {
        for (TaskTypeAndChannelEnum taskChannel : TaskTypeAndChannelEnum.values()) {
            if (taskChannel.getTaskCode().equals(code)) {
                return taskChannel;
            }
        }
        return null;
    }

    public static TaskTypeAndChannelEnum fromCommsCommand(String commsCommand) {
        for (TaskTypeAndChannelEnum taskChannel : TaskTypeAndChannelEnum.values()) {
            if (taskChannel.getCommsCommandEnum() != null && taskChannel.getCommsCommandEnum().getCommand().equals(commsCommand)) {
                return taskChannel;
            }
        }
        return null;
    }
}