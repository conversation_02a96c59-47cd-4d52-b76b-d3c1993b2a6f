package net.armcloud.paascenter.task.service.impl;

import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
import static net.armcloud.paascenter.task.exception.code.TaskExceptionCode.TASK_DOES_NOT_EXIST;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.CancelFileTaskDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.api.UpdateFileSubTaskDTO;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;
import net.armcloud.paascenter.filecenter.mapper.FileUploadTaskMapper;
import net.armcloud.paascenter.task.manager.TaskTaskManager;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.task.service.IFileTaskService;

@Slf4j
@Service
public class FileTaskServiceImpl implements IFileTaskService {
    private final TaskMapper taskMapper;
    private final TaskTaskManager taskTaskManager;
    private final FileUploadTaskMapper fileUploadTaskMapper;
    private final TaskService taskService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSubTaskStatus(UpdateFileSubTaskDTO dto) throws BasicException {
        long fileTaskId = dto.getSubTaskId();
        FileUploadTask fileUploadTask = fileUploadTaskMapper.getById(fileTaskId);
        if (Objects.isNull(fileUploadTask)) {
            throw new BasicException(TASK_DOES_NOT_EXIST);
        }

        updateSubTask(fileUploadTask, dto);
        taskTaskManager.refreshMasterTaskStatus(fileUploadTask.getTaskId());
    }

    @Override
    public FileUploadTask getById(long id) {
        return fileUploadTaskMapper.getById(id);
    }

    @Override
    public void cancelFileTask(CancelFileTaskDTO dto) {
        if (CollectionUtils.isEmpty(dto.getFileCustomerIds())) {
            return;
        }

        List<FileUploadTask> fileUploadTasks = fileUploadTaskMapper.listWaitDownloadTaskByFileCustomerId(dto.getFileCustomerIds());
        if (CollectionUtils.isEmpty(fileUploadTasks)) {
            return;
        }

        int updateSize = fileUploadTaskMapper.cancel(fileUploadTasks.stream().map(FileUploadTask::getId).collect(Collectors.toList()));
        if (updateSize <= 0) {
            return;
        }

        List<Long> masterTaskIds = fileUploadTasks.stream().map(FileUploadTask::getTaskId).distinct().collect(Collectors.toList());
        masterTaskIds.forEach(taskTaskManager::refreshMasterTaskStatus);
    }

    private void updateSubTask(FileUploadTask fileUploadTask, UpdateFileSubTaskDTO dto) {
        long fileTaskId = dto.getSubTaskId();
        int currentStatus = fileUploadTask.getStatus();
        int updateStatus = dto.getSubTaskStatus();

        // 成功的任务不再修改
        if (TaskStatusConstants.SUCCESS.getStatus() == currentStatus) {
            log.info("currentStatus is success skip dto:{}", JSON.toJSONString(dto));
            return;
        }

        FileUploadTask updateFileUploadTask = new FileUploadTask();
        updateFileUploadTask.setId(fileTaskId);
        updateFileUploadTask.setErrorMsg(dto.getErrorMsg());
        // 执行中的任务
        if (updateStatus == EXECUTING.getStatus() && Objects.isNull(fileUploadTask.getCreatedTime())) {
            updateFileUploadTask.setStatus(EXECUTING.getStatus());
            updateFileUploadTask.setCreatedTime(dto.getStartDate());
            fileUploadTaskMapper.update(updateFileUploadTask);
            return;
        }

        if (currentStatus == updateStatus) {
            log.info("currentStatus:{} == updateStatus:{} skip dto:{}", fileUploadTask, updateStatus, JSON.toJSONString(dto));
            return;
        }

        // 失败任务
        if (updateStatus == TaskStatusConstants.FAIL_ALL.getStatus()) {
            updateFileUploadTask.setStatus(TaskStatusConstants.FAIL_ALL.getStatus());
            updateFileUploadTask.setEndTime(dto.getEndDate());
            fileUploadTaskMapper.update(updateFileUploadTask);
            return;
        }

        // 成功任务
        if (updateStatus == TaskStatusConstants.SUCCESS.getStatus()) {
            updateFileUploadTask.setStatus(TaskStatusConstants.SUCCESS.getStatus());
            updateFileUploadTask.setEndTime(dto.getEndDate());
            fileUploadTaskMapper.update(updateFileUploadTask);
        }
    }

    public FileTaskServiceImpl(FileUploadTaskMapper fileUploadTaskMapper, TaskTaskManager taskTaskManager, TaskMapper taskMapper, TaskService taskService) {
        this.fileUploadTaskMapper = fileUploadTaskMapper;
        this.taskTaskManager = taskTaskManager;
        this.taskMapper = taskMapper;
        this.taskService = taskService;
    }
}
