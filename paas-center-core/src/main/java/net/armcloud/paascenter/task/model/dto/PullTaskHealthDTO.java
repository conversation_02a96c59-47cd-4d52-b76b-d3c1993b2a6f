package net.armcloud.paascenter.task.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 拉取任务模式健康检测请求对象
 */
@Data
public class PullTaskHealthDTO {
    /**集群编号*/
    @NotEmpty(message = "集群编号不能为空")
    private String clusterCode;

    /**设备类型 GS、CBS、BMC*/
    @NotEmpty(message = "设备类型不能为空")
    private String deviceType;

    /**设备ip*/
    private String deviceIp;

    /**设备编号*/
    private String deviceCode;

    /**是是否在线 0离线 1在线*/
    private Integer online;

    /**版本信息*/
    private String version;

    /**版本信息*/
    private String versionCode;
}
