package net.armcloud.paascenter.task.utils;


import net.armcloud.paascenter.common.utils.MD5Utils;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

public class IdGenerateUtils {
    private static final AtomicInteger counter = new AtomicInteger(0);

    public static String generationMasterUniqueId(long masterTaskId) {
        return "mt" + MD5Utils.generateMD5("" + System.currentTimeMillis() + masterTaskId);
    }

    public static String generationSubTaskUniqueId(long subTaskId) {
        return "st" + MD5Utils.generateMD5("" + System.currentTimeMillis() + subTaskId);
    }

    public static String generateUniqueIdApprox() {
        long time = System.nanoTime();
        int count = counter.incrementAndGet() & 0xFFFF;
        long rand = ThreadLocalRandom.current().nextLong();
        String input = "" + time + count + rand;
        return "st" + MD5Utils.generateMD5(input);
    }


    private IdGenerateUtils(){}
}
