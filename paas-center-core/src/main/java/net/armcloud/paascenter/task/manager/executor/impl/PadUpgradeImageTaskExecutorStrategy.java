package net.armcloud.paascenter.task.manager.executor.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
import net.armcloud.paascenter.cms.model.request.*;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.rtc.PadMacLog;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.openapi.mapper.PadMacLogMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static net.armcloud.paascenter.cms.exception.code.InstanceExceptionCode.NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION;

/**
 * 升级镜像
 */
@Slf4j
@Component
public class PadUpgradeImageTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    private final PadTaskMapper padTaskMapper;
    private final PadMacLogMapper padMacLogMapper;
    private final InstanceDetailImageSuccService instanceDetailImageSuccService;
    private final ITaskService taskService;

    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadTask padTask = padTaskMapper.getById(taskQueue.getSubTaskId());
        ImageRequest image = new ImageRequest();
        image.setId(padTask.getImageId());
        // 暂不需要放开此功能，暂为固定值
        image.setTag("latest");

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        InstanceUpgradeImageRequest.Instance instance = new InstanceUpgradeImageRequest.Instance();
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setPadCode(padCode);
        instance.setImage(image);
        instance.setMac(getAndSetPadMac(padTask.getPadCode()));
        instance.setClearDiskData(padTask.getWipeData());
        TaskRelInstanceDetail taskRelInstanceDetail = instanceDetailImageSuccService.getLastInfo(padCode);
        instance.setOldParam(buildPadOldParam(taskRelInstanceDetail));
        InstanceUpgradeImageRequest req = new InstanceUpgradeImageRequest();
        req.setInstances(Collections.singletonList(instance));

        //记录task_rel_instance_detail表
        taskService.saveDeviceInstanceSingle(padTask.getTaskId(),padTask.getId(), TaskTypeAndChannelEnum.UPGRADE_IMAGE.getCbsTaskTypeEnum(),taskRelInstanceDetail,req);
        return req;
    }

    public static VirtualizeDeviceRequest.Device.Pad buildPadOldParam(TaskRelInstanceDetail taskRelInstanceDetail){
        if(taskRelInstanceDetail == null){
            throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
        }
        CreatePadBO createPadBO = JSON.parseObject(taskRelInstanceDetail.getContainerProperty(),CreatePadBO.class);
        VirtualizeDeviceRequest.Device.Pad pad = new VirtualizeDeviceRequest.Device.Pad();
        pad.setContainerIndex(String.valueOf(createPadBO.getContainerIndex()));
        pad.setPadCode(createPadBO.getHostname());
        ImageRequest imageRequest = new ImageRequest();
        imageRequest.setId(taskRelInstanceDetail.getImageId());
        imageRequest.setTag(taskRelInstanceDetail.getImageTag());
        pad.setImage(imageRequest);
        DisplayRequest displayRequest = new DisplayRequest();
        displayRequest.setDpi(createPadBO.getDpi());
        displayRequest.setFps(createPadBO.getFps());
        displayRequest.setHeight(createPadBO.getHeight());
        displayRequest.setWidth(createPadBO.getWidth());
        pad.setDisplay(displayRequest);
        SpecRequest specRequest = new SpecRequest();
        specRequest.setCpu(createPadBO.getCpuLimit()!=null?createPadBO.getCpuLimit().intValue():null);
        specRequest.setDisk(createPadBO.getStorageLimit()!=null?createPadBO.getStorageLimit().intValue():null);
        specRequest.setMemory(createPadBO.getMemoryLimit()!=null?createPadBO.getMemoryLimit().intValue():null);
        specRequest.setIsolateDisk(createPadBO.getIsolateDisk());
        pad.setSpec(specRequest);
        NetworkRequest networkRequest = new NetworkRequest();
        networkRequest.setIp(createPadBO.getIp());
        networkRequest.setMaxDownlinkBandwidth(taskRelInstanceDetail.getMaxDownlinkBandwidth());
        networkRequest.setMaxUplinkBandwidth(taskRelInstanceDetail.getMaxUplinkBandwidth());
        networkRequest.setDns(createPadBO.getDns1());
        networkRequest.setMac(createPadBO.getMac());
        networkRequest.setNetworkDeviceName(createPadBO.getMacvlanName());
        pad.setNetwork(networkRequest);
        pad.setAndroidProp(createPadBO.getOtherAndroidProp());
        pad.setNetStorageResId(taskRelInstanceDetail.getNetStorageResId());
        pad.setNetStorageResFlag(taskRelInstanceDetail.getNetStorageResFlag());
        HashMap<String, String> androidMap = Maps.newHashMap();
        //默认关闭
        androidMap.put("persist.sys.cloud.madb_enable","0");
//        if(StrUtil.isNotEmpty(createPadBO.getDeviceAndroidProps())){
//            Map<String,String> map = JSON.parseObject(createPadBO.getDeviceAndroidProps(), Map.class);
//            androidMap.putAll(map);
//        }
        pad.setDeviceAndroidProps(androidMap);
        VirtualizeDeviceRequest.ADI adi = new VirtualizeDeviceRequest.ADI();
        adi.setTemplateUrl(createPadBO.getDownloadUrlOfADI());
        adi.setTemplatePassword(createPadBO.getPasswordOfADI());
        adi.setAndroidCertData(createPadBO.getAndroidCertData());
        pad.setAdi(adi);
        return pad;
    }



    private String getAndSetPadMac(String padCode) {
        Pad pad = padMapper.getByPadCode(padCode);
        String mac = pad.getMac();
        if (StringUtils.isNotBlank(mac)) {
            return mac;
        }

        do {
            mac = MACUtils.generateMacAddress();
            int updateSize = padMapper.updateMacById(pad.getId(), mac);
            if (updateSize <= 0) {
                continue;
            }

            PadMacLog padMacLog = new PadMacLog();
            padMacLog.setPadCode(pad.getPadCode());
            padMacLog.setMac(mac);
            padMacLogMapper.insert(padMacLog);
            return mac;
        } while (true);
    }

    public PadUpgradeImageTaskExecutorStrategy(PadMapper padMapper,PadTaskMapper padTaskMapper, PadMacLogMapper padMacLogMapper,InstanceDetailImageSuccService instanceDetailImageSuccService,
                                               ITaskService taskService) {
        this.padMapper = padMapper;
        this.padTaskMapper = padTaskMapper;
        this.padMacLogMapper = padMacLogMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        this.taskService = taskService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.UPGRADE_IMAGE.getType(), "padUpgradeImageTaskExecutorStrategy");
    }
}
