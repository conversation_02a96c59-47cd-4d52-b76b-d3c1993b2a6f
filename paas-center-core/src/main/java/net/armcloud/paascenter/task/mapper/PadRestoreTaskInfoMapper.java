package net.armcloud.paascenter.task.mapper;

import net.armcloud.paascenter.common.model.entity.task.PadRestoreTaskInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PadRestoreTaskInfoMapper {

    List<PadRestoreTaskInfo> listById(@Param("ids") List<Long> ids);

    int countByPadCodesAndTaskStatusList(@Param("padCodes") List<String> padCodes, @Param("taskStatusList") List<Integer> taskStatusList);

    void batchInsert(@Param("list") List<PadRestoreTaskInfo> list);
}