package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.config.HarborConfig;
import net.armcloud.paascenter.cms.manager.HarborConfigManage;
import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
import net.armcloud.paascenter.cms.model.request.ContainerNetworkDTO;
import net.armcloud.paascenter.cms.model.request.ContainerResourceOpsDTO;
import net.armcloud.paascenter.cms.model.request.VirtualizeDeviceRequest;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.AdiCertificateRepository;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.helper.NetStoragePadHelper;
import net.armcloud.paascenter.openapi.manager.AdiCertificateManager;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStoragePadCodeDetailDTO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网存实例开机
 *
 * <AUTHOR>
 * @Date 2025/3/29 16:44
 * @Description:
 */
@Slf4j
@Component
public class PadNetStorageOnTaskExecuteHandler implements ITaskParamExecutorStrategy {


    private final NetStoragePadHelper netStoragePadHelper;

    private final HarborConfigManage harborConfigManage;

    private final AdiCertificateManager adiCertificateManager;
    private final RealPhoneTemplateMapper realPhoneTemplateMapper;
    private final PadMapper padMapper;

    private final ConfigurationMapper configurationMapper;

    private final ScreenLayoutMapper screenLayoutMapper;

    @Resource
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;

    public final InstanceDetailImageSuccService instanceDetailImageSuccService;

    private final AdiCertificateRepositoryMapper adiCertificateRepositoryMapper;

    private final CustomerUploadImageMapper customerUploadImageMapper;

    @Override
    public Object execute(TaskQueue padTask) {
        log.info("PadNetStorageOnTaskExecuteHandler_execute_start, padTask:{}", JSONObject.toJSONString(padTask));
        ContainerResourceOpsDTO resourceOpsDTO = new ContainerResourceOpsDTO();
        try{
            String padCode = padTask.getKey();
            // 获取 JSON 字符串
            String contentJson = padTask.getContentJson();
            // 使用 Jackson 反序列化
            ObjectMapper objectMapper = new ObjectMapper();
            List<NetStorageDTO> netStorageDTOList ;
            netStorageDTOList = objectMapper.readValue(
                    contentJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, NetStorageDTO.class)
            );
            Map<String, NetStoragePadCodeDetailDTO> padCodeDetailDTOMap = netStorageDTOList.stream()
                    .flatMap(dto -> dto.getNetStoragePadCodeDetailDTOList().stream()) // 扁平化处理
                    .collect(Collectors.toMap(NetStoragePadCodeDetailDTO::getPadCode, detail -> detail, (v1, v2) -> v1));
            NetStoragePadCodeDetailDTO netStoragePadCodeDetailDTO = padCodeDetailDTOMap.get(padCode);
            PadDetailsVO padDetailsVO = netStoragePadCodeDetailDTO.getPadDetailsVO();
            HarborConfig harborConfig = harborConfigManage.get();
            String imageRepository = harborConfig.getUrl() + "/" + harborConfig.getProject() + "/" + padDetailsVO.getImageId();
            //获取规格信息
            VirtualizeDeviceInfoVO virtualizeDeviceInfoVOS = netStoragePadHelper.getVirtualizeDevice(netStoragePadCodeDetailDTO.getNetStorageComputeUnit().getDeviceId());
            String gameServiceInterfaceDomain = configurationMapper.selectValueByKey(SystemConfigurationConstants.GAME_SERVICE_INTERFACE_DOMAIN);

            TaskRelInstanceDetail lastInfo = instanceDetailImageSuccService.getLastInfo(padCode);
            log.info("PadNetStorageOnTaskExecuteHandler_execute_start lastInfo:{}", JSON.toJSONString(lastInfo));
            //开机过
            if(Objects.nonNull(lastInfo)){
                CreatePadBO createPadBO = JSON.parseObject(lastInfo.getContainerProperty(), CreatePadBO.class);
                if(lastInfo.getTaskType().equals(TaskTypeEnum.INSTANCE_UPGRADE_IMAGE.getIntValue())
                        || lastInfo.getTaskType().equals(TaskTypeEnum.INSTANCE_REPLACE_PROP.getIntValue())){
                    resourceOpsDTO.setCpuLimit(createPadBO.getCpuLimit());
                    resourceOpsDTO.setMemoryLimit(createPadBO.getMemoryLimit());
                    resourceOpsDTO.setWidth(createPadBO.getWidth());
                    resourceOpsDTO.setHeight(createPadBO.getHeight());
                    resourceOpsDTO.setFps(createPadBO.getFps());
                    resourceOpsDTO.setDpi(createPadBO.getDpi());


                }else{
                    resourceOpsDTO.setCpuLimit(lastInfo.getCpu().longValue());
                    resourceOpsDTO.setMemoryLimit(lastInfo.getMemory().longValue());
                    resourceOpsDTO.setWidth(lastInfo.getWidth());
                    resourceOpsDTO.setHeight(lastInfo.getHeight());
                    resourceOpsDTO.setFps(lastInfo.getFps());
                    resourceOpsDTO.setDpi(lastInfo.getDpi());
                }
                resourceOpsDTO.setStorageLimit(padDetailsVO.getNetStorageResSize());
                resourceOpsDTO.setName(lastInfo.getInstanceName());
                resourceOpsDTO.setIp(padDetailsVO.getPadIp());
                resourceOpsDTO.setMac(padDetailsVO.getMac());
                String[] splitDns = splitDns(padDetailsVO.getDns(),padDetailsVO.getClusterCode());
                if(splitDns.length>1){
                    resourceOpsDTO.setDns1(splitDns[0]);
                    resourceOpsDTO.setDns2(splitDns[1]);
                }else if (splitDns.length ==1){
                    resourceOpsDTO.setDns1(splitDns[0]);
                }
                resourceOpsDTO.setHostname(padCode);
                resourceOpsDTO.setImageRepository(imageRepository);
                resourceOpsDTO.setImageTag(lastInfo.getImageTag());
                resourceOpsDTO.setExtId(lastInfo.getIdentificationCode());

                String networkDeviceName =virtualizeDeviceInfoVOS.getMacVlan();
                if (StringUtils.isNotBlank(networkDeviceName) && networkDeviceName.contains(".")) {
                    int lastIndex = networkDeviceName.lastIndexOf(".");
                    String macVlanSuffix = networkDeviceName.substring(lastIndex + 1);
                    String macvlanName = "macvlan" + macVlanSuffix;
                    resourceOpsDTO.setMacvlanName(macvlanName);
                }

                resourceOpsDTO.setGameServerAddress(gameServiceInterfaceDomain);
                Map<String, String> deviceAndroidPropMap = Maps.newHashMap();
                //        每次实例开机,默认关闭adb
                deviceAndroidPropMap.put("persist.sys.cloud.madb_enable","0");
                if (StringUtils.isNotBlank(lastInfo.getDeviceAndroidProp())) {
                    deviceAndroidPropMap.putAll(Optional.ofNullable(JSON.parseObject(lastInfo.getDeviceAndroidProp(), Map.class)).orElse(Maps.newHashMap()));
                }else if(StringUtils.isNotBlank(createPadBO.getDeviceAndroidProps())){
                    deviceAndroidPropMap.putAll(Optional.ofNullable(JSON.parseObject(createPadBO.getDeviceAndroidProps(), Map.class)).orElse(Maps.newHashMap()));
                }
                resourceOpsDTO.setDeviceAndroidProps(deviceAndroidPropMap);
                // adi模板需要根据pad表的关联关系重新获取
                Pad pad = padMapper.getByPadCode(padCode);
                if(Objects.nonNull(padDetailsVO.getRealPhoneTemplateId())){
                    RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(padDetailsVO.getRealPhoneTemplateId());
                    resourceOpsDTO.setDownloadUrlOfADI(realPhoneTemplate.getAdiTemplateDownloadUrl());
                    resourceOpsDTO.setPasswordOfADI(realPhoneTemplate.getAdiTemplatePwd());
                    if(pad.getAdiCertificateId() == null){
                        resourceOpsDTO.setAndroidCertData(createPadBO.getAndroidCertData());
                    }else{
                        AdiCertificateRepository adiCertificateRepository = adiCertificateRepositoryMapper.selectById(pad.getAdiCertificateId());
                        resourceOpsDTO.setAndroidCertData(adiCertificateRepository.getCertificate());
                    }
                }else if(StringUtils.isNotBlank(lastInfo.getAdiJson())){
                    setADIInfo(resourceOpsDTO, lastInfo.getAdiJson());
                }
                
                resourceOpsDTO.setStorageId(padDetailsVO.getNetStorageResId());
                if(!Objects.equals(padDetailsVO.getTargetStorageResId(),padDetailsVO.getNetStorageResId())){
                    resourceOpsDTO.setStorageBasedOn(padDetailsVO.getTargetStorageResId());
                }
                resourceOpsDTO.setClearContainerData(false);
                ContainerNetworkDTO networkDTO = new ContainerNetworkDTO();
                networkDTO.setGateway(virtualizeDeviceInfoVOS.getGateway());
                networkDTO.setName(padCode);
                networkDTO.setSubnet(virtualizeDeviceInfoVOS.getSubnet());
                networkDTO.setIpRange(virtualizeDeviceInfoVOS.getIpRange());
                networkDTO.setDeviceName(networkDeviceName);
                resourceOpsDTO.setContainerNetwork(networkDTO);
            }else {
                log.info("PadNetStorageOnTaskExecuteHandler_execute_start virtualizeDeviceInfoVOS:{}", JSON.toJSONString(virtualizeDeviceInfoVOS));
                resourceOpsDTO.setAsync(false);
                resourceOpsDTO.setCpuLimit(!Objects.isNull(padDetailsVO.getCpu()) && padDetailsVO.getCpu()>0 ? padDetailsVO.getCpu() :virtualizeDeviceInfoVOS.getCpu().longValue());
                resourceOpsDTO.setMemoryLimit(!Objects.isNull(padDetailsVO.getMemory()) && padDetailsVO.getMemory()>0 ? padDetailsVO.getMemory() :virtualizeDeviceInfoVOS.getMemory().longValue());
                resourceOpsDTO.setStorageLimit(padDetailsVO.getNetStorageResSize());
                resourceOpsDTO.setMac(padDetailsVO.getMac());
                resourceOpsDTO.setIp(padDetailsVO.getPadIp());
                String[] splitDns = splitDns(padDetailsVO.getDns(),padDetailsVO.getClusterCode());
                if(splitDns.length>1){
                    resourceOpsDTO.setDns1(splitDns[0]);
                    resourceOpsDTO.setDns2(splitDns[1]);
                }else if (splitDns.length ==1){
                    resourceOpsDTO.setDns1(splitDns[0]);
                }
                resourceOpsDTO.setHostname(padCode);
                resourceOpsDTO.setName(padCode);
                resourceOpsDTO.setImageRepository(imageRepository);
                resourceOpsDTO.setImageTag("latest");
                resourceOpsDTO.setExtId(genInstIdCode(padTask.getMasterTaskId(),padTask.getSubTaskId(),padCode));
                String networkDeviceName =virtualizeDeviceInfoVOS.getMacVlan();
                if (StringUtils.isNotBlank(networkDeviceName) && networkDeviceName.contains(".")) {
                    int lastIndex = networkDeviceName.lastIndexOf(".");
                    String macVlanSuffix = networkDeviceName.substring(lastIndex + 1);
                    String macvlanName = "macvlan" + macVlanSuffix;
                    resourceOpsDTO.setMacvlanName(macvlanName);
                }
                resourceOpsDTO.setGameServerAddress(gameServiceInterfaceDomain);
                ScreenLayout screenLayout = screenLayoutMapper.selectOne(new QueryWrapper<ScreenLayout>().eq("code", padDetailsVO.getScreenLayoutCode()));
                log.info("PadNetStorageOnTaskExecuteHandler_execute_start screenLayout:{}", JSON.toJSONString(screenLayout));
                resourceOpsDTO.setWidth(screenLayout.getScreenWidth().intValue());
                resourceOpsDTO.setHeight(screenLayout.getScreenHigh().intValue());
                resourceOpsDTO.setDpi(screenLayout.getPixelDensity().intValue());
                resourceOpsDTO.setFps(screenLayout.getScreenRefreshRate().intValue());
                if(!Objects.equals(padDetailsVO.getTargetStorageResId(),padDetailsVO.getNetStorageResId())){
                    resourceOpsDTO.setStorageBasedOn(padDetailsVO.getTargetStorageResId());
                }
                if(Objects.nonNull(padDetailsVO.getRealPhoneTemplateId())){
                    RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(padDetailsVO.getRealPhoneTemplateId());
                    resourceOpsDTO.setDownloadUrlOfADI(realPhoneTemplate.getAdiTemplateDownloadUrl());
                    resourceOpsDTO.setPasswordOfADI(realPhoneTemplate.getAdiTemplatePwd());
                    String imageParameter = customerUploadImageMapper.getImageParameterByImageUniqueId(padDetailsVO.getImageId());
                    AdiCertificateRepository adiCertificateRepository = adiCertificateManager.useAdiCertificate(padCode, imageParameter);
                    if (adiCertificateRepository != null) {
                        resourceOpsDTO.setAndroidCertData(adiCertificateRepository.getCertificate());
                    }
                }
                resourceOpsDTO.setStorageId(padDetailsVO.getNetStorageResId());
                ContainerNetworkDTO networkDTO = new ContainerNetworkDTO();
                networkDTO.setGateway(virtualizeDeviceInfoVOS.getGateway());
                networkDTO.setName(padCode);
                networkDTO.setSubnet(virtualizeDeviceInfoVOS.getSubnet());
                networkDTO.setIpRange(virtualizeDeviceInfoVOS.getIpRange());
                networkDTO.setDeviceName(networkDeviceName);
                resourceOpsDTO.setClearContainerData(false);
                resourceOpsDTO.setContainerNetwork(networkDTO);
                //默认关闭adb
                Map<String, String> deviceAndroidProps = new HashMap<>();
                deviceAndroidProps.put("persist.sys.cloud.madb_enable","0");
                resourceOpsDTO.setDeviceAndroidProps(deviceAndroidProps);
            }
            //写入随机安卓属性
            JSONObject androidProp = netStoragePadCodeDetailDTO.getAndroidProp();
            if (androidProp != null) {
                // 将 JSONObject 转换为 Map<String, String>
                Map<String, String> androidPropMap = androidProp.toJavaObject(Map.class);
                resourceOpsDTO.getDeviceAndroidProps().putAll(androidPropMap);
            }

        }catch (Exception e){
            log.error("PadNetStorageOnTaskExecuteHandler execute error:{}",e.getMessage(),e);
            throw new BasicException("PadNetStorageOnTaskExecuteHandler 运行异常.");
        }
        log.info("PadNetStorageOnTaskExecuteHandler_execute result_param:{}", JSON.toJSONString(resourceOpsDTO));
        return resourceOpsDTO;
    }


    private String[] splitDns(String dns,String clusterCode){
        if(StringUtils.isEmpty(dns)){
            dns = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, EdgeClusterConfigurationEnum.CLUSTER_DNS_DEFAULT_SERVERS);
        }
        return dns.split(",");
    }

    private static void setADIInfo(ContainerResourceOpsDTO dto, String adiJson) {
        if (StringUtils.isBlank(adiJson)) {
            return;
        }

        VirtualizeDeviceRequest.ADI adi = JSON.parseObject(adiJson, VirtualizeDeviceRequest.ADI.class);
        if (adi == null) {
            return;
        }

        dto.setDownloadUrlOfADI(adi.getTemplateUrl());
        dto.setPasswordOfADI(adi.getTemplatePassword());
        dto.setAndroidCertData(adi.getAndroidCertData());
    }

    public static String genInstIdCode(long masterTaskId, long subTaskId, String instanceName) {
        return masterTaskId + "_" + subTaskId + "_" + instanceName;
    }

    public PadNetStorageOnTaskExecuteHandler(NetStoragePadHelper netStoragePadHelper,
                                             HarborConfigManage harborConfigManage,
                                             AdiCertificateManager adiCertificateManager,
                                             PadStatusMapper padStatusMapper,
                                             ConfigurationMapper configurationMapper,
                                             PadMapper padMapper,
                                             InstanceDetailImageSuccService instanceDetailImageSuccService,
                                             ScreenLayoutMapper screenLayoutMapper,
                                             RealPhoneTemplateMapper realPhoneTemplateMapper,
                                             AdiCertificateRepositoryMapper adiCertificateRepositoryMapper,
                                             CustomerUploadImageMapper customerUploadImageMapper) {
        this.padMapper = padMapper;
        this.netStoragePadHelper = netStoragePadHelper;
        this.harborConfigManage = harborConfigManage;
        this.realPhoneTemplateMapper = realPhoneTemplateMapper;
        this.adiCertificateManager = adiCertificateManager;
        this.configurationMapper = configurationMapper;
        this.screenLayoutMapper = screenLayoutMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        this.adiCertificateRepositoryMapper = adiCertificateRepositoryMapper;
        this.customerUploadImageMapper = customerUploadImageMapper;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CONTAINER_NET_STORAGE_ON.getType(), "padNetStorageOnTaskExecuteHandler");
    }
}
