package net.armcloud.paascenter.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfiguration;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceTaskResultMQ;
import net.armcloud.paascenter.common.model.mq.container.ContainerInstanceTaskResultMQ;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.openapi.mapper.EdgeClusterConfigurationMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.vo.PadAndDeviceInfoVO;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.model.dto.PullEdgeClusterConfigurationDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskHealthDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskResultDTO;
import net.armcloud.paascenter.task.model.vo.PullTaskVO;
import net.armcloud.paascenter.task.service.IPullTaskService;
import net.armcloud.paascenter.task.service.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 拉模式下任务业务层
 */
@Slf4j
@Service
public class PullTaskServerImpl implements IPullTaskService {

    @Autowired
    private TaskQueueManager taskQueueManager;
    @Autowired
    private DefaultRocketMqProducerWrapper rocketMqProducerService;
    @Autowired
    private EdgeClusterConfigurationMapper edgeClusterConfigurationMapper;
    @Autowired
    private PadMapper padMapper;
    @Autowired
    private ITaskService taskService;

    @Value("${producer-topic.vcp-pull-task-submit-result}")
    private String pullTaskSubmitResultTopic;

    @Value("${producer-topic.vcp-pull-health}")
    private String pullHealthTopic;

    /**gameserver任务回调主题*/
    @Value("${producer-topic.vcp-pod-cmd-result}")
    private String vcpPodCmdResultTopic;
    /**实例任务回调主题*/
    @Value("${mq.container-instance-task-message.topic}")
    private String containerInstanceTaskMessageTopic;
    /**板卡任务回调主题*/
    @Value("${mq.container-device-task-message.topic}")
    private String containerDeviceTaskMessageTopic;

    /**
     * 获取待执行的任务列表
     * @param dto
     * @return
     */
    @Override
    public PullTaskVO taskList(PullTaskDTO dto) {
        TaskChannelEnum taskChannelEnum = TaskChannelEnum.fromCode(dto.getDeviceType());
        if(taskChannelEnum == null){
            throw new BasicException("无效的设备类型");
        }
        return taskQueueManager.getPadTaskPullMode(taskChannelEnum, dto.getClusterCode(),dto.getDeviceIp(),dto.getDeviceCode(),dto.getPullNum());
    }

    /**
     * 上报任务结果
     * PullTaskResultConsumer 消费
     * @param dto
     */
    @Override
    public void submitResult(PullTaskResultDTO dto) {
        rocketMqProducerService.producerNormalMessage(pullTaskSubmitResultTopic, JSON.toJSONString(dto));
    }

    @Override
    public void submitResultDirect(PullTaskResultDTO pullTaskResultDTO) {
        if (Objects.isNull(pullTaskResultDTO)) {
            return;
        }
        String mqJson = null;
        String topic = null;
        if(TaskChannelEnum.GAMESERVER.getCode().equals(pullTaskResultDTO.getDeviceType())){
            PadCmdResultMessage padCmdResultMessage = new PadCmdResultMessage();
            padCmdResultMessage.setPadCode(pullTaskResultDTO.getDeviceCode());
            padCmdResultMessage.setCommand(pullTaskResultDTO.getCommand());
            padCmdResultMessage.setJsonData(pullTaskResultDTO.getResult());
            padCmdResultMessage.setStatus(pullTaskResultDTO.getTaskStatus());
            padCmdResultMessage.setMsg(pullTaskResultDTO.getErrMsg());
            mqJson = JSON.toJSONString(padCmdResultMessage);
            topic = vcpPodCmdResultTopic;
        }else if(TaskChannelEnum.CBS.getCode().equals(pullTaskResultDTO.getDeviceType())){
            //是否操作板卡本身的任务
            boolean isOperateDevice = false;
            TaskTypeAndChannelEnum taskTypeAndChannelEnum = null;
            if(pullTaskResultDTO.getTaskType() != null){
                //cbs任务中存在操作实例和操作板卡本身的 两类任务分别存在pad_task和device_task表 所以需要cbs传递任务类型回来
                taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(pullTaskResultDTO.getTaskType());
                isOperateDevice = taskTypeAndChannelEnum != null && "device".equals(taskTypeAndChannelEnum.getSmallerType());
                log.info("判断任务类型isOperateDevice：{},入参：{}", isOperateDevice, JSONUtil.toJsonStr(pullTaskResultDTO));
            }

            if(isOperateDevice){
                ContainerDeviceTaskResultMQ containerDeviceTaskResultMQ = new ContainerDeviceTaskResultMQ();
                containerDeviceTaskResultMQ.setDeviceIp(pullTaskResultDTO.getDeviceIp());
                containerDeviceTaskResultMQ.setMasterTaskId(pullTaskResultDTO.getTaskId());
                containerDeviceTaskResultMQ.setMasterTaskStatus(pullTaskResultDTO.getTaskStatus());
                containerDeviceTaskResultMQ.setMsg(pullTaskResultDTO.getErrMsg());
                containerDeviceTaskResultMQ.setData(pullTaskResultDTO.getResult());
                containerDeviceTaskResultMQ.setPullMode(true);
                mqJson = JSON.toJSONString(containerDeviceTaskResultMQ);
                topic = containerDeviceTaskMessageTopic;
            }else{
                String padCode = pullTaskResultDTO.getDeviceCode();
                if(StrUtil.isEmpty(padCode)){
                    Pad pad = padMapper.selectPadByPadIp(pullTaskResultDTO.getDeviceIp(),pullTaskResultDTO.getClusterCode());
                    if(pad == null){
                        log.error("PullTaskResultConsumer cbs任务未获取到实例编号，{}",pullTaskResultDTO);
                    }else {
                        padCode = pad.getPadCode();
                    }
                }
                ContainerInstanceTaskResultMQ containerInstanceTaskResultMQ = new ContainerInstanceTaskResultMQ();
                containerInstanceTaskResultMQ.setPadCode(padCode);
                containerInstanceTaskResultMQ.setMasterTaskId(pullTaskResultDTO.getTaskId());
                containerInstanceTaskResultMQ.setMasterTaskStatus(pullTaskResultDTO.getTaskStatus());
                containerInstanceTaskResultMQ.setMsg(pullTaskResultDTO.getErrMsg());
                containerInstanceTaskResultMQ.setPullMode(true);
                containerInstanceTaskResultMQ.setResult(pullTaskResultDTO.getResult());
                mqJson = JSON.toJSONString(containerInstanceTaskResultMQ);
                topic = containerInstanceTaskMessageTopic;

                log.info("ContainerInstanceTaskResultMQ发送消息：pullTaskResultDTO入参：{},消息内容:{}",  JSONUtil.toJsonStr(pullTaskResultDTO),
                        JSONUtil.toJsonStr(containerInstanceTaskResultMQ));
            }

            //如果任务成功 则创建实例、一键新机、升级镜像 都需要存task_rel_instance_detail_image_succ
            taskService.saveDeviceInstanceSucc(pullTaskResultDTO.getTaskId(),taskTypeAndChannelEnum);
        }else if(TaskChannelEnum.BMC.getCode().equals(pullTaskResultDTO.getDeviceType())){
            taskService.updateBmcTask(pullTaskResultDTO);
        }
        if(StrUtil.isNotEmpty(topic)){
//            log.info("上报任务结果处理后发送消息：{}", mqJson);
            rocketMqProducerService.producerNormalMessage(topic, mqJson);
        }
    }

    /**
     * 健康状态上报
     * @param dto
     */
    @Override
    public void pullHealth(PullTaskHealthDTO dto) {
        String hasKey = null;
        if(StrUtil.isNotEmpty(dto.getDeviceIp())){
            hasKey = dto.getClusterCode() + "_" + dto.getDeviceIp();
        }else if(StrUtil.isNotEmpty(dto.getDeviceCode())){
            hasKey = dto.getClusterCode() + "_" + dto.getDeviceCode();
        }

        if(StrUtil.isEmpty(hasKey)){
            rocketMqProducerService.producerNormalMessage(pullHealthTopic, JSON.toJSONString(dto));
        }else{
            rocketMqProducerService.producerOrderlyNormalMessage(pullHealthTopic, JSON.toJSONString(dto),hasKey);
        }

    }

    /**
     * 获取集群配置
     * @param dto
     * @return
     */
    @Override
    public Map<String, String> edgeClusterConfiguration(PullEdgeClusterConfigurationDTO dto) {
        String clusterCode = dto.getClusterCode();
        if(StrUtil.isEmpty(clusterCode) && TaskChannelEnum.GAMESERVER.getCode().equals(dto.getDeviceType())){
            PadAndDeviceInfoVO padAndDeviceInfoVO = padMapper.selectPadAndDeviceInfo(dto.getDeviceCode());
            if(padAndDeviceInfoVO == null){
                return null;
            }
            clusterCode = padAndDeviceInfoVO.getClusterCode();
        }
        TaskChannelEnum taskChannelEnum = TaskChannelEnum.fromCode(dto.getDeviceType());
        if (taskChannelEnum == null) {
            return null;
        }
        //数据很少 直接代码中判断权限
        List<EdgeClusterConfiguration> edgeClusterConfigurationList = edgeClusterConfigurationMapper.selectList(new QueryWrapper<>(EdgeClusterConfiguration.class)
                .select("`key`","`value`","permission").eq("cluster_code",clusterCode).eq("delete_flag",0));
        if(CollUtil.isEmpty(edgeClusterConfigurationList)){
            return null;
        }
        edgeClusterConfigurationList =  edgeClusterConfigurationList.stream()
                .filter(edge -> (edge.getPermission() == 0 || (taskChannelEnum.getPermission() <= edge.getPermission() && (taskChannelEnum.getPermission() & edge.getPermission()) != 0)))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(edgeClusterConfigurationList)){
            return null;
        }
        Map<String, String> map = edgeClusterConfigurationList.stream().collect(Collectors.toMap(EdgeClusterConfiguration::getKey, EdgeClusterConfiguration::getValue,(key1 , key2) -> key1));
        return map;
    }
}