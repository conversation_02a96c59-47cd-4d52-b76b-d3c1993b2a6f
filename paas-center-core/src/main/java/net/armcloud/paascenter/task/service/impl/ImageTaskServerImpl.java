package net.armcloud.paascenter.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.model.dto.api.AddImageTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.TaskImageUploadDTO;
import net.armcloud.paascenter.common.model.entity.task.ImageTask;
import net.armcloud.paascenter.common.model.vo.api.TaskImageUploadVo;
import net.armcloud.paascenter.task.mapper.ImageTaskMapper;
import net.armcloud.paascenter.task.service.IImageTaskServer;
import net.armcloud.paascenter.task.service.ITaskService;
import net.armcloud.paascenter.task.utils.IdGenerateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class ImageTaskServerImpl extends ServiceImpl<ImageTaskMapper, ImageTask> implements IImageTaskServer {
    @Resource
    private ITaskService taskService;

    @Override
    public List<Integer> addImageTasks(List<AddImageTaskDTO> imageTaskDtoList) {
        List<Integer> tasks = new ArrayList<>();
        List<ImageTask> list = new ArrayList<>();
        imageTaskDtoList.forEach(imageTaskDto -> {
            Integer customerTaskId = taskService.getCustomerTaskId(imageTaskDto.getCustomerId());
            ImageTask imageTask = new ImageTask();
            imageTask.setUniqueId(IdGenerateUtils.generationSubTaskUniqueId(imageTaskDto.getCustomerImageId()));
            imageTask.setTaskSource(imageTaskDto.getTaskSource());
            imageTask.setCustomerId(imageTaskDto.getCustomerId());
            imageTask.setCustomerTaskId(customerTaskId);
            imageTask.setCustomerImageId(imageTaskDto.getCustomerImageId());
            imageTask.setTraceId(imageTaskDto.getTraceId());
            imageTask.setCreateBy(imageTaskDto.getCreateBy());
            imageTask.setStatus(imageTaskDto.getStatus());
            imageTask.setCreateBy(imageTaskDto.getCreateBy());
            imageTask.setErrorMsg(imageTaskDto.getErrorMsg());
            tasks.add(customerTaskId);
            list.add(imageTask);
        });
        boolean result = this.saveBatch(list);
        if (result) {
            return tasks;
        }else {
            return null;
        }
    }

    @Override
    public List<TaskImageUploadVo> taskList(TaskImageUploadDTO taskDetailsDTO) {
        return this.baseMapper.taskList(taskDetailsDTO);
    }
}
