package net.armcloud.paascenter.common.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class DeviceDestroyDTO {

    @ApiModelProperty(value = "物理机IP列表")
    @Size(min = 1, max = 128, message = "删除板卡数量请勿超过128")
    private List<String> deviceIps;

    @ApiModelProperty(value = "物理机编码列表")
    @Size(min = 1, max = 128, message = "删除板卡数量请勿超过128")
    private List<String> deviceCodes;

    @ApiModelProperty(value = "备注说明")
    @NotNull(message = "remark cannot null")
    private String remark;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "操作人")
    private String oprBy;

    @ApiModelProperty(value = "任务来源")
    private String sourceCode;
}
