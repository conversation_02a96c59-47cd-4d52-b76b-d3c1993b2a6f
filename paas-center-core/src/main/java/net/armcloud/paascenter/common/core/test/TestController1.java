package net.armcloud.paascenter.common.core.test;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.test.mapper.TestModelMapper;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.Random;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;

@RestController
@Slf4j
@RequestMapping("/paas-center-core")
public class TestController1 {
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private TestModelMapper testModelMapper;
    @Resource
    private PadMapper padMapper;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private TestService testService;
    @Resource
    private TaskExecutor examples;


    /**
     * 测试实例状态变更回调
     * @param param
     * @return
     */
    @PostMapping("/callbackOnline")
    public Result<Void> callbackOnline(@RequestBody PadWSStatusMessage param){
        testService.callbackOnline(param);
        return Result.ok();
    }


    /**
     * pad测试在线状态
     * @param param
     * @return
     */
    @PostMapping("/padtest")
    public Integer padtest(@RequestBody PadWSStatusMessage param){
        LambdaQueryWrapper<Pad> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.select(Pad::getOnline);
        queryWrapper.eq(Pad::getPadCode, param.getPadCode());
        List<Pad> pads = padMapper.selectList(queryWrapper);
        Integer online = null;
        if (CollUtil.isNotEmpty(pads)) {
            Pad pad = pads.get(0);
             online = pad.getOnline();
            log.info("pad online:{}", online);
        }
        return online;
    }




    @PostMapping("/testRedis")
    public String testRedis() {
        redisTemplate.opsForValue().set("CENTER-CORE", "5", Duration.ofSeconds(1000*60));
        return null;
    }

    @PostMapping("/getRedis")
    public String getRedis() {
        Object o = redisTemplate.opsForValue().get("CENTER-CORE");

        return o.toString();
    }

    @PostMapping("/testDB")
    public String testDB( @RequestBody JSONObject json) {
        log.info("testDB json:{}", json);
        List<Integer> test = testModelMapper.test();
        log.info("test:{}", test);
        return null;
    }

    @PostMapping("/testMq")
    public String testMq() {
        log.info("testMq");
        rocketMQTemplate.sendOneWay("testMq", new Random().nextInt(10000) + "");
        return "ok";
    }

    @PostMapping("/testError")
    public String testError( ) {
        int i = new Random().nextInt(10000);
        if (i < 5000) {
            int i1 = i / 0;
        }else {
            throw new BasicException(PROCESSING_FAILED);

        }
           return "ok";
    }


    @PostMapping("/testLog")
    public String testRetestLogdis() {
        log.info("testRetestLogdis1");
        log.info("testRetestLogdis2");
        return null;
    }





}
