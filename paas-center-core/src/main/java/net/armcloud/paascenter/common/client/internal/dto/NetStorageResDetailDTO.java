package net.armcloud.paascenter.common.client.internal.dto;

/**
 * <AUTHOR>
 * @Date 2025/3/19 21:01
 * @Description:
 */


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网存资源分配记录表
 */
@Data
@ApiModel(value = "NetStorageRes", description = "网存资源分配查询")
public class NetStorageResDetailDTO   {

    @ApiModelProperty(value = "用户ID")
    private Long customerId;
    @ApiModelProperty(value = "集群编码")
    private String clusterCode;


}
