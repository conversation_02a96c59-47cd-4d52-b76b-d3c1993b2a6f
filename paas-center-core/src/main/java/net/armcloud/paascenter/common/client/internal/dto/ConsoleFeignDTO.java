package net.armcloud.paascenter.common.client.internal.dto;

import net.armcloud.paascenter.common.client.internal.vo.SDKCustomerVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConsoleFeignDTO {
    private SDKUploadDTO sdkUploadDTO;
    private SDKCustomerVO sdkCustomerVO;
    private List<String> padCodes;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;

    /**
     * 上传文件类型
     */
    private Integer uploadFileOrAppType;
    /**
     * 黑白名单id
     */
    private Long customerAppClassifyId;
    /**
     * 客户id
     */
    private Long customerId;
}
