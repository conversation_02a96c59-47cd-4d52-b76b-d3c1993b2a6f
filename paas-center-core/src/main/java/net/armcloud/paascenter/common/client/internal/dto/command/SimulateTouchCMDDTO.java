package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.openapi.model.dto.SimulateTouchDTO;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.SIMULATE_TOUCH;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UPDATE_SIM;

@Getter
@Setter
@Accessors(chain = true)
public class SimulateTouchCMDDTO extends BasePadCMDDTO{
    /**容器宽度 必填*/
    private Integer simulateWidth;
    /**容器高度 必填*/
    private Integer simulateHeight;
    /**点击坐标组*/
    private List<Position> simulateList;


    @Data
    @Valid
    public static class Position {
        /**操作类型 0按下 1抬起 2触摸中*/
        private Integer actionType;
        /**点击的x坐标*/
        private Float x;
        /**点击的y坐标*/
        private Float y;
        /**多组坐标时，触发下一组点击坐标的等待间隔时间ms毫秒值*/
        private Integer nextPositionWaitTime;

        /**滚动距离  -1 下划  1 上划   */
        private Float swipe;

        /**事件   gestureSwipe 划动事件 gesture  触控事件 keystroke  按键事件 其他或不传默认按下抬起*/
        private String touchType;

        /**按键的code */
        private Integer keyCode;
    }

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));

        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(SIMULATE_TOUCH);
        padCMDForwardDTO.setSourceCode(sourceCode);
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}
