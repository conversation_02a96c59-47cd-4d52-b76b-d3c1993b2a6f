package net.armcloud.paascenter.common.client.component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.AddPadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.dto.api.AddPadTaskDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.service.ITaskService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.WAIT_EXECUTE;

@Slf4j
@Component
public class CommonPadTaskComponent {

    @Resource
    private ITaskService taskService;

    @Resource
    private PadMapper padMapper;


    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO) {
        return addPadTask(customerId, padCodes, taskType, null, JSON.toJSONString(padCMDForwardDTO), padCMDForwardDTO.getSourceCode());
    }

    public PadTaskBO addSyncPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO,Integer status) {
        return addSyncPadTask(customerId, padCodes, taskType, null, JSON.toJSONString(padCMDForwardDTO), padCMDForwardDTO.getSourceCode(),status);
    }

    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO,Consumer<AddPadTaskDTO> padTaskConsumer) {
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, JSON.toJSONString(padCMDForwardDTO), padCMDForwardDTO.getSourceCode());
    }


    public PadTaskBO addPadTaskWithPaas(long customerId, List<String> padCodes, TaskTypeConstants taskType, String queueContentJSON) {
        return addPadTask(customerId, padCodes, taskType, null, queueContentJSON, SourceTargetEnum.PAAS);
    }
    public PadTaskBO addPadTaskWithPaas(long customerId, List<String> padCodes, TaskTypeConstants taskType, String queueContentJSON,SourceTargetEnum sourceTargetEnum) {
        if (sourceTargetEnum == null) {
            sourceTargetEnum = SourceTargetEnum.PAAS;
        }
        return addPadTask(customerId, padCodes, taskType, null, queueContentJSON, sourceTargetEnum);
    }

    public PadTaskBO addPadTaskWithPaas(long customerId, List<String> padCodes, TaskTypeConstants taskType, String queueContentJSON,Consumer<AddPadTaskDTO> padTaskConsumer) {
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, SourceTargetEnum.PAAS);
    }

    public PadTaskBO addPadTaskWithPaas(long customerId, List<String> padCodes, TaskTypeConstants taskType, String queueContentJSON,Consumer<AddPadTaskDTO> padTaskConsumer,SourceTargetEnum sourceTargetEnum) {
        sourceTargetEnum = sourceTargetEnum == null ? SourceTargetEnum.PAAS : sourceTargetEnum;
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTargetEnum);
    }

    public PadTaskBO addReplacePadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON,SourceTargetEnum sourceTarget) {
        return addPadTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget);
    }

    /**
     * 添加带超时时间的任务(支持自定义超时时间)
     * @param customerId 客户id
     * @param padCodes 实例codes
     * @param taskType 任务类型
     * @param padTaskConsumer 自定义消费者
     * @param queueContentJSON 任务参数JSON字符串
     * @param sourceTarget 任务来源
     * @param timeout 自定义超时时间(秒)，会覆盖默认配置的超时时间，并且在任务被拉走后不会再刷新
     * @return 任务相关id对象
     */
    public PadTaskBO addTimeoutPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType, Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON,SourceTargetEnum sourceTarget, Integer timeout) {
        return addPadCMDTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget, null, null, timeout);
    }

    public PadTaskBO addPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget) {
        return addPadCMDTask(customerId,  padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget,null);
    }

    public PadTaskBO addSyncPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget,Integer taskStatus) {
        return addPadCMDTask(customerId,  padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget,null,taskStatus);
    }

    public PadTaskBO addPadTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget,
                                Map<String,String> prohibitPadCodeMap) {
        return addPadCMDTask(customerId,  padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget,prohibitPadCodeMap);
    }

    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                   Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON, SourceTargetEnum sourceTarget,
                                   Map<String,String> prohibitPadCodeMap) {
        return addPadCMDTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget, prohibitPadCodeMap,null);
    }


    /**
     * @param customerId         客户id
     * @param padCodes           实例codes
     * @param taskType           任务类型定义
     * @param padTaskConsumer    执行函数
     * @param queueContentJSON   任务请求参数 bo
     * @param sourceTarget       任务来源 paas / admin ...
     * @param prohibitPadCodeMap key=padCode value=任务备注  禁止执行的padCode 需要生成任务但无需执行的实例
     * @param taskStatus         ***慎用*** 任务状态 现在只有两种情况 默认就是待执行 ，还有一种是执行中(保留任务 直接由调用者下发到gameserver，回调逻辑保持不变)
     * @param timeout            自定义任务超时时间(秒)
     * @return 任务相关id对象
     */
    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                   Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON,
                                   SourceTargetEnum sourceTarget, Map<String, String> prohibitPadCodeMap,
                                   Integer taskStatus, Integer timeout) {
        log.info("添加pad任务, customerId: {}, padCodes: {}, taskType: {}, sourceTarget: {}, " +
                        "prohibitPadCodeMap: {}, taskStatus: {}, timeout: {}", customerId, JSON.toJSONString(padCodes), taskType, sourceTarget, JSON.toJSONString(prohibitPadCodeMap), taskStatus, timeout);

        AddPadTaskDTO dto = new AddPadTaskDTO();
        dto.setPadCodes(padCodes);
        dto.setType(taskType.getType());
        dto.setStatus(taskStatus == null ? WAIT_EXECUTE.getStatus() : taskStatus);
        dto.setCustomerId(customerId);
        dto.setSourceCode(sourceTarget.getCode());
        dto.setCreateBy(String.valueOf(customerId));
        dto.setQueueContentJSON(queueContentJSON);
        dto.setProhibitPadCodeMap(prohibitPadCodeMap);
        dto.setTimeout(timeout);
        if (padTaskConsumer != null) {
            padTaskConsumer.accept(dto);
        }

        List<AddPadTaskVO> padTasks = null;
        try {
            // 只有 gs 任务区分任务类型
            TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(taskType.getType());
            boolean isGsTask = taskTypeAndChannelEnum != null &&
                    TaskChannelEnum.GAMESERVER.getCode().equals(taskTypeAndChannelEnum.getChannel());

            if (CollUtil.isNotEmpty(padCodes)) {
                List<Pad> pads = padMapper.selectPadByPadCodes(padCodes);
                if (CollUtil.isNotEmpty(pads)) {
                    // 拉任务 实例
                    List<String> pullPadCodes = new ArrayList<>();
                    // 推任务 实例
                    List<String> pushPadCodes = new ArrayList<>();
                    for (Pad pad : pads) {
                        if ((pad.getTaskMode() != null && pad.getTaskMode() == 1) || !isGsTask) {
                            pullPadCodes.add(pad.getPadCode());
                        } else {
                            pushPadCodes.add(pad.getPadCode());
                        }
                    }
                    if (CollUtil.isNotEmpty(pullPadCodes)) {
                        dto.setPadCodes(pullPadCodes);
                        if (taskTypeAndChannelEnum.getSendDirectlyNotTask()) {
                            // 拉模式 直发 无任务
                            taskService.addGsDirectPadTaskServicePullMode(dto);
                        } else {
                            padTasks = taskService.addPadTaskServicePullMode(dto);
                        }
                    }
                    if (CollUtil.isNotEmpty(pushPadCodes)) {
                        // 深拷贝
                        AddPadTaskDTO pushAddPadTaskDTO = BeanUtil.copyProperties(dto, AddPadTaskDTO.class);
                        pushAddPadTaskDTO.setPadCodes(pushPadCodes);
                        padTasks = taskService.addPadTaskService(pushAddPadTaskDTO);
                    }
                }
            }
        } catch (Exception e) {
            log.error("addPadTask error>>> customerId:{} padCodes:{} taskType:{}",
                    customerId, JSON.toJSONString(padCodes), taskType.getType(), e);
            throw new BasicException();
        }

        // 网存存储操作 (通过padCode找不到)
        if (TaskTypeConstants.CONTAINER_NET_STORAGE_BACKUP.getType().equals(taskType.getType()) ||
                TaskTypeConstants.CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType().equals(taskType.getType())) {
            padTasks = taskService.addPadTaskServicePullMode(dto);
        }

        if (CollectionUtils.isEmpty(padTasks)) {
            return new PadTaskBO();
        }

        // 封装返回结果
        List<PadTaskBO.PadSubTaskBO> subTaskBOS = new ArrayList<>(padTasks.size());
        padTasks.forEach(padTask -> {
            String padCode = padTask.getPadCode();
            PadTaskBO.PadSubTaskBO subTaskBO = new PadTaskBO.PadSubTaskBO();
            subTaskBO.setPadCode(padCode);
            subTaskBO.setSubTaskStatus(padTask.getSubTaskStatus());
            subTaskBO.setMasterTaskId(padTask.getMasterTaskId());
            subTaskBO.setMasterTaskUniqueId(padTask.getMasterUniqueId());
            subTaskBO.setSubTaskId(padTask.getSubTaskId());
            subTaskBO.setSubTaskUniqueId(padTask.getSubTaskUniqueId());
            subTaskBO.setCustomerTaskId(padTask.getCustomerTaskId());
            subTaskBO.setOnline(padTask.getOnline());
            subTaskBO.setErrMsg(padTask.getErrMsg());
            subTaskBOS.add(subTaskBO);
        });

        PadTaskBO masterTask = new PadTaskBO();
        masterTask.setMasterTaskId(padTasks.get(0).getMasterTaskId());
        masterTask.setMasterTaskUniqueId(padTasks.get(0).getMasterUniqueId());
        masterTask.setSubTasks(subTaskBOS);
        return masterTask;
    }

    /**
     * @param customerId         客户id
     * @param padCodes           实例codes
     * @param taskType           任务类型定义
     * @param padTaskConsumer    执行函数
     * @param queueContentJSON   任务请求参数 bo
     * @param sourceTarget       任务来源 paas / admin ...
     * @param prohibitPadCodeMap key=padCode value=任务备注  禁止执行的padCode 需要生成任务但无需执行的实例
     * @param taskStatus         ***慎用*** 任务状态 现在只有两种情况 默认就是待执行 ，还有一种是执行中(保留任务 直接由调用者下发到gameserver，回调逻辑保持不变)
     * @return 任务相关id对象
     */
    public PadTaskBO addPadCMDTask(long customerId, List<String> padCodes, TaskTypeConstants taskType,
                                   Consumer<AddPadTaskDTO> padTaskConsumer, String queueContentJSON,
                                   SourceTargetEnum sourceTarget, Map<String, String> prohibitPadCodeMap,
                                   Integer taskStatus) {
        return addPadCMDTask(customerId, padCodes, taskType, padTaskConsumer, queueContentJSON, sourceTarget, prohibitPadCodeMap, taskStatus, null);
    }
}