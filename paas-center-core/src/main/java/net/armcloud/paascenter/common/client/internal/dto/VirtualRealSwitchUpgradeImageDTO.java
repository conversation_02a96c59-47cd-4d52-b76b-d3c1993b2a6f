package net.armcloud.paascenter.common.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.core.constant.pad.PadConstants;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 升级真机镜像dto
 */
@Getter
@Setter
public class VirtualRealSwitchUpgradeImageDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1,max = 200,message = "实例数量不多于200个")
    private List<String> padCodes;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "镜像ID")
    @NotNull(message = "imageId cannot null")
    private String imageId;

    @ApiModelProperty(value = "是否清除实例数据(data分区), true清除，false不清除", required = true)
    @NotNull(message = "wipeData cannot null")
    private Boolean wipeData;

    @ApiModelProperty(value = "真机模板ID")
    private Long realPhoneTemplateId;

    /**
     * 转换类型
     * {@link PadConstants.Type}
     */
    @ApiModelProperty(value = "转换类型")
    private String upgradeImageConvertType;
    @ApiModelProperty(value = "屏幕布局ID")
    private Long screenLayoutId;

    /**
     * userId
     */
    @ApiModelProperty(hidden = true)
    private Long userId;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;

    public Boolean checkParam(){
        Boolean checkStatus = true;
        if(PadConstants.Type.REAL.getValue().equals(upgradeImageConvertType)){
            if(realPhoneTemplateId == null){
                checkStatus  = false;
            }
        }else if(PadConstants.Type.VIRTUAL.getValue().equals(upgradeImageConvertType)){
            realPhoneTemplateId = null;
        }else {
            checkStatus = false;
        }
        return checkStatus;
    }
}