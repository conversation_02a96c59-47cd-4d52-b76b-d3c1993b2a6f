package net.armcloud.paascenter.common.redis.lock;

import net.armcloud.paascenter.common.core.exception.BasicException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.LOCK_ACQUISITION_FAILED;

@Component
public class RedissonDistributedLock {

    private final RedissonClient redissonClient;

    public RLock mustLocked(String lockKey) {
        return mustLocked(lockKey, 1, 5);
    }

    public RLock mustLocked(String lockKey, long waitTime, long leaseTime) {
        RLock rLock = tryLock(lockKey, waitTime, leaseTime);
        if (Objects.isNull(rLock)) {
            throw new BasicException(LOCK_ACQUISITION_FAILED);
        }

        return rLock;
    }

    /**
     * 加锁
     *
     * @param lockKey   锁key
     * @param waitTime  获取锁最大等待时间，单位：秒
     * @param leaseTime 锁占用时间，单位：秒
     */
    public RLock tryLock(String lockKey, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                return lock;
            }

            return null;
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }

    /**
     * 判断当前线程是否已拿到此锁
     *
     * @param lockKey 锁key
     */
    public boolean isHeldByCurrentThread(String lockKey) {
        RLock rLock = redissonClient.getLock(lockKey);
        if (Objects.isNull(rLock)) {
            return false;
        }

        return rLock.isHeldByCurrentThread();
    }

    public void unlock(RLock lock) {
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    public RedissonDistributedLock(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

}