package net.armcloud.paascenter.common.client.internal.dto.command;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.utils.MD5Utils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UPLOAD_PREVIEW;

@Getter
@Setter
@Accessors(chain = true)
public class UploadPreviewCMDDTO extends BasePadCMDDTO {
    /**
     * 截图画面横竖屏旋转
     * <p>
     * 0：截图方向不做处理，默认
     * 1：截图画面旋转为竖屏：
     * 1.1 手机竖屏的截图，不做处理
     * 1.2 手机横屏的截图，截图顺时针旋转90度
     */
    private Integer rotation;

    /**
     * 事件是否广播
     */
    private Boolean broadcast;

    /**
     * 文件存储路径
     */
    private String path;

    /**
     * 文件过期时间
     */
    private Long expirationTimestamp;

    /**
     * 清晰度 0-100
     */
    private Integer definition;
    /**
     * 分辨率 - 高
     */
    private Integer resolutionHeight;
    /**
     * 分辨率 - 宽
     */
    private Integer resolutionWidth;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, long imageExpirationSecond) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPLOAD_PREVIEW);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> {
            UploadPreviewCMDDTO newData = JSON.parseObject(JSON.toJSONString(this), UploadPreviewCMDDTO.class);
            // 存储地址
            newData.setPath("/screenshot/" + LocalDate.now() + "/" + MD5Utils.generateMD5(System.currentTimeMillis() + padCode) + ".png");
            // 过期时间
            newData.setExpirationTimestamp(System.currentTimeMillis() + imageExpirationSecond);
            padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(newData).setPadCode(padCode));
        });
        padCMDForwardDTO.setPadInfos(padInfos);

        return padCMDForwardDTO;
    }
}
