package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.client.internal.dto.ArmServerStatusDTO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.AddDeviceTaskDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ArmServerInternalFacade {
    @PostMapping(value = "/openapi/internal/device/callbackArmServerStatus")
    Result<?> armServerStatusCallback(@RequestBody ArmServerStatusDTO armServerStatusDTO);

    @PostMapping(value = "/openapi/internal/device/createDevice")
    Result<?> createDevice(@RequestBody AddDeviceTaskDTO addDeviceTaskDTO);
}
