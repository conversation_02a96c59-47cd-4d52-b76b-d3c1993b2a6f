package net.armcloud.paascenter.common.client.internal.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/17 16:35
 * @Description:
 */
@Data
public class DeviceBoardImageWarmupDTO {

    private String deviceCode;

    private Long customerId;

    /**
     * 每批次预热数量
     */
    private Integer batchSize;

    /**
     * 延迟步长，单位：秒
     */
    private Long sleepTime;

    private List<String> imageId;

    private String clusterCode;

    private String armServerCodeList;

    private String deviceCodeList;
}
