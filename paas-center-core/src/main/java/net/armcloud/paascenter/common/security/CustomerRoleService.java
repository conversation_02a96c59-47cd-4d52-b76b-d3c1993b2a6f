package net.armcloud.paascenter.common.security;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;

@Slf4j
@Component
public class CustomerRoleService {

    
    @Autowired
    private RedisService redisService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @PostConstruct
    public void init() {
        try {
            // 直接写sql查询所有的用户信息
            String sql = "select ct.id as customer_id, CAST(rl.is_admin AS SIGNED) as is_admin from customer ct left JOIN sys_user_role sr on ct.id = sr.user_id left join sys_role rl on sr.role_id = rl.role_id where ct.`status` = 1 and rl.del_flag = 0";
            List<Map<String, Object>> userRoles = jdbcTemplate.queryForList(sql);
            
            if (userRoles != null && !userRoles.isEmpty()) {
                log.info("Successfully retrieved {} customer roles from database", userRoles.size());
                // 将用户角色信息缓存到redis中
                for (Map<String, Object> userRole : userRoles) {
                    String customerId = userRole.get("customer_id").toString();
                    String isAdmin = userRole.get("is_admin").toString();
                    redisService.setCacheMapValue(RedisKeyPrefix.USER_ROLES_PREFIX + customerId, "is_admin", isAdmin);

                    log.info("cache customer role customerId: {}, isAdmin: {}", customerId, isAdmin);
                }
            } else {
                log.warn("No customer roles found in database");
            }
        } catch (Exception e) {
            log.error("Error querying customer roles from database: {}", e.getMessage(), e);
        }
    }
    
}
