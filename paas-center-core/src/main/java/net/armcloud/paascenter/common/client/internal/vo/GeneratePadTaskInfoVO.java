package net.armcloud.paascenter.common.client.internal.vo;

import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


@Data
public class GeneratePadTaskInfoVO {
    @ApiModelProperty(value = "任务id")
    private Integer taskId;

    @ApiModelProperty(value = "实例编号")
    private String padCode;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    public static List<GeneratePadTaskInfoVO> builder(PadTaskBO padTaskBO) {
        if (Objects.isNull(padTaskBO)) {
            return Collections.emptyList();
        }

        List<PadTaskBO.PadSubTaskBO> subTasks = padTaskBO.getSubTasks();
        List<GeneratePadTaskInfoVO> result = new ArrayList<>(subTasks.size());
        subTasks.forEach(subTaskBO -> {
            GeneratePadTaskInfoVO generatePadTaskVO = new GeneratePadTaskInfoVO();
            generatePadTaskVO.setTaskId(subTaskBO.getCustomerTaskId());
            generatePadTaskVO.setPadCode(subTaskBO.getPadCode());
            generatePadTaskVO.setErrorMsg(subTaskBO.getErrMsg());
            result.add(generatePadTaskVO);
        });

        return result;
    }
}
