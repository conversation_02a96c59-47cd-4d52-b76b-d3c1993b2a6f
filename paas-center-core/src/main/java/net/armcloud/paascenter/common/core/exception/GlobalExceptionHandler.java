package net.armcloud.paascenter.common.core.exception;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.*;


@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 接口请求参数异常
     */
    @ExceptionHandler(value = BindException.class)
    public final Result handleBindException(BindException ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        printErrorLog(ex, request, httpServletRequest);
        String errorMsg = Optional.ofNullable(ex.getFieldError()).map(FieldError::getDefaultMessage).orElse(PARAMETER_EXCEPTION.getMsg());
        return Result.fail(PARAMETER_EXCEPTION.getStatus(), errorMsg);
    }

    /**
     * 未知异常
     */
    @ExceptionHandler(value = Exception.class)
    public final Result handleException(Exception ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        finallyErrorLog(ex, request, httpServletRequest);
        return Result.fail(BasicExceptionCode.SYSTEM_EXCEPTION.getStatus(), BasicExceptionCode.SYSTEM_EXCEPTION.getMsg());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(value = BasicException.class)
    public final Result handleBasicException(BasicException ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        printBasicErrorLog(ex, request, httpServletRequest);
        return Result.fail(ex.getExceptionCode().getStatus(), ex.getExceptionCode().getMsg());
    }

    /**
     * 接口请求方式不支持异常
     */
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public final Result handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        printErrorLog(ex, request, httpServletRequest);
        String method = ex.getMethod();
        return Result.fail(INTERFACE_NOT_SUPPORT_HTTP_METHOD.getStatus(), INTERFACE_NOT_SUPPORT_HTTP_METHOD.getMsg() + ":" + method);
    }

    /**
     * 接口请求参数格式不正确异常
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public final Result handleBasicException(HttpMessageNotReadableException ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        printErrorLog(ex, request, httpServletRequest);
        return Result.fail(PARAMETER_TYPE_ERROR);
    }

    private String getRequestBody(HttpServletRequest request) {
        StringBuilder requestBody = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));) {
            requestBody.append(reader.lines().collect(Collectors.joining(System.lineSeparator())));
        } catch (Exception e) {
            log.error("getRequestBody >>> url:{}", request.getRequestURI(), e);
            return null;
        }

        return requestBody.toString();
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result handleValidationExceptions(MethodArgumentNotValidException ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {

        printMethodArgumentNotValidExceptionLog(ex,request,httpServletRequest);
        List<String> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        return Result.fail("参数验证失败：" + String.join(", ", errors));
    }
    protected void printErrorLog(Exception ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        String requestBody = getRequestBody(httpServletRequest);
        log.error("invoke exception -> [uri={} params={} requestBody={} msg={}]",
                request.getRequest().getRequestURI(), JSON.toJSONString(request.getParameterMap()),
                requestBody, ex.getMessage(), ex);
    }

    protected void finallyErrorLog(Exception ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        String requestBody = getRequestBody(httpServletRequest);
        log.error("finally exception -> [uri={} params={} requestBody={}]",
                request.getRequest().getRequestURI(), JSON.toJSONString(request.getParameterMap()),
                requestBody, ex);
    }

    protected void printBasicErrorLog(BasicException ex, ServletWebRequest request, HttpServletRequest httpServletRequest) {
        // 获取请求 URL
        String url = httpServletRequest.getRequestURI();

        // 获取请求参数
        String requestParams = JSON.toJSONString(httpServletRequest.getParameterMap());

        // 获取请求体内容
        String requestBody = getRequestBody(httpServletRequest);

        // 获取堆栈信息并提取出类名和方法名
        StackTraceElement[] stackTrace = ex.getStackTrace();
        String className = null;
        String methodName = null;
        if (stackTrace.length > 0) {
            className = stackTrace[0].getClassName();  // 获取类名
            methodName = stackTrace[0].getMethodName(); // 获取方法名
        }

        // 记录错误日志
        log.warn("handleBasicErrorLog -> [url={} params={} requestBody={} msg={} className={} method={}]",
                url, requestParams, requestBody, ex.getMessage(), className, methodName);
    }


    protected void printMethodArgumentNotValidExceptionLog(MethodArgumentNotValidException ex,
                                                           ServletWebRequest request,
                                                           HttpServletRequest httpServletRequest) {
        // 获取请求 URL
        String url = httpServletRequest.getRequestURI();

        // 获取请求参数对象
        Object requestParams = null;
        if (ex.getBindingResult().getTarget() != null) {
            requestParams = ex.getBindingResult().getTarget();
        }

        // 获取 Controller 方法所在的类和方法
        String className = ex.getParameter().getDeclaringClass().getSimpleName();
        String methodName = ex.getParameter().getMethod().getName();

        // 解析校验失败的字段、错误信息、传入值
        List<String> errorsDesc = ex.getBindingResult().getFieldErrors().stream()
                .map(fieldError ->
                        "字段: " + fieldError.getField() +
                                " | 错误: " + fieldError.getDefaultMessage() +
                                " | 传入值: " + fieldError.getRejectedValue()
                )
                .collect(Collectors.toList());

        // 提取异常字段对应的类
        String exceptionFieldClass = ex.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> fieldError.getObjectName() + "." + fieldError.getField())
                .distinct()
                .collect(Collectors.joining(", "));

        // 记录错误日志
        log.error("handleValidationExceptions -> url:{} | params:{} | className:{}| method:{}  | errors:{} | exceptionField:{}",
                url, JSON.toJSONString(requestParams), className, methodName, errorsDesc, exceptionFieldClass);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public final Result<?> handleMissingServletRequestParameter(MissingServletRequestParameterException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String paramName = e.getParameterName();
        log.error("请求地址'{}',缺少必需的请求参数'{}'", requestURI, paramName);
        // 使用统一的错误码，保持与PARAMETER_EXCEPTION一致的处理方式
        return Result.fail(PARAMETER_EXCEPTION.getStatus(),
                String.format("缺少必需的请求参数[%s]", paramName));
    }

}
