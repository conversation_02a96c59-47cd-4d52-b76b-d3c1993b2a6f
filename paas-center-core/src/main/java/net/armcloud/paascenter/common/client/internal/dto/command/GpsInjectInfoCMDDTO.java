package net.armcloud.paascenter.common.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.GPS_INJECT_INFO;

@Getter
@Setter
@Accessors(chain = true)
public class GpsInjectInfoCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "longitude cannot null")
    private Float longitude;

    @NotBlank(message = "latitude cannot null")
    private Float latitude;

    @NotBlank(message = "altitude cannot null")
    private Float altitude;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(GPS_INJECT_INFO);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);

        return padCMDForwardDTO;
    }
}
