package net.armcloud.paascenter.common.rocketmq.configure;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InitialLoadingProducer {

    @Bean
    public Producer alimqProducer() {
        Producer producer = null;
        try {
            ClientServiceProvider clientServiceProvider = SpringUtil.getBean(ClientServiceProvider.class);
            ClientConfiguration clientConfiguration = SpringUtil.getBean(ClientConfiguration.class);
            producer = clientServiceProvider.newProducerBuilder().setClientConfiguration(clientConfiguration)
                    //如果生产者没有初始化，可能会抛出{@link ClientException}。
                    .build();
            log.info("Initialized loading RocketMQ producer Success 》》》》");

        } catch (Exception e) {
            log.info("Initialized loading RocketMQ producer Failed !!! ERROR={}", e.getMessage());
        }
        return producer;
    }


}
