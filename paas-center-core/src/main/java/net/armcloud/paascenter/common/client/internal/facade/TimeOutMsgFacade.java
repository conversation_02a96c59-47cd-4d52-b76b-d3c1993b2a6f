package net.armcloud.paascenter.common.client.internal.facade;


import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.InstallAppTaskTimeOutMsgDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface TimeOutMsgFacade {
    @PostMapping("/callback/open/timeOut/cmdCallBack")
    Result<String> cmdCallBack(@RequestBody InstallAppTaskTimeOutMsgDTO dto);
}
