package net.armcloud.paascenter.common.client.internal.dto.command;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.*;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UPDATE_BLACK_LIST;

@Getter
@Setter
@Accessors(chain = true)
public class BlackListCMDDTO extends BasePadCMDDTO {

    /**
     * 黑名单包名列表
     * app_black表  规格维度
     */
    private List<String> blacklists;

    /**
     * 新黑白名单中各实例自定义黑名单
     * customer_app_classify_relation表 padCode维度
     * key 为黑白名单id
     * value 为包名黑名单列表
     */
    private Map<Long,List<String>> padBlackMap;

    /**
     * 新黑白名单实例和黑白名单对应关系
     * key 为实例编号
     * value 为新黑白名单id
     */
    private Map<String,List<Long>> padAppClassifyMap;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, String oprBy) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPDATE_BLACK_LIST);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(buildAppBlackListBlackListCMDDTO(padCode,this)).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        padCMDForwardDTO.setOprBy(oprBy);

        return padCMDForwardDTO;
    }

    /**
     * 由于触发黑名单由老黑名单和新黑名单组成 这里需要进行拆分
     * @param padCode
     * @param blackListCMDDTO 包含了所有padcode的黑名单
     * @return
     */
    private BlackListCMDDTO buildAppBlackListBlackListCMDDTO(String padCode,BlackListCMDDTO blackListCMDDTO){
        BlackListCMDDTO padCodeBlackListCMDDTO = BeanUtil.copyProperties(blackListCMDDTO,BlackListCMDDTO.class);
        padCodeBlackListCMDDTO.setPadAppClassifyMap(null);
        padCodeBlackListCMDDTO.setPadBlackMap(null);
        padCodeBlackListCMDDTO.setBlacklists(null);

        List<String> blackPckNames = new ArrayList<>();
        if(CollUtil.isNotEmpty(blackListCMDDTO.getBlacklists())){
            blackPckNames.addAll(blackListCMDDTO.getBlacklists());
        }
        if(CollUtil.isNotEmpty(blackListCMDDTO.getPadAppClassifyMap())){
            List<Long> padAppClassifys = blackListCMDDTO.getPadAppClassifyMap().get(padCode);
            if(CollUtil.isNotEmpty(padAppClassifys) && CollUtil.isNotEmpty(blackListCMDDTO.getPadBlackMap())){
                for(Long appClassifyId : padAppClassifys){
                    List<String> padCodeBlackList = blackListCMDDTO.getPadBlackMap().get(appClassifyId);
                    if(CollUtil.isNotEmpty(padCodeBlackList)){
                        blackPckNames.addAll(padCodeBlackList);
                    }
                }
            }
        }
        padCodeBlackListCMDDTO.setBlacklists(blackPckNames.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o))), ArrayList::new)));
        return padCodeBlackListCMDDTO;
    }
}
