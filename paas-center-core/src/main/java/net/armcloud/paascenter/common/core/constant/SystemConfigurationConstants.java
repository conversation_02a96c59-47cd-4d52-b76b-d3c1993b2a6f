package net.armcloud.paascenter.common.core.constant;

public class SystemConfigurationConstants {
    public static final String ENABLE_STANDARD_ROUTE = "enable_standard_route";
    public static final String HARBOR_CONFIGURATION = "harbor_configuration";
    public static final String GAME_SERVICE_INTERFACE_DOMAIN = "game_service_interface_domain";
    public static final String P2P_PEER_TO_PEER_PUSH_STREAM = "p2p_peer_to_peer_push_stream";
    public static final String GLOBAL_PUSH_STREAM_TYPE = "global_push_stream_type";
    public static final String DINGTALK_WARN_WEBHOOK = "dingtalk_warn_webhook";
    public static final String PLATFORM_NAME = "platform_name";
    public static final String PLATFORM_LOGO = "platform_logo";
    public static final String INSTANCE_DEFAULT_DNS = "instance_default_dns";
    public static final String MINIO_CONFIGURATION = "minio_configuration";
    public static final String HARBOR_PROJECT_NAME = "harbor.project.name";
    public static final String HARBOR_SERVER = "harbor.server";
    public static final String HARBOR_PROJECT_PROXY_NAME = "harbor.project.proxy.name";
    public static final String HARBOR_AUTH_USERNAME = "harbor.auth.username";
    public static final String HARBOR_AUTH_PASSWORD = "harbor.auth.password";

}


