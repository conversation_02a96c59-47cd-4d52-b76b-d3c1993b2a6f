package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BasePadCMDDTO {
    private String requestId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 子任务id
     */
    private Long subTaskId;
}
