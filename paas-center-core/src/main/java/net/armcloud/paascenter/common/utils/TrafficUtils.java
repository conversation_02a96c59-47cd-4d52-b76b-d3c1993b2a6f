package net.armcloud.paascenter.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class TrafficUtils {

    private static final BigDecimal FACTOR = new BigDecimal("8").divide(new BigDecimal("300")).divide(new BigDecimal("1000")).divide(new BigDecimal("1000"));

    /**
     * 计算公网带宽输出值
     * @param publicOut 公网带宽输出值
     * @return 计算后的值
     */
    public static BigDecimal calculatePublicBandwidthOut(Long publicOut) {
        BigDecimal publicOutBigDecimal = publicOut != null ? new BigDecimal(publicOut) : BigDecimal.ZERO;
        return publicOutBigDecimal.multiply(FACTOR).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算公网带宽输入值
     * @param publicIn 公网带宽输入值
     * @return 计算后的值
     */
    public static BigDecimal calculatePublicBandwidthIn(Long publicIn) {
        BigDecimal publicInBigDecimal = publicIn != null ? new BigDecimal(publicIn) : BigDecimal.ZERO;
        return publicInBigDecimal.multiply(FACTOR).setScale(2, RoundingMode.HALF_UP);
    }
}
