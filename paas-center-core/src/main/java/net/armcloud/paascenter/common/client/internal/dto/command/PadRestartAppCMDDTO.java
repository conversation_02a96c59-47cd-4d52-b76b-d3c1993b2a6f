package net.armcloud.paascenter.common.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.RESTART_APP_CMD;

@Getter
@Setter
@Accessors(chain = true)
public class PadRestartAppCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "packageName cannot null")
    private String packageName;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(RESTART_APP_CMD);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);

        return padCMDForwardDTO;
    }
}
