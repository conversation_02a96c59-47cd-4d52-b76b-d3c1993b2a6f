package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
public class PushFlowCenterCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "roomCode cannot null")
    private String roomCode;

    @NotNull(message = "userId cannot null")
    private String userId;

    @NotBlank(message = "token cannot null")
    private String token;

    @NotBlank(message = "rtcAppId cannot null")
    private String rtcAppId;

    @NotNull(message = "videoStream cannot null")
    private VideoStream videoStream;

    @Getter
    @Setter
    public static class VideoStream {
        /**
         * 视频分辨率
         */
        private String resolution;

        /**
         * 帧率
         */
        private String frameRate;
        /**
         * 码率
         */
        private String bitrate;
    }
}
