package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.client.internal.dto.CallbackDeviceTakes;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface DeviceTaskInternalFacade {
    @PostMapping("/task/internal/device/callbackDeviceTask")
    public Result<?> callbackDeviceTakes(@RequestBody CallbackDeviceTakes param);
}
