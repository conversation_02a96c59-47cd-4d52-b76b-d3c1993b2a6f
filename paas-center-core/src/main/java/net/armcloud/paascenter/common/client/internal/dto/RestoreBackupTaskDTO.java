package net.armcloud.paascenter.common.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class RestoreBackupTaskDTO {
    private Long customerId;

    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @Valid
    @NotNull(message = "pads cannot null")
    @Size(min = 1, message = "pads cannot null")
    private List<Pad> pads;

    @Data
    public static class Pad {
        @NotBlank(message = "padCode cannot null")
        private String padCode;

        @NotNull(message = "deviceId cannot null")
        private Long deviceId;

        @NotBlank(message = "backupName cannot null")
        private Long backupId;
    }
}
