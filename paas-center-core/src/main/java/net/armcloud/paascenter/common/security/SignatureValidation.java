package net.armcloud.paascenter.common.security;//package net.armcloud.paascenter.common.security.annotation;
//
//import java.lang.annotation.ElementType;
//import java.lang.annotation.Retention;
//import java.lang.annotation.RetentionPolicy;
//import java.lang.annotation.Target;
//
///**
// * paas-自定义签名注解
// */
//@Target(ElementType.METHOD)
//@Retention(RetentionPolicy.RUNTIME)
//public @interface SignatureValidation {
//}
