package net.armcloud.paascenter.common.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.CHANGE_TIME_ZONE;

@Getter
@Setter
@Accessors(chain = true)
public class UpdatePadTimeZoneCMDDTO extends BasePadCMDDTO{
    private String timeZone;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));

        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(CHANGE_TIME_ZONE);
        padCMDForwardDTO.setSourceCode(sourceCode);
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}
