package net.armcloud.paascenter.common.core.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadStatusMapper;
import net.armcloud.paascenter.openapi.service.impl.PadStatusServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class TestService {
    @Resource private PadMapper padMapper;
    @Resource private PadStatusMapper padStatusMapper;
    @Resource private PadStatusServiceImpl padStatusService;

    public void test33(){
        log.info("testRetestLogdis3");
    }

    public void callbackOnline(PadWSStatusMessage message) {
        log.info("实例连接状态变更回调准备 {}", JSONUtil.toJsonStr(message));
        if (StrUtil.isNotEmpty(message.getPadCode())) {
            LambdaQueryWrapper<Pad> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.in(Pad::getPadCode, message.getPadCode());
            List<Pad> pads = padMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(pads)) {
                Pad pad = pads.get(0);
                Integer msgOnline = null;
                Boolean connected = message.getConnected();
                if (connected) {
                    msgOnline = 1;
                } else {
                    msgOnline = 0;
                }
                log.info("实例状态条件判断，入参：{}，pad在线状态：{}，入参在线状态：{}", JSONUtil.toJsonStr(message),pad.getOnline(),msgOnline);
                if (!pad.getOnline().equals(msgOnline)) {
                    log.info("实例连接状态回调满足条件 {}", JSONUtil.toJsonStr(message));
                    List<String> padCodes = new ArrayList<String>();
                    padCodes.add(message.getPadCode());
                    List<PadStatus> padStatuseList = padStatusMapper.listByPadCodes(padCodes);
                    if (CollUtil.isNotEmpty(padStatuseList)) {
                        PadStatus padStatus = padStatuseList.get(0);
                        padStatusService.sendPadStatusService(pad.getCustomerId(), padStatus.getPadStatus(), msgOnline, padCodes);
                    }
                }
            }
        }
    }
}
