package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 应用分类
 */
public interface CustomerNewAppClassifyFacade {

    /**
     * 根据appIds 查询类别名称
     * @param dto
     * @return
     */
    @PostMapping(value = "/openapi/internal/customer/app/newClassify")
    Result<List<NewAppClassifyNameVO>> queryAppNewClassifyName(@RequestBody QueryNewAppClassifyNameDTO dto);
}
