package net.armcloud.paascenter.common.client.internal.dto.command;

import net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class PadCMDForwardDTO {
    @NotNull(message = "command cannot null")
    private CommsCommandEnum command;

    @NotNull(message = "sourceCode cannot null")
    private SourceTargetEnum sourceCode;

    @Valid
    @Size(min = 1, message = "pad列表 cannot null")
    @NotNull(message = "pad列表 cannot null")
    private List<PadInfoDTO> padInfos;

    @ApiModelProperty(value = "是否授予全部权限  默认为true")
    private Boolean isGrantAllPerm;
    /**
     * 特定业务字段需移动到子类
     */
    @Deprecated
    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    /**
     * 特定业务字段需移动到子类
     */
    @Deprecated
    @ApiModelProperty(value = "是否清除实例数据")
    private Boolean wipeData;

    @ApiModelProperty(value = "任务内容")
    private String taskContent;

    /**
     * 客户端文件id
     * <p>
     * 特定业务字段需移动到子类
     */
    @Deprecated
    private Long customerFileId;

    @ApiModelProperty(value = "操作者")
    private String oprBy;

    @Getter
    @Setter
    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PadInfoDTO {
        @NotBlank(message = "padCode cannot null")
        private String padCode;

        /**
         * 从{@link BasePadCMDDTO} 继承
         */
        @Valid
        private Object data;
    }
}
