package net.armcloud.paascenter.common.client.internal.feign;

import net.armcloud.paascenter.cms.model.request.*;
import net.armcloud.paascenter.cms.model.response.*;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.net.URI;
import java.util.List;


@FeignClient(name = "armcloud-container-device", url = "placeholder")
public interface ContainerDeviceFeignClient {

    /**
     * 创建云机
     */
    @PostMapping(value = "/armcloud-container/open/device/virtualize")
    Result<List<DeviceVirtualizeResponse>> virtualize(URI host, @RequestBody VirtualizeDeviceRequest req);

    /**
     * 删除云机
     */
    @PostMapping(value = "/armcloud-container/open/device/destroy")
    Result<List<DeviceDestroyResponse>> destroy(URI host, @RequestBody DeviceIpsRequest req);

    /**
     * 一键新机
     */
    @PostMapping(value = "/armcloud-container/open/instance/replaceProp")
    Result<List<InstanceResetResponse>> replaceProp(URI host, @RequestBody InstanceReplacePropRequest req);

    /**
     * 重启云机
     */
    @PostMapping(value = "/armcloud-container/open/device/restart")
    Result<List<DeviceRestartResponse>> restart(URI host, @RequestBody DeviceIpsRequest req);

    /**
     * 查询板卡信息
     */
    @PostMapping(value = "/armcloud-container/open/device/info")
    Result<List<DeviceInfoResponse>> info(URI host, @RequestBody DeviceIpsRequest req);

    /**
     * 设置实例限速
     */
    @PostMapping(value = "/armcloud-container/open/instance/networkLimit")
    Result<List<InstanceNetworkLimitResponse>> networkLimit(URI host, @RequestBody InstanceNetworkLimitRequest req);

    /**
     * cbs自更新
     */
    @PostMapping(value = "/armcloud-container/open/device/selfUpdate")
    Result<List<SelfUpdateResponse>> selfUpdate(URI host, @Valid @RequestBody SelfUpdateRequest request);

    /**
     * 创建网存实例
     */
    @PostMapping(value = "/armcloud-container/open/device/netStorage/create")
    Result<List<DeviceVirtualizeResponse>> netStorageCreate(URI host,@RequestBody VirtualizeDeviceRequest req);

}
