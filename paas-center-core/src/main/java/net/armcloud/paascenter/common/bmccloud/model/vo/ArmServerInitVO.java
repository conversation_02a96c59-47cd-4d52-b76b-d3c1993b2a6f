package net.armcloud.paascenter.common.bmccloud.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ArmServerInitVO implements Serializable {
    private String sn;
    private List<cardVo> cards;

    @Data
    public static class cardVo {
        private String cardId;
        private String mac;
        private String nodeId;
        private String position;
    }
}
