package net.armcloud.paascenter.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云SLS日志配置
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.sls")
public class SlsLogConfig {

    /**
     * 是否启用SLS日志
     */
    private boolean enabled = false;

    /**
     * SLS服务端点
     */
    private String endpoint;

    /**
     * 访问密钥ID
     */
    private String accessKeyId;

    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * 项目名称
     */
    private String project;

    /**
     * 日志库名称
     */
    private String logstore;

    /**
     * 主题
     */
    // 单独读取spring.application.name
    @Value("${spring.application.name}")
    private String topic;

    /**
     * 来源
     */
    private String source = "application";

    /**
     * 批量发送大小 - 高并发优化
     */
    private int batchSizeThresholdInBytes = 1048576; // 1MB

    /**
     * 批量发送条数 - 高并发优化
     */
    private int batchCountThreshold = 8192;

    /**
     * 批量发送时间间隔(毫秒) - 高并发优化
     */
    private int lingerMs = 500;

    /**
     * 重试次数 - 高并发场景减少重试
     */
    private int retries = 1;

    /**
     * 基础重试间隔(毫秒)
     */
    private long baseRetryBackoffMs = 50;

    /**
     * 最大重试间隔(毫秒)
     */
    private long maxRetryBackoffMs = 5000;

    /**
     * SLS队列最大容量 - 防止内存溢出
     */
    private int queueCapacity = 100000;

    /**
     * 是否启用本地调试模式
     */
    private boolean debugMode = false;

}
