package net.armcloud.paascenter.common.lingdian.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class NodeInfoVO {
    /**
     * id
     */
    @JSONField(name = "id")
    private Integer id;
    /**
     * maxsocslot
     */
    @JSONField(name = "maxsocslot")
    private String maxsocslot;
    /**
     * SOC 在位情况 0-无 1-有
     */
    @JSONField(name = "socinp")
    private List<Integer> socinp;
    /**
     * 运行状态
     */
    @JSONField(name = "status")
    private String status;
    /**
     * temp
     */
    @J<PERSON>NField(name = "temp")
    private Integer temp;

    @JSONField(name = "version")
    private Integer version;

}
