package net.armcloud.paascenter.common.core.constant.pad;

import lombok.Getter;

@Getter
public class PadStatusConstant {
    /**
     * 运行中
     */
    public static final Integer RUNNING = 10;

    /**
     * 重启中
     */
    public static final Integer RESTARTING = 11;

    /**
     * 重置中
     */
    public static final Integer RESETTING = 12;

    /**
     * 升级中
     */
    public static final Integer UPGRADING = 13;

    /**
     * 异常
     */
    public static final Integer ABNORMAL = 14;

    /**
     * 未就绪
     */
    public static final Integer NOT_READY = 15;

    /**
     * 备份中
     */
    public static final Integer BACKUP = 16;

    /**
     * 恢复数据中
     */
    public static final Integer RESTORE = 17;

    /**
     * 关机
     */
    public static final Integer OFF = 18;

    /**
     * 关机中
     */
    public static final Integer OFF_RUN = 19;
    /**
     * 开机中
     */
    public static final Integer ON_RUN = 20;
    /**
     * 关机失败
     */
    public static final Integer OFF_ERROR = 21;

    /**
     * 开机失败
     */
    public static final Integer ON_ERROR = 22;

    /**
     * 删除中
     */
    public static final Integer DELETING = 23;

    /**
     * 删除失败
     */
    public static final Integer DELETE_FAIL = 24;

    /**
     * 删除
     */
    public static final Integer DELETED = 25;
}

