package net.armcloud.paascenter.rtc.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.Security;
import java.util.Base64;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
public class AESECBUtils {
    private static final String ALGORITHM = "AES";
    private static final String ALGORITHM_MODE_PADDING = "AES/ECB/PKCS7Padding";
    private static final String ISO_8859_1 = "ISO-8859-1";

    /**
     * AES加密
     */
    public static String encrypt(String data, String keyStr) {
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING, "BC");
            SecretKeySpec key = new SecretKeySpec(keyStr.getBytes(), ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            return base64Encode8859(new String(cipher.doFinal(data.getBytes()), ISO_8859_1));
        } catch (Exception e) {
            log.error("AESECBUtils encrypt error>>>data:{} keyStr:{}", data, keyStr, e);
            return null;
        }
    }

    /**
     * AES解密
     */
    public static String decrypt(String base64Data, String keyStr) {
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance(ALGORITHM_MODE_PADDING, "BC");
            SecretKeySpec key = new SecretKeySpec(keyStr.getBytes(), ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key);
            return new String(cipher.doFinal(base64Decode8859(base64Data).getBytes(ISO_8859_1)), UTF_8);
        } catch (Exception e) {
            log.error("AESECBUtils decrypt error>>>base64Data:{} keyStr:{}", base64Data, keyStr, e);
            return null;
        }
    }

    /**
     * Base64解码
     */
    public static String base64Decode8859(final String source) throws UnsupportedEncodingException {
        final Base64.Decoder decoder = Base64.getDecoder();
        return new String(decoder.decode(source), ISO_8859_1);
    }

    /**
     * Base64加密
     */
    public static String base64Encode8859(final String source) {
        String result = null;
        final Base64.Encoder encoder = Base64.getEncoder();
        byte[] textByte;
        try {
            textByte = source.getBytes(ISO_8859_1);
            result = encoder.encodeToString(textByte);
        } catch (final UnsupportedEncodingException e) {
            log.error("base64Encode8859 errror>>>>source:{}", source, e);
        }

        return result;
    }
}
