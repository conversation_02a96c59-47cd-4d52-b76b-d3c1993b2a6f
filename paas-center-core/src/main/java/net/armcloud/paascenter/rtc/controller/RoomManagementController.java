package net.armcloud.paascenter.rtc.controller;

import cn.hutool.core.util.ObjectUtil;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.rtc.ApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.DissolveRoomDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RefreshRoomTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.vo.rtc.DissolveRoomVO;
import net.armcloud.paascenter.common.model.vo.rtc.RefreshRoomTokenVO;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.rtc.annotation.PaaSToken;
import net.armcloud.paascenter.rtc.service.IPadRoomService;
import net.armcloud.paascenter.rtc.service.strategy.room.model.dto.ApplyShareTokenSDKDTO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.dto.BatchApplyTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.Constants.TOKEN;
import static net.armcloud.paascenter.common.utils.CustomerUtils.getAndVerifyUserIdByAttribute;
import static net.armcloud.paascenter.common.utils.http.RequestUtils.getCurrentRequest;

@Slf4j
@RestController
@RequestMapping("/rtc/open/room")
public class RoomManagementController {

    @Value("${rtc.appid}")
    private String appId;
    @Value("${rtc.appkey}")
    private String appKey;

    @Value("${rtc.appIdOverseas}")
    private String appIdOverseas;
    @Value("${rtc.appKeyOverseas}")
    private String appKeyOverseas;
    @Resource
    private IPadRoomService padRoomService;


    /**
     * @param roomTokenDTO rtc token
     * @return sdk获取rtc通信token
     */
    @PaaSToken
    @PostMapping("applyToken")
    public Result<RoomTokenVO> applyToken(@Valid @RequestBody RoomTokenDTO roomTokenDTO) {
        long current = System.currentTimeMillis();
        log.info("applyToken start thread:{} current:{}", Thread.currentThread().getId(), current);
//        log.warn("applyToken start roomTokenDTO={}", roomTokenDTO.getPadCode());
        HttpServletRequest httpServletRequest = getCurrentRequest();
        String customerId = httpServletRequest.getAttribute("customerId").toString();
        String validaToken = httpServletRequest.getHeader(TOKEN);
        //默认使用海外的appId
        String requestId = appIdOverseas;
        String requestKey = appKeyOverseas;
        //如果是pc终端且没有版本号，则使用国内appid和key，否则使用海外appid和key
        if(Objects.equals(roomTokenDTO.getSdkTerminal(),"pc") ||Objects.equals(roomTokenDTO.getSdkTerminal(),"pc-rtc")){
            if(StringUtils.isEmpty(roomTokenDTO.getStreamVersion())){
                requestId = appId;
                requestKey = appKey;
            }
        }
        log.info("applyToken start thread:{} customerId:{} validaToken:{} current:{}", Thread.currentThread().getId(), customerId,validaToken, current);
        RoomTokenVO data = padRoomService.getRoomRtcTokenService(validaToken, Long.parseLong(customerId), requestId, requestKey, roomTokenDTO);
//        log.warn("applyToken end padCode={}", roomTokenDTO.getPadCode());
//        log.info("applyToken end thread:{} customerId:{} validaToken:{} current:{}", Thread.currentThread().getId(), customerId,validaToken, current);
        return Result.ok(data);
    }

    @PostMapping("batchApplyToken")
    public Result<List<RoomTokenVO>> batchApplyToken(@Valid @RequestBody BatchApplyTokenDTO roomTokenDTO) {
        return Result.ok(padRoomService.batchApplyToken(CustomerUtils.getAndVerifyUserId(getCurrentRequest()), roomTokenDTO));
    }

    @PaaSToken
    @PostMapping("/sdk/batchApplyToken")
    public Result<List<RoomTokenVO>> sdkBatchApplyToken(@Valid @RequestBody BatchApplyTokenDTO roomTokenDTO) {
        return Result.ok(padRoomService.batchApplyToken(getAndVerifyUserIdByAttribute(getCurrentRequest()), roomTokenDTO));
    }

    @PostMapping("/share/applyToken")
    public Result<RoomTokenVO> applyVolcanoShareToken(@Valid @RequestBody ApplyShareTokenDTO dto) {
        return Result.ok(padRoomService.applyShareToken(CustomerUtils.getAndVerifyUserId(getCurrentRequest()), dto));
    }

    @PaaSToken
    @PostMapping("/sdk/share/applyToken")
    public Result<RoomTokenVO> applyVolcanoShareSDKToken(@Valid @RequestBody ApplyShareTokenSDKDTO dto) {
        log.info("applyVolcanoShareSDKToken start");
        HttpServletRequest httpServletRequest = getCurrentRequest();
        String customerId = httpServletRequest.getAttribute("customerId").toString();
        String validaToken = httpServletRequest.getHeader(TOKEN);
        return Result.ok(padRoomService.applyShareSDKToken(Long.parseLong(customerId), validaToken, dto));
    }

    /**
     * @param param RefreshRoomTokenDTO
     * @return 刷新room token
     */
    @PostMapping("refreshToken")
    public Result<RefreshRoomTokenVO> refreshToken(@Valid @RequestBody RefreshRoomTokenDTO param) {
        if (ObjectUtil.isNull(param.getRoomToken())) {
            param.setRoomToken(param.getToken());
        }
        RefreshRoomTokenVO data = padRoomService.refreshTokenService(appId, appKey, param.getRoomToken());
        return Result.ok(data);
    }

    /**
     * @param dissolveRoomDTO rtc token
     * @return 通知gameService 解散房间
     */
    //@PaaSToken
    @PostMapping("dissolveRoom")
    public Result<DissolveRoomVO> dissolveRoom(@Valid @RequestBody DissolveRoomDTO dissolveRoomDTO) {
        Long customerId = CustomerUtils.getAndVerifyUserId(getCurrentRequest());
        DissolveRoomVO dissolveRoomVO = padRoomService.dissolveRoomService(customerId, dissolveRoomDTO.getPadCodes());
        return Result.ok(dissolveRoomVO);
    }


}
