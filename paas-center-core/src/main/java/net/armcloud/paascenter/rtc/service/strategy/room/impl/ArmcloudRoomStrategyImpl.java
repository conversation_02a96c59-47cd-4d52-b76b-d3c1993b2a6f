package net.armcloud.paascenter.rtc.service.strategy.room.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.armcloudrtc.vo.ApplyEndpointVO;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.CheckSdkTokenPadDTO;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.ArmcloudPushaFlowCenterCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.JoinArmcloudShareRoomCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.rtc.P2PModelEnum;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.api.PadCustomerDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.entity.paas.PadRtcTokenLog;
import net.armcloud.paascenter.common.model.vo.rtc.ArmcloudRoomTokenV2VO;
import net.armcloud.paascenter.common.model.vo.rtc.ArmcloudRoomTokenVO;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadRoomMapper;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.rtc.config.ArmcloudRTCAppConfiguration;
import net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode;
import net.armcloud.paascenter.rtc.manager.armcloudrtc.ArmcloudRtcManager;
import net.armcloud.paascenter.rtc.manager.volcano.VolcanoRTCManager;
import net.armcloud.paascenter.rtc.mapper.*;
import net.armcloud.paascenter.rtc.service.IPadRtcTokenLogService;
import net.armcloud.paascenter.rtc.service.strategy.room.IRoomStrategy;
import net.armcloud.paascenter.rtc.service.strategy.room.RoomApplicationContext;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.ApplyShareTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.BatchApplyTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.GetRoomTokenStrategyBO;
import net.armcloud.paascenter.rtc.utils.AESECBUtils;
import net.armcloud.paascenter.rtc.utils.AESUtils;
import net.armcloud.paascenter.task.config.PullModeConfigHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.P2P_PEER_TO_PEER_PUSH_STREAM;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.JOIN_ARMCLOUD_SHARE_ROOM;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.PUSH_ARMCLOUD_FLOW;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.PAD_SET_NETWORK_PROXY;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;
import static net.armcloud.paascenter.rtc.constants.RoomConstants.StreamType.ARMCLOUD_STREAM_TYPE;
import static net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode.NO_STREAMING_PERMISSION_INSTANCE;

@Slf4j
@Component
public class ArmcloudRoomStrategyImpl implements IRoomStrategy, ApplicationRunner {
    private final RtcPadMapper rtcPadMapper;
    private final RtcPadRoomMapper rtcPadRoomMapper;
    private final ApplicationContext applicationContext;
    private final ArmcloudRtcManager armcloudRtcManager;
    private final PadRtcTokenLogMapper padRtcTokenLogMapper;
    private final RtcConfigurationMapper rtcConfigurationMapper;
    private final ArmcloudRTCAppConfiguration armcloudRTCAppConfiguration;
    private final RtcCustomerConfigMapper rtcCustomerConfigMapper;

    private final RedisService redisService;
    private final ICustomerService customerService;
    private final PadCommsDataService padCommsDataService;

    @Value("${use.p2p.stun.only}")
    private Boolean useP2pStunOnly;

    @Value("#{'${applyShareTokenGrayPadCodes:}'.split(',')}")
    private List<String> applyShareTokenGrayPadCodes;
    /**是否开启灰度*/
    @Value("${applyShareTokenPadCodesOpenPc:true}")
    private Boolean applyShareTokenPadCodesOpenPc;

    private final IPadService padService;

    private final CommonPadTaskComponent commonPadTaskComponent;

    private final PadMapper padMapper;

    @Override
    public RoomTokenVO generateToken(GetRoomTokenStrategyBO param) {
        log.info("ArmcloudRoomStrategyImpl generateToken >>> param:{}", JSON.toJSONString(param));
        String validaToken = param.getValidaToken();
        RoomTokenDTO dto = param.getRoomTokenDTO();
        Long customerId = param.getCustomerId();
        // todo 因目前没有刷新token功能,临时调整rtc token过期时间
        dto.setExpire(86400);

        //MoreLogin 需要判断token是否是当前的padCode获取的
        if(validaToken.contains(Constants.USER_MORELOGIN)){
            CheckSdkTokenPadDTO padDTO = new CheckSdkTokenPadDTO();
            padDTO.setPadCode(dto.getPadCode());
            padDTO.setSdkToken(validaToken);
            Boolean checkResult = customerService.checkSdkTokenBindCustomerAndPadV2(padDTO);
            if (Boolean.FALSE.equals(checkResult)) {
                throw new BasicException(RtcExceptionCode.PADCODE_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_PADCODE);
            }
        }
        //在鉴权通过后token需要绑定uuid
        VerifyAndGetSDKCustomerDTO verifyAndGetSDKCustomerDTO = new VerifyAndGetSDKCustomerDTO();
        verifyAndGetSDKCustomerDTO.setSdkToken(validaToken);
        verifyAndGetSDKCustomerDTO.setUuid(dto.getUuid());
        Boolean checkResult = customerService.sdkTokenBindUuidService(verifyAndGetSDKCustomerDTO);
        if (Boolean.FALSE.equals(checkResult)) {
            throw new BasicException(RtcExceptionCode.UUID_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_UUID);
        }

        PadCustomerDTO padCustomerDTO = new PadCustomerDTO();
        padCustomerDTO.setCustomerId(customerId);
        padCustomerDTO.setPadCode(dto.getPadCode());
        if(redisService.isAdmin(customerId)){
            padCustomerDTO.setCustomerId(0L);
        }
        Pad resultPad = padService.getPadInfoByCustomerId(padCustomerDTO);
        if (ObjectUtil.isNull(resultPad)) {
            throw new BasicException(RtcExceptionCode.NO_STREAMING_PERMISSION_INSTANCE);
        }

        ArmcloudRoomTokenVO roomTokenVO = new ArmcloudRoomTokenVO();
        PadRoom padRoom = rtcPadRoomMapper.selectOne(new QueryWrapper<PadRoom>().eq("pad_code", dto.getPadCode()).eq("type", NumberConsts.ONE));
        if (ObjectUtil.isNull(padRoom)) {
            throw new BasicException(RtcExceptionCode.ROOM_CORRESPONDING_TO_PAD_DOES_NOT_EXIST);
        }

        String roomCode = padRoom.getRoomCode() + dto.getUserId();
        //生成客户端sdk与rtc通信token
        String sdkToken = armcloudRtcManager.generateAccessToken(roomCode, dto.getUserId(), dto.getExpire(), Boolean.FALSE);

        //生成gameService与rtc通信token
        String gameServiceToken = armcloudRtcManager.generateAccessToken(roomCode, dto.getPadCode(), dto.getExpire(), Boolean.TRUE);

        //通知gameService加入房间
        ArmcloudPushaFlowCenterCMDDTO data = new ArmcloudPushaFlowCenterCMDDTO();
        data.setRtcAppId(armcloudRTCAppConfiguration.getAppId());
        data.setToken(gameServiceToken);
        data.setUserId(dto.getPadCode());
        data.setRoomCode(roomCode);

        String p2pPeerToPeerPushStream = rtcCustomerConfigMapper.getP2pPushStream(customerId);
        p2pPeerToPeerPushStream = StringUtils.isNotBlank(p2pPeerToPeerPushStream) ? p2pPeerToPeerPushStream : rtcConfigurationMapper.selectValueByKey(P2P_PEER_TO_PEER_PUSH_STREAM);
        if (StringUtils.isNotBlank(p2pPeerToPeerPushStream)) {
            if (P2PModelEnum.DEFAULT.getValue().equals(p2pPeerToPeerPushStream)) {
                useP2pStunOnly = null;
            }

            if (P2PModelEnum.DIRECT.getValue().equals(p2pPeerToPeerPushStream)) {
                useP2pStunOnly = true;
            }

            if (P2PModelEnum.FORWARD.getValue().equals(p2pPeerToPeerPushStream)) {
                useP2pStunOnly = false;
            }
        }

        data.setUseP2P(useP2pStunOnly);

        // 申请RTC连接信息
        ApplyEndpointVO applyEndpointVO = armcloudRtcManager.applyEndpoint(dto.getPadCode());
        ApplyEndpointVO.SignalEndpoint signalEndpoint = applyEndpointVO.getSignalEndpoint();

        List<ArmcloudPushaFlowCenterCMDDTO.Stun> stuns = new ArrayList<>();
        List<ArmcloudPushaFlowCenterCMDDTO.Turn> turns = new ArrayList<>();
        applyEndpointVO.getPeerConnectionEndpoints().forEach(endpoint -> {
            if (endpoint.getUri().startsWith("stun")) {
                ArmcloudPushaFlowCenterCMDDTO.Stun stun = new ArmcloudPushaFlowCenterCMDDTO.Stun();
                stun.setUri(endpoint.getUri());
                stuns.add(stun);
                return;
            }

            if (endpoint.getUri().startsWith("turn")) {
                ArmcloudPushaFlowCenterCMDDTO.Turn turn = new ArmcloudPushaFlowCenterCMDDTO.Turn();
                turn.setUri(endpoint.getUri());
                turn.setUsername(endpoint.getUsername());
                turn.setPwd(endpoint.getPwd());
                turns.add(turn);
            }
        });

        // 设置推流配置
        StringBuilder aesKey = new StringBuilder(padRoom.getPadCode());
        String signalServerAesEncryptData = AESUtils.encrypt(signalEndpoint.getIp(), aesKey.toString());
        data.setSignalServer(signalServerAesEncryptData);

        String stunsAesEncryptData = AESUtils.encrypt(JSON.toJSONString(stuns), aesKey.toString());
        data.setStuns(stunsAesEncryptData);

        String turnsAesEncryptData = AESUtils.encrypt(JSON.toJSONString(turns), aesKey.toString());
        data.setTurns(turnsAesEncryptData);

        ArmcloudPushaFlowCenterCMDDTO.VideoStream target = new ArmcloudPushaFlowCenterCMDDTO.VideoStream();
        BeanUtil.copyProperties(dto.getVideoStream(), target);
        data.setVideoStream(target);

        PadCMDForwardDTO request = new PadCMDForwardDTO()
                .setSourceCode(PAAS)
                .setCommand(PUSH_ARMCLOUD_FLOW)
                .setPadInfos(Arrays.asList(new PadCMDForwardDTO.PadInfoDTO(dto.getPadCode(), data)));

        Pad padTaskMode = padMapper.selectPadByPadCode(dto.getPadCode());
        if(padTaskMode != null && padTaskMode.getTaskMode() != null && padTaskMode.getTaskMode() == 1){
            commonPadTaskComponent.addPadCMDTask(customerId,Collections.singletonList(dto.getPadCode()), TaskTypeConstants.GS_PUSH_ARMCLOUD_FLOW, request);
        }else{
            try {
                List<CommsTransmissionResultVO> result = padCommsDataService.forward(request);
                result.stream()
                        .map(CommsTransmissionResultVO::getSendSuccess)
                        .filter(Boolean.TRUE::equals).findFirst()
                        .orElseThrow(() -> {
                            log.info("通知gameService加入armcloud房间失败！request={},result={}", JSON.toJSONString(request), JSON.toJSONString(result));
                            return new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                        });
            } catch (BasicException e) {
            log.warn("通知gameService加入armcloud房间失败！request={}", JSON.toJSONString(request), e.getMessage());
            throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
        } catch (Exception e) {
                log.error("通知gameService加入armcloud房间失败！request={}", JSON.toJSONString(request), e);
                throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
            }
        }

        //rtc token日志
        IPadRtcTokenLogService padRtcTokenLogService = applicationContext.getBean(IPadRtcTokenLogService.class);
        padRtcTokenLogService.saveTokenLog(dto, padRoom, sdkToken, ZERO);
        padRtcTokenLogService.saveTokenLog(dto, padRoom, gameServiceToken, NumberConsts.ONE);

        roomTokenVO.setAppId(armcloudRTCAppConfiguration.getAppId());
        roomTokenVO.setRoomCode(roomCode);
        roomTokenVO.setRoomToken(sdkToken);
        roomTokenVO.setSignalServer(signalServerAesEncryptData);
        roomTokenVO.setStuns(stunsAesEncryptData);
        roomTokenVO.setTurns(turnsAesEncryptData);
        roomTokenVO.setStreamType(ARMCLOUD_STREAM_TYPE);

        // h5加密单独规则
        if (dto.isH5SdkTerminal()) {
            int keyLength = 16;
            while (keyLength > aesKey.length()) {
                aesKey.append("0");
            }

            if (aesKey.length() > keyLength) {
                aesKey = (StringBuilder) aesKey.subSequence(0, keyLength);
            }

            roomTokenVO.setSignalServer(AESECBUtils.encrypt(signalEndpoint.getDomain(), aesKey.toString()));
            roomTokenVO.setStuns(AESECBUtils.encrypt(JSON.toJSONString(stuns), aesKey.toString()));
            roomTokenVO.setTurns(AESECBUtils.encrypt(JSON.toJSONString(turns), aesKey.toString()));
        }

        return roomTokenVO;
    }

    @Override
    public List<? extends RoomTokenVO> batchGenerateToken(BatchApplyTokenBO param) {
        // todo 因目前没有刷新token功能,临时调整rtc token过期时间
        param.setExpire(86400);

        verifyParam(param);
        List<BatchApplyTokenBO.Pad> requestPads = param.getPads();
        List<ArmcloudRoomTokenV2VO> result = new ArrayList<>(requestPads.size());

        String customerUserId = param.getCustomerUserId();
        int expire = param.getExpire();
        List<PadCMDForwardDTO.PadInfoDTO> padInfoDatas = new ArrayList<>();

        List<String> padCodes = requestPads.stream().map(BatchApplyTokenBO.Pad::getPadCode).collect(Collectors.toList());
        List<PadRoom> padRooms = rtcPadRoomMapper.listByPadCode(padCodes);
        Map<String, String> padCodeRefRoomCodeMap = padRooms.stream()
                .collect(Collectors.toMap(PadRoom::getPadCode, PadRoom::getRoomCode, (o1, o2) -> o1));

        requestPads.forEach(pad -> {
            String padCode = pad.getPadCode();
            String roomCode = padCodeRefRoomCodeMap.get(padCode) + customerUserId;

            // basic
            ArmcloudPushaFlowCenterCMDDTO data = new ArmcloudPushaFlowCenterCMDDTO();
            data.setUserId(padCode);
            data.setRoomCode(roomCode);
            data.setRtcAppId(armcloudRTCAppConfiguration.getAppId());

            // rtc token
            String gameServiceToken = armcloudRtcManager.generateAccessToken(roomCode, padCode, expire, Boolean.TRUE);
            data.setToken(gameServiceToken);
            saveLog(padCode, gameServiceToken, param, 1);

            // push stream
            ArmcloudPushaFlowCenterCMDDTO.VideoStream target = new ArmcloudPushaFlowCenterCMDDTO.VideoStream();
            BeanUtil.copyProperties(pad.getVideoStream(), target);
            data.setVideoStream(target);

            ApplyEndpointVO applyEndpointVO = armcloudRtcManager.applyEndpoint(padCode);
            // rtc  stun/turn
            List<ArmcloudPushaFlowCenterCMDDTO.Stun> stuns = new ArrayList<>();
            List<ArmcloudPushaFlowCenterCMDDTO.Turn> turns = new ArrayList<>();
            applyEndpointVO.getPeerConnectionEndpoints().forEach(endpoint -> {
                if (endpoint.getUri().startsWith("stun")) {
                    ArmcloudPushaFlowCenterCMDDTO.Stun stun = new ArmcloudPushaFlowCenterCMDDTO.Stun();
                    stun.setUri(endpoint.getUri());
                    stuns.add(stun);
                    return;
                }

                if (endpoint.getUri().startsWith("turn")) {
                    ArmcloudPushaFlowCenterCMDDTO.Turn turn = new ArmcloudPushaFlowCenterCMDDTO.Turn();
                    turn.setUri(endpoint.getUri());
                    turn.setUsername(endpoint.getUsername());
                    turn.setPwd(endpoint.getPwd());
                    turns.add(turn);
                }
            });

            // data encrypt
            StringBuilder aesKey = new StringBuilder(padCode);
            String stunsAesEncryptData = AESUtils.encrypt(JSON.toJSONString(stuns), aesKey.toString());
            data.setStuns(stunsAesEncryptData);

            String turnsAesEncryptData = AESUtils.encrypt(JSON.toJSONString(turns), aesKey.toString());
            data.setTurns(turnsAesEncryptData);

            // rtc signal
            ApplyEndpointVO.SignalEndpoint signalEndpoint = applyEndpointVO.getSignalEndpoint();
            String signalServerAesEncryptData = AESUtils.encrypt(signalEndpoint.getIp(), aesKey.toString());
            data.setSignalServer(signalServerAesEncryptData);

            // set pad command
            PadCMDForwardDTO.PadInfoDTO padInfoDTO = new PadCMDForwardDTO.PadInfoDTO();
            padInfoDTO.setPadCode(padCode);
            padInfoDTO.setData(data);
            padInfoDatas.add(padInfoDTO);

            ArmcloudRoomTokenV2VO armcloudRoomTokenV2VO = new ArmcloudRoomTokenV2VO();
            armcloudRoomTokenV2VO.setPadCode(padCode);
            armcloudRoomTokenV2VO.setSignalServer(signalServerAesEncryptData);
            armcloudRoomTokenV2VO.setStuns(stunsAesEncryptData);
            armcloudRoomTokenV2VO.setTurns(turnsAesEncryptData);
            String customerToken = armcloudRtcManager.generateAccessToken(roomCode, customerUserId, expire, Boolean.FALSE);
            saveLog(padCode, customerToken, param, 0);
            armcloudRoomTokenV2VO.setRoomToken(customerToken);
            armcloudRoomTokenV2VO.setRoomCode(roomCode);
            armcloudRoomTokenV2VO.setAppId(armcloudRTCAppConfiguration.getAppId());
            armcloudRoomTokenV2VO.setStreamType(ARMCLOUD_STREAM_TYPE);
            result.add(armcloudRoomTokenV2VO);
        });

        // notify pad join room
        PadCMDForwardDTO request = new PadCMDForwardDTO()
                .setSourceCode(PAAS).setCommand(PUSH_ARMCLOUD_FLOW).setPadInfos(padInfoDatas);

        List<Pad> pads = padMapper.selectPadByPadCodes(padCodes);
        if(CollUtil.isNotEmpty(pads)){
            //拉任务 实例
            List<String> pullPadCodes = new ArrayList<>();
            //推任务 实例
            List<String> pushPadCodes = new ArrayList<>();
            for(Pad pad : pads){
                //通过PadInfoVO.isOnline判断GameServer在线状态'长连接状态 0-离线 1-在线'
                if(!pad.online()){
                    result.stream()
                            .filter(r -> r.getPadCode().equals(pad.getPadCode()))
                            .findFirst()
                            .ifPresent(r -> {
                                r.setMsg("connect pad fail");
                                r.setStuns(null);
                                r.setTurns(null);
                                r.setSignalServer(null);
                                r.setRoomToken(null);
                            });
                    continue;
                }
                if(pad.getTaskMode() != null && pad.getTaskMode() == 1){
                    pullPadCodes.add(pad.getPadCode());
                }else{
                    pushPadCodes.add(pad.getPadCode());
                }
            }

            if(CollUtil.isNotEmpty(pullPadCodes)){
                commonPadTaskComponent.addPadCMDTask(Long.parseLong(customerUserId),pullPadCodes, TaskTypeConstants.GS_PUSH_ARMCLOUD_FLOW, request);
            }
            if(CollUtil.isNotEmpty(pushPadCodes)){
                //深拷贝
                PadCMDForwardDTO padCMDForwardDTO = BeanUtil.copyProperties(request,PadCMDForwardDTO.class);
                try {
                    List<PadCMDForwardDTO.PadInfoDTO> pushPadInfoDTOs = new ArrayList<>();
                    for(PadCMDForwardDTO.PadInfoDTO padInfoDTO : padCMDForwardDTO.getPadInfos()){
                        if(pushPadCodes.contains(padInfoDTO.getPadCode())){
                            pushPadInfoDTOs.add(padInfoDTO);
                        }
                    }
                    padCMDForwardDTO.setPadInfos(pushPadInfoDTOs);
                    List<CommsTransmissionResultVO> notifyPadResult = padCommsDataService.forward(padCMDForwardDTO);
                    Map<String, CommsTransmissionResultVO> notifyPadResultMap = notifyPadResult.stream()
                            .collect(Collectors.toMap(CommsTransmissionResultVO::getPadCode, obj -> obj, (o1, o2) -> o1));

                    // builder result
                    result.forEach(r -> {
                        CommsTransmissionResultVO commsTransmissionResultVO = notifyPadResultMap.get(r.getPadCode());
                        if (Objects.isNull(commsTransmissionResultVO) || Boolean.FALSE.equals(commsTransmissionResultVO.getSendSuccess())) {
                            r.setMsg("connect pad fail");
                            r.setStuns(null);
                            r.setTurns(null);
                            r.setSignalServer(null);
                            r.setRoomToken(null);
                        }
                    });
                } catch (Exception e) {
                    log.error("batchGenerateToken error>>>param={}", JSON.toJSONString(param), e);
                    throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                }
            }

        }
        return result;
    }

    @Override
    public RoomTokenVO applyShareToken(ApplyShareTokenBO bo) {
        long customerId = bo.getCustomerId();
        String roomCode = bo.getTerminal() + bo.getUserId() + customerId;
        int expire = bo.getExpire();
        String userId = bo.getUserId();
        ApplyEndpointVO applyEndpointVO = armcloudRtcManager.applyEndpoint(String.valueOf(customerId));
        ApplyEndpointVO.SignalEndpoint signalEndpoint = applyEndpointVO.getSignalEndpoint();

        List<PadRtcTokenLog> padRtcTokenLogs = new ArrayList<>();
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        List<String> padCodes = new ArrayList<>();
        bo.getPads().forEach(pad -> {
            String padCode = pad.getPadCode();
            padCodes.add(padCode);
            PadCMDForwardDTO.PadInfoDTO padInfoDTO = new PadCMDForwardDTO.PadInfoDTO();
            padInfoDTO.setPadCode(padCode);

            JoinArmcloudShareRoomCMDDTO cmdDto = new JoinArmcloudShareRoomCMDDTO();
            cmdDto.setRoomCode(roomCode);
            cmdDto.setUserId(bo.getUserId());

            // gameservice没有主动拼接前缀，返回数据特殊处理下
            String signalServer = signalEndpoint.getIp();
            if (!signalServer.startsWith("ws")) {
                signalServer = "ws://" + signalServer;
            }
            cmdDto.setSignalServer(signalServer);

            String gameServiceToken = armcloudRtcManager.generateAccessToken(roomCode, padCode, expire, Boolean.FALSE);
            cmdDto.setRoomToken(gameServiceToken);
            padInfoDTO.setData(cmdDto);
            padInfos.add(padInfoDTO);

            PadRtcTokenLog padRtcTokenLog = new PadRtcTokenLog();
            padRtcTokenLog.setRoomId(0L);
            padRtcTokenLog.setPadCode(padCode);
            padRtcTokenLog.setUserId(userId);
            padRtcTokenLog.setExpire(expire);
            padRtcTokenLog.setRoomToken(cmdDto.getRoomToken());
            padRtcTokenLog.setPublishStream(0);
            padRtcTokenLogs.add(padRtcTokenLog);
        });

        PadCMDForwardDTO request = new PadCMDForwardDTO()
                .setSourceCode(PAAS)
                .setCommand(JOIN_ARMCLOUD_SHARE_ROOM)
                .setPadInfos(padInfos);

        List<Pad> pads = padMapper.selectPadByPadCodes(padCodes);
        if(CollUtil.isNotEmpty(pads)) {
            //拉任务 实例
            List<String> pullPadCodes = new ArrayList<>();
            //推任务 实例
            List<String> pushPadCodes = new ArrayList<>();
            //判断是否有在线的pad，如果都离线直接抛出异常new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION)，只要有一个pad是在线的则继续执行下面的逻辑
            boolean anyOnline = pads.stream()
                    .anyMatch(Pad::online);
            if (!anyOnline) {
                log.warn("ArmcloudRoomStrategyImpl.applyShareToken:所有pad都离线，无法执行操作！request={}", JSON.toJSONString(request));
                throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
            }
            for (Pad pad : pads) {
                if (pad.getTaskMode() != null && pad.getTaskMode() == 1) {
                    pullPadCodes.add(pad.getPadCode());
                } else {
                    pushPadCodes.add(pad.getPadCode());
                }
            }
            if(CollUtil.isNotEmpty(pullPadCodes)){
                commonPadTaskComponent.addPadCMDTask(customerId,pullPadCodes, TaskTypeConstants.GS_JOIN_ARMCLOUD_SHARE_ROOM, request);
            }
            if(CollUtil.isNotEmpty(pushPadCodes)) {
                //深拷贝
                PadCMDForwardDTO padCMDForwardDTO = BeanUtil.copyProperties(request, PadCMDForwardDTO.class);
                try {
                    List<PadCMDForwardDTO.PadInfoDTO> pushPadInfoDTOs = new ArrayList<>();
                    for(PadCMDForwardDTO.PadInfoDTO padInfoDTO : padCMDForwardDTO.getPadInfos()){
                        if(pushPadCodes.contains(padInfoDTO.getPadCode())){
                            pushPadInfoDTOs.add(padInfoDTO);
                        }
                    }
                    padCMDForwardDTO.setPadInfos(pushPadInfoDTOs);
                    List<CommsTransmissionResultVO> result = padCommsDataService.forward(padCMDForwardDTO);
                    result.stream()
                            .map(CommsTransmissionResultVO::getSendSuccess)
                            .filter(Boolean.TRUE::equals).findFirst()
                            .orElseThrow(() -> new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION));
                } catch (BasicException e) {
                    log.warn("通知gameService加入armcloud房间失败！request={}", JSON.toJSONString(padCMDForwardDTO), e.getMessage());
                    throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                }  catch (Exception e) {
                    log.error("通知gameService加入房间失败！request={}", JSON.toJSONString(padCMDForwardDTO), e);
                    throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                }
            }
        }

        String roomToken = armcloudRtcManager.generateAccessToken(roomCode, userId, expire, Boolean.FALSE);
        PadRtcTokenLog padRtcTokenLog = new PadRtcTokenLog();
        padRtcTokenLog.setRoomId(0L);
        padRtcTokenLog.setPadCode("");
        padRtcTokenLog.setUserId(userId);
        padRtcTokenLog.setExpire(expire);
        padRtcTokenLog.setRoomToken(roomToken);
        padRtcTokenLog.setPublishStream(0);
        padRtcTokenLogs.add(padRtcTokenLog);
        applicationContext.getBean(VolcanoRTCManager.class).asyncSaveLog(padRtcTokenLogs);

        ArmcloudRoomTokenVO roomTokenVO = new ArmcloudRoomTokenVO();
        roomTokenVO.setRoomToken(roomToken);
        roomTokenVO.setRoomCode(roomCode);
        roomTokenVO.setSignalServer(signalEndpoint.getDomain());
        String firstPadCode = null;
        if(CollUtil.isNotEmpty(bo.getPads())){
            firstPadCode = bo.getPads().get(0).getPadCode();
        }

        //terminal包含pc 这里不确定有啥风险  所有增加一个开关applyShareTokenGrayPadCodesOpen 如果一旦有问题 applyShareTokenGrayPadCodesOpen设置为false可以兜底
        if((CollUtil.isNotEmpty(applyShareTokenGrayPadCodes) && StrUtil.isNotEmpty(firstPadCode) && applyShareTokenGrayPadCodes.contains(firstPadCode)) || (applyShareTokenPadCodesOpenPc !=null && applyShareTokenPadCodesOpenPc)){
            if(StrUtil.isNotEmpty(bo.getTerminal()) && bo.getTerminal().toLowerCase().contains("pc")){
                String ip = signalEndpoint.getIp();
                if (!ip.startsWith("ws")) {
                    ip = "ws://" + ip;
                }
                roomTokenVO.setSignalServer(ip);
            }
        }

        roomTokenVO.setStreamType(ARMCLOUD_STREAM_TYPE);
        return roomTokenVO;
    }

    public void saveLog(String padCode, String roomToken, BatchApplyTokenBO param, int publishStream) {
        PadRtcTokenLog tokenLog = new PadRtcTokenLog();
        tokenLog.setRoomId(0L);
        tokenLog.setPadCode(padCode);
        tokenLog.setUserId(param.getCustomerUserId());
        tokenLog.setExpire(param.getExpire());
        tokenLog.setRoomToken(roomToken);
        tokenLog.setPublishStream(publishStream);
        padRtcTokenLogMapper.insert(tokenLog);
    }

    private void verifyParam(BatchApplyTokenBO param) {
        List<BatchApplyTokenBO.Pad> requestPads = param.getPads();
        List<String> padCodes = requestPads.stream().map(BatchApplyTokenBO.Pad::getPadCode).collect(Collectors.toList());
        List<Pad> pads = rtcPadMapper.listByPadCodes(padCodes);
        if (padCodes.size() != pads.size()) {
            throw new BasicException(NO_STREAMING_PERMISSION_INSTANCE);
        }

        long customerId = param.getCustomerId();
        boolean customerNoPermission = pads.stream().anyMatch(pad -> pad.getCustomerId() != customerId);
        if (customerNoPermission) {
            throw new BasicException(NO_STREAMING_PERMISSION_INSTANCE);
        }

    }

    @Override
    public void run(ApplicationArguments args) {
        RoomApplicationContext.putBeanName(ARMCLOUD_STREAM_TYPE, "armcloudRoomStrategyImpl");
    }
    
    public ArmcloudRoomStrategyImpl(RtcPadRoomMapper rtcPadRoomMapper,
                                    ApplicationContext applicationContext,
                                    ArmcloudRtcManager armcloudRtcManager,
                                    ArmcloudRTCAppConfiguration armcloudRTCAppConfiguration,
                                    RtcPadMapper rtcPadMapper,
                                    PadRtcTokenLogMapper padRtcTokenLogMapper,
                                    RtcConfigurationMapper rtcConfigurationMapper,
                                    RtcCustomerConfigMapper rtcCustomerConfigMapper,
                                    RedisService redisService,
                                    IPadService padService,
                                    ICustomerService customerService,
                                    PadCommsDataService padCommsDataService,
                                    CommonPadTaskComponent commonPadTaskComponent,
                                    PadMapper padMapper) {
        this.rtcPadRoomMapper = rtcPadRoomMapper;
        this.applicationContext = applicationContext;
        this.armcloudRtcManager = armcloudRtcManager;
        this.armcloudRTCAppConfiguration = armcloudRTCAppConfiguration;
        this.rtcPadMapper = rtcPadMapper;
        this.padRtcTokenLogMapper = padRtcTokenLogMapper;
        this.rtcConfigurationMapper = rtcConfigurationMapper;
        this.rtcCustomerConfigMapper = rtcCustomerConfigMapper;
        this.redisService = redisService;
        this.padService = padService;
        this.customerService = customerService;
        this.padCommsDataService = padCommsDataService;
        this.commonPadTaskComponent = commonPadTaskComponent;
        this.padMapper = padMapper;
    }
}
