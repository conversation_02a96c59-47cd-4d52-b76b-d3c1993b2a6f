package net.armcloud.paascenter.rtc.service.strategy.room.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.dto.CheckSdkTokenPadDTO;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PushFlowCenterCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.api.PadCustomerDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.volcipaas.service.RtcSdkService;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.rtc.constants.RoomConstants;
import net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode;
import net.armcloud.paascenter.rtc.manager.volcano.VolcanoRTCManager;
import net.armcloud.paascenter.rtc.mapper.RtcPadRoomMapper;
import net.armcloud.paascenter.rtc.service.IPadRtcTokenLogService;
import net.armcloud.paascenter.rtc.service.strategy.room.IRoomStrategy;
import net.armcloud.paascenter.rtc.service.strategy.room.RoomApplicationContext;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.ApplyShareTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.BatchApplyTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.GetRoomTokenStrategyBO;
import net.armcloud.paascenter.task.config.PullModeConfigHolder;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.PUSH_VOLCANO_FLOW;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.INTERFACE_NOT_SUPPORT;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;

@Slf4j
@Component
public class VolcengineRoomStrategyImpl implements IRoomStrategy, ApplicationRunner {
    private final RtcPadRoomMapper rtcPadRoomMapper;
    private final VolcanoRTCManager volcanoRTCManager;
    private final ApplicationContext applicationContext;

    private final RedisService redisService;
    private final IPadService padService;
    private final ICustomerService customerService;
    private final PadCommsDataService padCommsDataService;

    private CommonPadTaskComponent commonPadTaskComponent;
    private final PadMapper padMapper;


    @Override
    public RoomTokenVO generateToken(GetRoomTokenStrategyBO param) {
        log.info("VolcengineRoomStrategyImpl generateToken >>> param:{}", JSON.toJSONString(param));
        String validaToken = param.getValidaToken();
        Long customerId = param.getCustomerId();
        RoomTokenDTO dto = param.getRoomTokenDTO();
        String appId = param.getAppId();
        String appKey = param.getAppKey();

        //在鉴权通过后token需要绑定uuid
        VerifyAndGetSDKCustomerDTO verifyAndGetSDKCustomerDTO = new VerifyAndGetSDKCustomerDTO();
        verifyAndGetSDKCustomerDTO.setSdkToken(validaToken);
        verifyAndGetSDKCustomerDTO.setUuid(dto.getUuid());
        //MoreLogin 需要判断token是否是当前的padCode获取的
        if(validaToken.contains(Constants.USER_MORELOGIN)){
            CheckSdkTokenPadDTO padDTO = new CheckSdkTokenPadDTO();
            padDTO.setPadCode(dto.getPadCode());
            padDTO.setSdkToken(validaToken);
            Boolean checkResult = customerService.checkSdkTokenBindCustomerAndPadV2(padDTO);
            if (Boolean.FALSE.equals(checkResult)) {
                throw new BasicException(RtcExceptionCode.PADCODE_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_PADCODE);
            }
        }
        Boolean checkResult = customerService.sdkTokenBindUuidService(verifyAndGetSDKCustomerDTO);
        if (Boolean.FALSE.equals(checkResult)) {
            throw new BasicException(RtcExceptionCode.UUID_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_UUID);
        }

        PadCustomerDTO padCustomerDTO = new PadCustomerDTO();
        padCustomerDTO.setCustomerId(customerId);
        padCustomerDTO.setPadCode(dto.getPadCode());
        if(redisService.isAdmin(customerId)){
            padCustomerDTO.setCustomerId(0L);
        }
        Pad resultPad = padService.getPadInfoByCustomerId(padCustomerDTO);
        if (ObjectUtil.isNull(resultPad)) {
            throw new BasicException(RtcExceptionCode.NO_STREAMING_PERMISSION_INSTANCE);
        }
        padCustomerDTO.setCustomerId(customerId);
        RoomTokenVO roomTokenVO = new RoomTokenVO();
        PadRoom padRoom = rtcPadRoomMapper.selectOne(new QueryWrapper<PadRoom>()
                .eq("pad_code", dto.getPadCode())
                .eq("type", NumberConsts.ONE));
        if (ObjectUtil.isNull(padRoom)) {
            throw new BasicException(RtcExceptionCode.ROOM_CORRESPONDING_TO_PAD_DOES_NOT_EXIST);
        }
        //生成客户端sdk与rtc通信token
        RtcSdkService rtcSdkService = applicationContext.getBean(RtcSdkService.class);
        String sdkToken = rtcSdkService.getRtcTokenService(appId, appKey, padRoom.getRoomCode(), dto.getUserId(),
                dto.getExpire(), Boolean.FALSE);

        //生成gameService与rtc通信token
        String gameServiceToken = rtcSdkService.getRtcTokenService(appId, appKey, padRoom.getRoomCode(),
                dto.getPadCode(), dto.getExpire(), Boolean.TRUE);

        //通知gameService加入房间
        PushFlowCenterCMDDTO data = new PushFlowCenterCMDDTO();
        data.setRtcAppId(appId);
        data.setToken(gameServiceToken);
        data.setUserId(dto.getPadCode());
        data.setRoomCode(padRoom.getRoomCode());
        PushFlowCenterCMDDTO.VideoStream target = new PushFlowCenterCMDDTO.VideoStream();
        BeanUtil.copyProperties(dto.getVideoStream(), target);
        data.setVideoStream(target);

        PadCMDForwardDTO request = new PadCMDForwardDTO()
                .setSourceCode(PAAS)
                .setCommand(PUSH_VOLCANO_FLOW)
                .setPadInfos(Arrays.asList(new PadCMDForwardDTO.PadInfoDTO(dto.getPadCode(), data)));

        Pad padTaskMode = padMapper.selectPadByPadCode(dto.getPadCode());
        if(padTaskMode != null && padTaskMode.getTaskMode() != null && padTaskMode.getTaskMode() == 1){
            commonPadTaskComponent.addPadCMDTask(customerId, Collections.singletonList(dto.getPadCode()), TaskTypeConstants.GS_PUSH_VOLCANO_FLOW, request);
        }else{
            try {
                List<CommsTransmissionResultVO> result = padCommsDataService.forward(request);
                result.stream()
                        .map(CommsTransmissionResultVO::getSendSuccess)
                        .filter(Boolean.TRUE::equals).findFirst()
                        .orElseThrow(() -> {
                            log.info("通知gameService加入房间失败！request={},result={}", JSON.toJSONString(request), JSON.toJSONString(result));
                            return new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
                        });
            } catch (Exception e) {
                log.warn("通知gameService加入房间失败！request={}", JSON.toJSONString(request), e);
                throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
            }
        }


        IPadRtcTokenLogService padRtcTokenLogService = applicationContext.getBean(IPadRtcTokenLogService.class);
        //rtc token日志
        padRtcTokenLogService.saveTokenLog(dto, padRoom, sdkToken, ZERO);
        padRtcTokenLogService.saveTokenLog(dto, padRoom, gameServiceToken, NumberConsts.ONE);

        roomTokenVO.setAppId(appId);
        roomTokenVO.setRoomCode(padRoom.getRoomCode());
        roomTokenVO.setRoomToken(sdkToken);
        roomTokenVO.setStreamType(RoomConstants.StreamType.VOLCENGINE_STREAM_TYPE);
        return roomTokenVO;
    }

    @Override
    public List<RoomTokenVO> batchGenerateToken(BatchApplyTokenBO param) {
        throw new BasicException(INTERFACE_NOT_SUPPORT);
    }

    @Override
    public RoomTokenVO applyShareToken(ApplyShareTokenBO applyShareTokenBO) {
        return volcanoRTCManager.applyVolcanoShareToken(applyShareTokenBO);
    }

    @Override
    public void run(ApplicationArguments args) {
        RoomApplicationContext.putBeanName(RoomConstants.StreamType.VOLCENGINE_STREAM_TYPE, "volcengineRoomStrategyImpl");
    }

    public VolcengineRoomStrategyImpl(RtcPadRoomMapper rtcPadRoomMapper,
                                      ApplicationContext applicationContext, VolcanoRTCManager volcanoRTCManager, RedisService redisService,
                                      IPadService padService,ICustomerService customerService,PadCommsDataService padCommsDataService,
                                      CommonPadTaskComponent commonPadTaskComponent,PadMapper padMapper) {
        this.rtcPadRoomMapper = rtcPadRoomMapper;
        this.applicationContext = applicationContext;
        this.volcanoRTCManager = volcanoRTCManager;
        this.redisService = redisService;
        this.padService = padService;
        this.customerService = customerService;
        this.padCommsDataService = padCommsDataService;
        this.commonPadTaskComponent = commonPadTaskComponent;
        this.padMapper = padMapper;
    }

}
