package net.armcloud.paascenter.rtc.controller.internal;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paascenter.common.client.internal.facade.RoomManageInternalFacade;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.rtc.ManageApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenConsoleDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.rtc.service.IPadRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class RoomManageInternalController implements RoomManageInternalFacade {
    @Value("${rtc.appid}")
    private String appId;
    @Value("${rtc.appkey}")
    private String appKey;
    @Resource
    private IPadRoomService padRoomService;

    @Override
    public Result<RoomTokenVO> getRoomToken(RoomTokenConsoleDTO param) {
        log.info("getRoomToken start padCode={}", param.getPadCode());
        log.info("getRoomToken start param={}", param.toString());
        RoomTokenDTO roomTokenDTO = new RoomTokenDTO();
        BeanUtil.copyProperties(param, roomTokenDTO);
        RoomTokenVO data = padRoomService.getRoomRtcTokenService(param.getUuid(), param.getCustomerId(), appId, appKey, roomTokenDTO);
        log.info("getRoomToken end padCode={}", param.getPadCode());
        return Result.ok(data);
    }

    @Override
    public Result<Object> applyShareToken(ManageApplyShareTokenDTO dto) {
        return Result.ok(padRoomService.manageApplyShareToken(dto));
    }
}
