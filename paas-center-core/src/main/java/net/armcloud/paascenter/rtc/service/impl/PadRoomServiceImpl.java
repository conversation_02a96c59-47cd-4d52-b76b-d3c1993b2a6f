package net.armcloud.paascenter.rtc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.DissolveRoomCMDDTO;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.client.internal.vo.PadInfoVO;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.api.PadCustomerDTO;
import net.armcloud.paascenter.common.model.dto.rtc.ApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.ManageApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.entity.paas.PadRtcTokenLog;
import net.armcloud.paascenter.common.model.vo.rtc.*;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.volcipaas.service.RtcSdkService;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode;
import net.armcloud.paascenter.rtc.manager.RtcPadManager;
import net.armcloud.paascenter.rtc.manager.volcano.VolcanoRTCManager;
import net.armcloud.paascenter.rtc.mapper.RtcPadMapper;
import net.armcloud.paascenter.rtc.mapper.RtcPadRoomMapper;
import net.armcloud.paascenter.rtc.service.IPadRoomService;
import net.armcloud.paascenter.rtc.service.IPadRtcTokenLogService;
import net.armcloud.paascenter.rtc.service.strategy.room.RoomApplicationContext;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.ApplyShareTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.BatchApplyTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.BatchApplyTokenBO.Pad.VideoStream;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.GetRoomTokenStrategyBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.dto.ApplyShareTokenSDKDTO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.dto.BatchApplyTokenDTO;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ONE;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.DESTROY_VOLCANO_ROOM;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
import static net.armcloud.paascenter.common.enums.SourceTargetEnum.PAAS;
import static net.armcloud.paascenter.rtc.constants.LockConstants.APPLY_SHARE_TOKEN_LOCK_PREFIX;
import static net.armcloud.paascenter.rtc.constants.RoomConstants.StreamType.ARMCLOUD_STREAM_TYPE;
import static net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode.INSTANCES_NOT_SUPPORT_SIMULTANEOUS_OPERATIONS;

@Slf4j
@Service
public class PadRoomServiceImpl extends ServiceImpl<RtcPadRoomMapper, PadRoom> implements IPadRoomService {
    private final RtcPadMapper rtcPadMapper;
    private final RtcPadManager rtcPadManager;
    private final RtcSdkService rtcSdkService;
    private final RtcPadRoomMapper rtcPadRoomMapper;
    private final VolcanoRTCManager volcanoRTCManager;
    private final IPadRtcTokenLogService padRtcTokenLogService;
    private final RoomApplicationContext roomApplicationContext;
    private final RedissonDistributedLock redissonDistributedLock;
    private final ICustomerService customerService;
    private final IPadService padService;
    private final PadCommsDataService padCommsDataService;

    @Override
    public RoomTokenVO getRoomRtcTokenService(String validaToken, Long customerId, String appId, String appKey, RoomTokenDTO dto) {
        GetRoomTokenStrategyBO getRoomTokenStrategyBO = new GetRoomTokenStrategyBO();
        getRoomTokenStrategyBO.setValidaToken(validaToken);
        getRoomTokenStrategyBO.setCustomerId(customerId);
        getRoomTokenStrategyBO.setAppId(appId);
        getRoomTokenStrategyBO.setAppKey(appKey);
        getRoomTokenStrategyBO.setRoomTokenDTO(dto);

        String podCode = dto.getPadCode();
        PadInfoVO padInfoVO = rtcPadManager.getPodInfo(podCode);
        //通过PadInfoVO.isOnline判断GameServer在线状态'长连接状态 0-离线 1-在线'
        if (padInfoVO != null && !padInfoVO.isOnline()) {
            throw new BasicException(RtcExceptionCode.PUSH_STREAMING_SERVICE_EXCEPTION);
        }

        if (dto.isPCSdkTerminal()) {
            return roomApplicationContext.getInstance(ARMCLOUD_STREAM_TYPE).generateToken(getRoomTokenStrategyBO);
        }

        return roomApplicationContext.getInstance(padInfoVO.getStreamType()).generateToken(getRoomTokenStrategyBO);
    }

    @Override
    public RefreshRoomTokenVO refreshTokenService(String appId, String appKey, String oldToken) {

        PadRtcTokenLog oldTokenLog = padRtcTokenLogService.getOne(new QueryWrapper<PadRtcTokenLog>().eq("room_token", oldToken).lt("refresh_flag", ONE));
        if (ObjectUtil.isNull(oldTokenLog)) {
            throw new BasicException(RtcExceptionCode.ORIGINAL_TOKEN_INFORMATION_DOES_NOT_EXIST);
        }
        PadRoom padRoom = rtcPadRoomMapper.selectById(oldTokenLog.getRoomId());
        if (ObjectUtil.isNull(padRoom)) {
            throw new BasicException(RtcExceptionCode.ROOM_CORRESPONDING_TO_PAD_DOES_NOT_EXIST);
        }

        Boolean publishStream = oldTokenLog.getPublishStream().equals(NumberConsts.ONE);
        String rtcToken = rtcSdkService.getRtcTokenService(appId, appKey, padRoom.getRoomCode(), oldTokenLog.getUserId(), oldTokenLog.getExpire(), publishStream);

        Integer publishStreamFlag = publishStream.equals(Boolean.TRUE) ? NumberConsts.ONE : ZERO;

        RoomTokenDTO dto = new RoomTokenDTO();
        dto.setPadCode(oldTokenLog.getPadCode());
        dto.setUserId(oldTokenLog.getUserId());
        dto.setExpire(oldTokenLog.getExpire());
        padRtcTokenLogService.saveTokenLog(dto, padRoom, rtcToken, publishStreamFlag);
        padRtcTokenLogService.update(new UpdateWrapper<PadRtcTokenLog>().set("refresh_flag", ONE).eq("id", oldTokenLog.getId()));

        RefreshRoomTokenVO refreshRoomTokenVO = new RefreshRoomTokenVO();
        refreshRoomTokenVO.setRoomToken(rtcToken);
        refreshRoomTokenVO.setToken(rtcToken);
        return refreshRoomTokenVO;
    }

    @Override
    public DissolveRoomVO dissolveRoomService(Long customerId, List<String> padCodes) {
        DissolveRoomVO dissolveRoomVO = new DissolveRoomVO();
        List<RoomErrorVO> failList = new ArrayList<>();
        List<RoomSuccessVO> successList = new ArrayList<>();
        PadCustomerDTO padCustomerDTO = new PadCustomerDTO();
        padCustomerDTO.setCustomerId(customerId);
        for (String padCode : padCodes) {
            RoomSuccessVO roomSuccessVO = new RoomSuccessVO();
            RoomErrorVO roomErrorVO = new RoomErrorVO();

            padCustomerDTO.setPadCode(padCode);
            Pad resultPad = padService.getPadInfoByCustomerId(padCustomerDTO);
            if (ObjectUtil.isNull(resultPad)) {
                roomErrorVO.setPadCode(padCode);
                roomErrorVO.setErrorCode(RtcExceptionCode.NO_STREAMING_PERMISSION_INSTANCE.getStatus());
                roomErrorVO.setErrorMsg(RtcExceptionCode.NO_STREAMING_PERMISSION_INSTANCE.getMsg());
                failList.add(roomErrorVO);
                continue;
            }
            //通知gameService销毁房间
            DissolveRoomCMDDTO data = new DissolveRoomCMDDTO();
            PadCMDForwardDTO request = new PadCMDForwardDTO()
                    .setSourceCode(PAAS)
                    .setCommand(DESTROY_VOLCANO_ROOM)
                    .setPadInfos(Arrays.asList(new PadCMDForwardDTO.PadInfoDTO(padCode, data)));
            try {
                for (CommsTransmissionResultVO commsTransmissionResultVO : padCommsDataService.forward(request)) {
                    Boolean sendSuccess = commsTransmissionResultVO.getSendSuccess();
                    if (Boolean.TRUE.equals(sendSuccess)) {
                        roomSuccessVO.setPadCode(padCode);
                        successList.add(roomSuccessVO);
                    } else {
                        roomErrorVO.setPadCode(padCode);
                        roomErrorVO.setErrorCode(RtcExceptionCode.ABORTING_STREAMING_ERROR_INSTRUCTION_SERVICE_EXCEPTION.getStatus());
                        roomErrorVO.setErrorMsg(RtcExceptionCode.ABORTING_STREAMING_ERROR_INSTRUCTION_SERVICE_EXCEPTION.getMsg());
                        failList.add(roomErrorVO);
                    }
                }
            } catch (Exception e) {
                log.error("通知gameService销毁房间失败！commsCentorPadCMDInternalFeign调用异常，error：" + e.getMessage());
                roomErrorVO.setPadCode(padCode);
                roomErrorVO.setErrorCode(RtcExceptionCode.ABORTING_STREAMING_ERROR_INSTRUCTION_SERVICE_EXCEPTION.getStatus());
                roomErrorVO.setErrorMsg(RtcExceptionCode.ABORTING_STREAMING_ERROR_INSTRUCTION_SERVICE_EXCEPTION.getMsg());
                failList.add(roomErrorVO);
            }
        }
        dissolveRoomVO.setSuccessList(successList);
        dissolveRoomVO.setFailList(failList);
        return dissolveRoomVO;

    }

    @Override
    public List<RoomTokenVO> batchApplyToken(long customerId, BatchApplyTokenDTO roomTokenDTO) {
        // 仅支持ArmCloud推流
        BatchApplyTokenBO batchApplyTokenBO = new BatchApplyTokenBO();
        batchApplyTokenBO.setCustomerId(customerId);
        batchApplyTokenBO.setCustomerUserId(roomTokenDTO.getUserId());
        batchApplyTokenBO.setExpire(roomTokenDTO.getExpire());
        List<BatchApplyTokenBO.Pad> pads = new ArrayList<>();

        roomTokenDTO.getPads().forEach(padDTO -> {
            BatchApplyTokenBO.Pad pad = new BatchApplyTokenBO.Pad();
            pad.setPadCode(padDTO.getPadCode());
            pads.add(pad);

            VideoStream videoStream = new VideoStream();
            pad.setVideoStream(videoStream);

            BatchApplyTokenDTO.Pad.VideoStream videoStreamDTO = Optional.ofNullable(padDTO.getVideoStream()).orElse(new BatchApplyTokenDTO.Pad.VideoStream());
            videoStream.setResolution(videoStreamDTO.getResolution());
            videoStream.setFrameRate(videoStreamDTO.getFrameRate());
            videoStream.setBitrate(videoStreamDTO.getBitrate());
        });
        batchApplyTokenBO.setPads(pads);
        return (List<RoomTokenVO>) roomApplicationContext.getInstance(ARMCLOUD_STREAM_TYPE).batchGenerateToken(batchApplyTokenBO);
    }

    @Override
    public RoomTokenVO applyShareToken(long customerId, ApplyShareTokenDTO dto) {
        log.info("applyShareToken start");
        String lockKey = APPLY_SHARE_TOKEN_LOCK_PREFIX + customerId + dto.getTerminal() + dto.getUserId();
        RLock lock = redissonDistributedLock.mustLocked(lockKey);
        try {
            List<String> padCodes = dto.getPads().stream().map(ApplyShareTokenDTO.Pad::getPadCode).collect(Collectors.toList());
            rtcPadManager.verifyPermissions(padCodes, customerId);
            return applyShareTokenAndNotifyPadJoinRoom(customerId, dto);
        }catch (BasicException e){
            log.warn("applyVolcanoShareToken error>>>", e.getMessage());
            throw new BasicException(e.getMessage());
        }
        catch (Exception e) {
            log.error("applyVolcanoShareToken error>>>", e);
            throw new BasicException(PROCESSING_FAILED);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    private RoomTokenVO applyShareTokenAndNotifyPadJoinRoom(long customerId, ApplyShareTokenDTO dto) {
        log.info("applyShareTokenAndNotifyPadJoinRoom customerId:{}", customerId);
        List<String> padCodes = dto.getPads().stream().map(ApplyShareTokenDTO.Pad::getPadCode).collect(Collectors.toList());
        List<Integer> streamTypes = rtcPadMapper.listByPadCodes(padCodes).stream()
                .map(Pad::getStreamType).distinct().collect(Collectors.toList());
        if (streamTypes.size() > 1) {
            log.error("PadRoomServiceImpl.applyShareTokenAndNotifyPadJoinRoom run error! msg:{},padCodes:{}",INSTANCES_NOT_SUPPORT_SIMULTANEOUS_OPERATIONS.getMsg(), JSONObject.toJSONString(padCodes));
            throw new BasicException(INSTANCES_NOT_SUPPORT_SIMULTANEOUS_OPERATIONS);
        }

        ApplyShareTokenBO applyShareTokenBO = JSON.parseObject(JSON.toJSONString(dto), ApplyShareTokenBO.class);
        applyShareTokenBO.setCustomerId(customerId);
        return roomApplicationContext.getInstance(streamTypes.get(0)).applyShareToken(applyShareTokenBO);
    }

    @Override
    public RoomTokenVO applyShareSDKToken(Long customerId, String validaToken, ApplyShareTokenSDKDTO dto) {
        log.info("applyShareSDKToken start");
        String lockKey = APPLY_SHARE_TOKEN_LOCK_PREFIX + customerId + dto.getTerminal() + dto.getUserId();
        RLock lock = redissonDistributedLock.mustLocked(lockKey);
        try {
            //在鉴权通过后token需要绑定uuid
            VerifyAndGetSDKCustomerDTO verifyAndGetSDKCustomerDTO = new VerifyAndGetSDKCustomerDTO();
            verifyAndGetSDKCustomerDTO.setSdkToken(validaToken);
            verifyAndGetSDKCustomerDTO.setUuid(dto.getUuid());
            Boolean checkResult = customerService.sdkTokenBindUuidService(verifyAndGetSDKCustomerDTO);
            if (Boolean.FALSE.equals(checkResult)) {
                throw new BasicException(RtcExceptionCode.UUID_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_UUID);
            }

            return applyShareToken(customerId, dto);
        }catch (BasicException e){
            //自定义异常直接往外层抛出,由全局异常捕获处理
            log.warn("applyVolcanoShareToken error>>>：{}", e.getMessage());
            throw new BasicException(e.getMessage());
        } catch (Exception e) {
            log.error("applyVolcanoShareToken error>>>", e);
            throw new BasicException(PROCESSING_FAILED);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    @Override
    public RoomTokenVO manageApplyShareToken(ManageApplyShareTokenDTO dto) {
        long customerId = dto.getCustomerId();
        String lockKey = APPLY_SHARE_TOKEN_LOCK_PREFIX + customerId + dto.getTerminal() + dto.getUserId();
        RLock lock = redissonDistributedLock.mustLocked(lockKey);
        try {
            return applyShareTokenAndNotifyPadJoinRoom(customerId, dto);
        } catch (Exception e) {
            log.error("manageApplyShareToken error>>>", e);
            throw new BasicException(PROCESSING_FAILED);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    public PadRoomServiceImpl(RtcSdkService rtcSdkService,
                              IPadRtcTokenLogService padRtcTokenLogService,
                              RtcPadRoomMapper rtcPadRoomMapper,
                              RtcPadManager rtcPadManager,
                              RoomApplicationContext roomApplicationContext,
                              VolcanoRTCManager volcanoRTCManager,
                              RedissonDistributedLock redissonDistributedLock,
                              RtcPadMapper rtcPadMapper,
                              ICustomerService customerService,
                              IPadService padService,
                              PadCommsDataService padCommsDataService) {
        this.rtcSdkService = rtcSdkService;
        this.padRtcTokenLogService = padRtcTokenLogService;
        this.rtcPadRoomMapper = rtcPadRoomMapper;
        this.rtcPadManager = rtcPadManager;
        this.roomApplicationContext = roomApplicationContext;
        this.volcanoRTCManager = volcanoRTCManager;
        this.redissonDistributedLock = redissonDistributedLock;
        this.rtcPadMapper = rtcPadMapper;
        this.customerService = customerService;
        this.padService = padService;
        this.padCommsDataService = padCommsDataService;
    }
}
