package net.armcloud.paascenter.rtc.service.strategy.room.model.bo;

import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
public class BatchApplyTokenBO {
    private Long customerId;

    /**
     * 调用方自有用户Id
     */
    private String customerUserId;

    /**
     * token有效期 单位：秒
     */
    private Integer expire = 3600;

    @Valid
    private List<Pad> pads;

    @Data
    public static class Pad {
        private String padCode;
        private VideoStream videoStream;

        @Data
        public static class VideoStream {
            private String resolution;
            private String frameRate;
            private String bitrate;
        }
    }
}
