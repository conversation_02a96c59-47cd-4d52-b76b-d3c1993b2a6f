package net.armcloud.paascenter.rtc.service.strategy.room.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class BatchApplyTokenDTO {
    /**
     * 调用方自有用户Id
     */
    @NotBlank(message = "userId cannot null")
    private String userId;

    /**
     * token有效期 单位：秒
     */
    private Integer expire = 3600;

    @Valid
    @Size(min = 1, message = "pads cannot null")
    @NotNull(message = "pads cannot null")
    private List<Pad> pads;

    @Data
    public static class Pad {
        @NotBlank(message = "padCode cannot null")
        private String padCode;
        private VideoStream videoStream;

        @Data
        public static class VideoStream {
            private String resolution;
            private String frameRate;
            private String bitrate;
        }
    }
}
