-- Magisk ROM OSS记录表
CREATE TABLE magisk_rom_oss_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增id',
    oss_url VARCHAR(1024) NOT NULL COMMENT 'oss地址',
    version VARCHAR(100) NOT NULL COMMENT '包版本',
    replace_time TIMESTAMP NULL COMMENT '替换时间',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(100) NOT NULL COMMENT '创建人',
    update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(100) NULL COMMENT '更新人',
    UNIQUE KEY uk_version (version) COMMENT '版本唯一键',
    INDEX idx_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Magisk ROM OSS记录表';
