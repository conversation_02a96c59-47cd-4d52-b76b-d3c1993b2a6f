<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.KeepAliveAppRelationMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into `keep_alive_app_relation`
        (customer_id,  server_name,relation_id,status,create_time,create_by,update_time,update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.serverName},#{item.relationId},#{item.status}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>
</mapper>
