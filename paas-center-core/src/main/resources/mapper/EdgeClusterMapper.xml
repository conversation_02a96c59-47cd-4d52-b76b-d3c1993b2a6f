<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.EdgeClusterMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="cluster_code" jdbcType="VARCHAR" property="clusterCode"/>
        <result column="dc_code" jdbcType="VARCHAR" property="dcCode"/>
        <result column="online_status" jdbcType="TINYINT" property="onlineStatus"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="cluster_public_ip" jdbcType="VARCHAR" property="clusterPublicIp"/>
        <result column="server_subnet_ip" jdbcType="VARCHAR" property="serverSubnetIp"/>
        <result column="device_net" jdbcType="VARCHAR" property="deviceNet"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag"/>
        <result column="server_num" jdbcType="INTEGER" property="serverNum"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , cluster_name, cluster_code, dc_code, online_status, status, cluster_public_ip,
    server_subnet_ip, device_net ,remarks, delete_flag, server_num,create_by, create_time, update_by, update_time,storage_capacity
    </sql>
    <select id="selectClusterByArmServerCodeAndStatusAndOnline" resultType="net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO">
        SELECT cluster_code,
        cluster_public_ip,
        dc_code from edge_cluster
       where cluster_code= #{clusterCode} and online_status = #{online} and status = #{status}
    </select>

    <select id="selectListEdgeCluster" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where online_status = 1 and status = 1 and delete_flag = 0
    </select>

    <update id="updateEdgeClusterByClusterCode">
        update edge_cluster
        set server_num = #{serverCode}
        where cluster_code = #{clusterCode,jdbcType=VARCHAR}
    </update>

    <select id="selectClusterByClusterCode" resultType="net.armcloud.paascenter.common.model.entity.paas.EdgeCluster">
        select
        <include refid="Base_Column_List"/>
        from
        edge_cluster
        where cluster_code = #{clusterCode} and delete_flag = 0
    </select>

    <select id="selectEdgeClusterByPadCodes" resultType="net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO">
        SELECT t3.cluster_code,
        t3.cluster_public_ip,
        t3.dc_code
        FROM pad t1
        LEFT JOIN arm_server t2 on t1.arm_server_code = t2.arm_server_code and t2.delete_flag = 0
        LEFT JOIN edge_cluster t3 on t3.cluster_code = t2.cluster_code and t3.delete_flag = 0
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        GROUP BY t3.cluster_code, t3.cluster_public_ip, t3.dc_code
    </select>

    <update id="updateOnlineStatusById">
        update edge_cluster
        set online_status = #{onlineStatus}
        where id #{id}
    </update>


    <select id="selectEdgeClusterCodeByPadCodeSingle" resultType="java.lang.String">
        SELECT t4.cluster_code
        FROM pad t1
                 LEFT JOIN device_pad t2 on t1.id = t2.pad_id
                 LEFT JOIN device t3 on t2.device_id = t3.id and t3.delete_flag = 0
                 LEFT JOIN arm_server t4 on t4.arm_server_code = t3.arm_server_code and t4.delete_flag = 0
        where t1.pad_code =#{padCode}
            limit 1
    </select>
</mapper>