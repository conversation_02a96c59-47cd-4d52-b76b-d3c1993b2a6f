<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.CmdDownloadQueueMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
        insert into cmd_download_queue
        (pad_code, cmd_data,  create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.padCode}, #{item.cmdData},  #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <update id="updateStatusByRequestId">
        update cmd_download_queue
        set status = #{status}
        where request_id = #{requestId}
    </update>

</mapper>