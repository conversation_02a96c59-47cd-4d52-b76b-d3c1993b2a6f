<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CountryInfoMapper">


    <select id="selectCountryInfoByCountryCode" resultType="net.armcloud.paascenter.common.model.entity.paas.CountryInfo">
       select * from country_info where country_code = #{countryCode}
    </select>


</mapper>