<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.AdiTemplateLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.AdiTemplateLog">
        <id column="id" property="id" />
        <result column="template_id" property="templateId" />
        <result column="operation_type" property="operationType" />
        <result column="operator" property="operator" />
        <result column="operation_detail" property="operationDetail" />
        <result column="operation_time" property="operationTime" />
    </resultMap>

    <!-- 查询ADI模板操作日志列表（带模板名称和操作者名称） -->
    <select id="selectAdiTemplateLogsWithNames" resultType="java.util.Map">
        SELECT
        atl.id,
        atl.template_id,
        at.template_name,
        atl.operation_type,
        atl.operator,
        u.username as operator_name,
        atl.operation_detail,
        atl.operation_time
        FROM
        adi_template_log atl
        LEFT JOIN
        adi_template at ON atl.template_id = at.id
        LEFT JOIN
        sys_user u ON atl.operator = u.id
        <where>
            <if test="params.templateId != null">
                AND atl.template_id = #{params.templateId}
            </if>
            <if test="params.operationType != null">
                AND atl.operation_type = #{params.operationType}
            </if>
            <if test="params.operator != null">
                AND atl.operator = #{params.operator}
            </if>
            <if test="params.startTime != null">
                AND atl.operation_time &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                AND atl.operation_time &lt;= #{params.endTime}
            </if>
        </where>
        ORDER BY atl.operation_time DESC
    </select>

    <!-- 获取指定模板ID的最新操作日志 -->
    <select id="selectLatestLogByTemplateId" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        adi_template_log
        WHERE
        template_id = #{templateId}
        ORDER BY
        operation_time DESC
        LIMIT 1
    </select>

</mapper>