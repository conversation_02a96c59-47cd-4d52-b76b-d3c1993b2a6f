<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.AdiTemplateCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.AdiTemplateCustomer">
        <result column="template_id" property="templateId" />
        <result column="customer_id" property="customerId" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>
    
    <!-- 批量插入模板客户关系 -->
    <insert id="batchInsertTemplateCustomers">
        INSERT INTO adi_template_customer (template_id, customer_id, create_time, update_time,create_by,update_by)
        VALUES 
        <foreach collection="customerIds" item="customerId" separator=",">
            (#{templateId}, #{customerId}, NOW(), NOW(),#{operationId},#{operationId})
        </foreach>
    </insert>
    
    <!-- 根据模板ID删除所有关联关系 -->
    <delete id="deleteByTemplateId">
        DELETE FROM adi_template_customer WHERE template_id = #{templateId}
    </delete>
    
    <!-- 查询模板关联的客户ID列表 -->
    <select id="selectCustomerIdsByTemplateId" resultType="java.lang.Long">
        SELECT customer_id FROM adi_template_customer WHERE template_id = #{templateId}
    </select>

</mapper>