<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.CmsImageTaskMapper">

    <select id="taskList" resultType="net.armcloud.paascenter.common.model.vo.api.TaskImageUploadVo">
        select customer_task_id                          as taskId,
        CASE
        WHEN `status` IN (1, 2) THEN 1
        WHEN `status` = 3 THEN 2
        ELSE -1
        END AS taskStatus,
        error_msg as errorMsg
        from image_task
        <where>
            delete_flag = 0
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="taskIds != null and taskIds.size() != 0">
                and customer_task_id in
                <foreach collection="taskIds" item="customerTaskId" open="(" separator="," close=")">
                    #{customerTaskId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
</mapper>