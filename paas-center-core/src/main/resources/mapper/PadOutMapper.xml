<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadOutMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.Pad">
        <id column="id" property="id" />
        <result column="pad_code" property="padCode" />
        <result column="pad_out_code" property="padOutCode" />
        <result column="device_level" property="deviceLevel" />
        <result column="pad_ip" property="padIp" />
        <result column="pad_sn" property="padSn" />
        <result column="image_id" property="imageId" />
        <result column="cloud_vendor_type" property="cloudVendorType" />
        <result column="customer_id" property="customerId" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, pad_code, pad_out_code, device_level, pad_ip, pad_sn, image_id, cloud_vendor_type,
        customer_id, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="getByPadCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad
        where pad_code = #{padCode}
    </select>
</mapper>