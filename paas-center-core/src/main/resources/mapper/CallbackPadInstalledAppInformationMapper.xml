<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.callback.mapper.CallbackPadInstalledAppInformationMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation">
    <id column="id" property="id" />
    <result column="pad_code" property="padCode" />
    <result column="apps_json" property="appsJSON" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, pad_code, apps_json, create_by, create_time, update_by, update_time
  </sql>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation" useGeneratedKeys="true">
    insert into pad_installed_app_information
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="padCode != null">
        pad_code,
      </if>
      <if test="appsJSON != null">
        apps_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="padCode != null">
        #{padCode},
      </if>
      <if test="appsJSON != null">
        #{appsJSON},
      </if>
    </trim>
  </insert>
</mapper>