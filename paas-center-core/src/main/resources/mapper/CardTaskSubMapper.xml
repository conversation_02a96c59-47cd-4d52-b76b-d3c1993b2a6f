<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.bmc.mapper.CardTaskSubMapper">
    <insert id="insertCardTaskSub" parameterType="net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub"
            useGeneratedKeys="true" keyProperty="id">
        insert into card_task_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainTask != null">
                main_task,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="timeout != null ">
                timeout,
            </if>
            <if test="serverSn != null ">
                server_sn,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainTask != null">
                #{mainTask},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="timeout != null ">
                #{timeout},
            </if>
            <if test="serverSn != null ">
                #{serverSn},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO card_task_sub (main_task, type,server_sn, timeout)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.mainTask}, #{item.type}, #{item.serverSn}, #{item.timeout})
        </foreach>
    </insert>
    <select id="selectExecutedServerSn" resultType="string" parameterType="integer">
        SELECT server_sn
        from card_task_sub
        where type = #{type}
          and status = 1
        GROUP BY server_sn
    </select>


</mapper>