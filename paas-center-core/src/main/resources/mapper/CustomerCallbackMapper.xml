<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerCallbackMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerCallback">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="callback_id" jdbcType="BIGINT" property="callbackId" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="host" jdbcType="VARCHAR" property="host" />
        <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
        <result column="enable" jdbcType="TINYINT" property="enable" />
        <result column="delete_flag" jdbcType="BOOLEAN" property="deleteFlag" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>
    <resultMap id="CallbackMap" type="net.armcloud.paascenter.common.client.internal.vo.CustomerCallbackVO">
        <id column="customer_id" jdbcType="BIGINT" property="customerId" />
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
        <result column="type" jdbcType="VARCHAR" property="callbackType" />
        <result column="callbackId" jdbcType="VARCHAR" property="callbackId" />
        <result column="description" jdbcType="BIGINT" property="callbackName" />
        <result column="categories" jdbcType="BIGINT" property="categories" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="type" jdbcType="VARCHAR" property="callbackType" />
    </resultMap>

    <sql id="Base_Column_List">
        id, callback_id, customer_id, host, callback_url, enable, delete_flag, create_time,
        create_by, update_time, update_by
    </sql>
    <insert id="batchInsert">
        INSERT INTO customer_callback (callback_id, callback_url, customer_id, host, enable, delete_flag, create_time, create_by )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.callbackId}, #{item.callbackUrl}, #{item.customerId}, #{item.host}, #{item.enable}, #{item.deleteFlag}, #{item.createTime}, #{item.createBy})
        </foreach>
    </insert>
    <update id="DeleteCallback">
        update customer_callback set delete_flag = 1 where customer_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateCallback">
        update customer_callback
        <set>
            update_time = now(),
            <if test="callbackUrl != null and callbackUrl != ''">
                callback_url = #{callbackUrl}
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="deleteByCustomerIdAndCallBackId">
        update customer_callback set delete_flag = 1 where customer_id  = #{customerId} and delete_flag= 0 and callback_id = #{callBackId}
    </update>

    <update id="deleteByCustomerId">
        update customer_callback set delete_flag = 1 where customer_id  = #{customerId} and delete_flag= 0
    </update>

    <select id="selectByCustomerIdList" parameterType="java.lang.Long" resultMap="CallbackMap">
        SELECT
        GROUP_CONCAT( DISTINCT pci.type ) AS type,
        GROUP_CONCAT( DISTINCT pci.id ) AS callbackId,
        GROUP_CONCAT( DISTINCT ctr.customer_name) as customer_name,
        pcc.customer_id,
        GROUP_CONCAT( DISTINCT pcc.HOST ) AS HOST,
        pcc.callback_url,
        GROUP_CONCAT( DISTINCT pci.description ) description
        FROM
        callback_information pci
        JOIN customer_callback pcc ON pci.id = pcc.callback_id
        inner join customer ctr on ctr.id = pcc.customer_id
        WHERE
        pcc.ENABLE = 1
        AND pcc.delete_flag = 0
        <if test="customerId != null and customerId != '' ">
            and pcc.customer_id = #{customerId}
        </if>
        GROUP BY
        customer_id,
        callback_url

    </select>
    <select id="selectList"  resultMap="CallbackMap">
        select id,description,type
        from callback_information
        where delete_flag = 0
    </select>
    <select id="selectByCallbackId" resultMap="CallbackMap">
        select pcc.id,
               pci.description
        from callback_information pci
                 join customer_callback pcc on pci.id = pcc.callback_id
        where pcc.callback_id = #{callbackId}
          and pcc.enable = 1
          and pcc.delete_flag = 0
          and pcc.customer_id = #{customerId}
    </select>

    <select id="selectById" resultMap="CallbackMap">
        select pcc.customer_id,
               pcc.host,
               pcc.callback_url,
               pcc.id,
               pci.type,
               pci.description
        from customer_callback pcc
        join callback_information pci on pci.id = pcc.callback_id
        where pcc.id = #{id}
          and pcc.delete_flag = 0
    </select>
    <select id="selectCallback" resultType="java.lang.String">
        SELECT callback_url from customer_callback where customer_id = #{customerId} and delete_flag = 0 limit 1
    </select>
</mapper>