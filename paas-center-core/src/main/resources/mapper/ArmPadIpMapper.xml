<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.ArmPadIpMapper">
    <insert id="batchSaveArmPadIp">
        INSERT INTO arm_pad_ip (arm_server_id, net_pad_id)
        VALUES
        <foreach collection="netPadIds" item="netPadId" separator=",">
            (#{id}, #{netPadId})
        </foreach>
    </insert>
    <update id="updateArmPad" parameterType="net.armcloud.paascenter.common.model.entity.paas.ArmPadIp">
        update arm_pad_ip
        <set>
            <if test="armServerId != null">
                arm_server_id = #{armServerId,jdbcType=BIGINT},
            </if>
            <if test="netPadId != null">
                net_pad_id = #{netPadId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteByArmServerIdOrNetPadId">
        delete
        from arm_pad_ip
        <where>
            <if test="armServerId != null">
                and arm_server_id = #{armServerId,jdbcType=BIGINT}
            </if>
            <if test="netPadId != null">
               and net_pad_id = #{netPadId,jdbcType=BIGINT}
            </if>
        </where>
    </delete>
    <select id="selectByArmServerIdOrNetPadId" resultType="net.armcloud.paascenter.common.model.entity.paas.ArmPadIp">
        select id, arm_server_id, net_pad_id
        from arm_pad_ip
        <where>
            <if test="armServerId != null">
                and arm_server_id = #{armServerId,jdbcType=BIGINT}
            </if>
            <if test="netPadId != null">
               and  net_pad_id = #{netPadId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
</mapper>