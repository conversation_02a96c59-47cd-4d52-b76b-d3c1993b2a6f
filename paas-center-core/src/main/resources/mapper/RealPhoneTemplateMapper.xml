<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.RealPhoneTemplateMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate">
        <id column="id" property="id" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="resource_specification_code" property="resourceSpecificationCode" />
        <result column="screen_layout_code" property="screenLayoutCode" />
        <result column="adi_template_download_url" property="adiTemplateDownloadUrl" />
        <result column="adi_template_pwd" property="adiTemplatePwd" />
        <result column="property_json" property="propertyJSON" />
        <result column="android_image_version" property="androidImageVersion" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_official" property="isOfficial" />
        <result column="device_name" property="deviceName" />
        <result column="status" property="status" />
        <result column="customer_id" property="customerId" />
        <result column="adi_template_version" property="adiTemplateVersion" />
        <result column="model_code" property="modelCode" />
        <result column="test_cases_download_url" property="testCasesDownloadUrl" />
        <result column="aosp_version" property="aospVersion" />
    </resultMap>
    
    <!-- 定义AdiTemplateVO的结果映射 -->
    <resultMap id="AdiTemplateVOResultMap" type="net.armcloud.paascenter.openapi.model.vo.AdiTemplateVO">
        <id column="id" property="id" />
        <result column="device_name" property="deviceName" />
        <result column="brand" property="brand" />
        <result column="model" property="model" />
        <result column="fingerprint" property="fingerprint" />
        <result column="fingerprint_md5" property="fingerprintMd5" />
        <result column="android_image_version" property="androidImageVersion" />
        <result column="adi_template_version" property="adiTemplateVersion" />
        <result column="screen_layout_code" property="screenLayoutCode" />
        <result column="resource_specification_code" property="resourceSpecificationCode" />
        <result column="adi_template_download_url" property="adiTemplateDownloadUrl" />
        <result column="is_official" property="isOfficial" />
        <result column="is_official_desc" property="isOfficialDesc" />
        <result column="status" property="status" />
        <result column="status_desc" property="statusDesc" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="is_public" property="isPublic" />
        <result column="instance_count" property="instanceCount" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="screen_width" property="screenWidth" />
        <result column="screen_high" property="screenHigh" />
        <result column="pixel_density" property="pixelDensity" />
        <result column="screen_refresh_rate" property="screenRefreshRate" />
        <result column="model_code" property="modelCode" />
        <result column="test_cases_download_url" property="testCasesDownloadUrl" />
        <result column="aosp_version" property="aospVersion" />
    </resultMap>

    <sql id="Base_Column_List">
        id, brand, model, resource_specification_code, screen_layout_code, adi_template_download_url,
    adi_template_pwd, property_json, android_image_version, fingerprint, fingerprint_md5, delete_flag, create_by, create_time, update_by, update_time,is_official,
        device_name,status,customer_id,adi_template_version
    </sql>


    <!-- 检查指纹MD5是否已存在 -->
    <select id="checkFingerprintMd5Exists" resultType="net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate">
        SELECT
            t.*
        FROM
            real_phone_template t
        INNER JOIN
            adi_template_customer atc ON t.id = atc.template_id
        WHERE
            t.fingerprint_md5 = #{fingerprintMd5}
            AND atc.customer_id = #{customerId}
            AND t.delete_flag = 0
        LIMIT 1
    </select>
    
    <!-- 优化版 - 查询ADI模板列表，直接返回VO对象 -->
    <select id="queryAdiTemplates" resultMap="AdiTemplateVOResultMap">
        SELECT
            t.id,
            t.brand,
            t.model,
            t.device_name,
            t.fingerprint,
            t.fingerprint_md5,
            t.android_image_version,
            t.screen_layout_code,
            t.resource_specification_code,
            t.adi_template_download_url,
            t.adi_template_pwd,
            t.is_official,
            d2.dict_label as is_official_desc,
            t.status,
            d1.dict_label as status_desc,
            NULL as customer_id,
            (SELECT GROUP_CONCAT(c.customer_name SEPARATOR ', ')
             FROM adi_template_customer atc
             JOIN customer c ON atc.customer_id = c.id
             WHERE atc.template_id = t.id) as customer_name,
            t.is_public,
            (SELECT COUNT(1) FROM pad WHERE real_phone_template_id = t.id AND status = 1) as instance_count,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.adi_template_version,
            sl.screen_width,
            sl.screen_high,
            sl.pixel_density,
            sl.screen_refresh_rate,
            t.model_code,
            t.test_cases_download_url,
            t.aosp_version
        FROM
            real_phone_template t
        LEFT JOIN 
            dict d1 ON d1.dict_value = t.status AND d1.dict_type = 'adi_template_status'
        LEFT JOIN 
            dict d2 ON d2.dict_value = t.is_official AND d2.dict_type = 'adi_template_official'
        LEFT JOIN screen_layout sl ON sl.code = t.screen_layout_code
        <where>
            <!-- 基本过滤条件 -->
            AND t.delete_flag = 0
            <if test="query.brand != null and query.brand != ''">
                AND t.brand = #{query.brand}
            </if>
            <if test="query.model != null and query.model != ''" >
                AND t.id = #{query.model}
            </if>
            <if test="query.androidImageVersion != null and query.androidImageVersion != ''">
                AND t.android_image_version = #{query.androidImageVersion}
            </if>
            <if test="query.isOfficial != null">
                AND t.is_official = #{query.isOfficial}
            </if>
            <if test="query.adiTemplateVersion != null and query.adiTemplateVersion != ''">
                AND t.adi_template_version = #{query.adiTemplateVersion}
            </if>
            <if test="query.aospVersion != null and query.aospVersion != ''">
                AND t.aosp_version = #{query.aospVersion}
            </if>
            
            <!-- 模板类型过滤 -->
            <if test="query.templateType != null">
                <choose>
                    <!-- 模板类型1 - 公共模板：对所有用户都显示 -->
                    <when test="query.templateType == 1">
                        AND t.is_public = 1
                    </when>
                    <!-- 模板类型2 - 自定义模板：根据权限控制显示 -->
                    <when test="query.templateType == 2">
                        AND t.is_public = 0
                        <choose>
                            <!-- 管理员且指定了客户ID -->
                            <when test="isAdmin and query.customerIds != null and !query.customerIds.isEmpty()">
                                AND EXISTS (
                                    SELECT 1 FROM adi_template_customer atc2 
                                    WHERE atc2.template_id = t.id AND atc2.customer_id IN
                                    <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                                        #{customerId}
                                    </foreach>
                                )
                            </when>
                            <!-- 管理员且未指定客户ID，查询所有自定义模板 -->
                            <when test="isAdmin">
                                <!-- 管理员可以查看所有自定义模板，无需额外条件 -->
                            </when>
                            <!-- 非管理员，只能查看自己的自定义模板 -->
                            <otherwise>
                                AND EXISTS (
                                    SELECT 1 FROM adi_template_customer atc2 
                                    WHERE atc2.template_id = t.id AND atc2.customer_id IN
                                    <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                                        #{customerId}
                                    </foreach>
                                )
                            </otherwise>
                        </choose>
                    </when>
                </choose>
            </if>
            
            <!-- 当未指定模板类型时的逻辑 -->
            <if test="query.templateType == null">
                <choose>
                    <!-- 如果是管理员且customerIds不为空，根据customerIds条件查询 -->
                    <when test="isAdmin and query.customerIds != null and !query.customerIds.isEmpty()">
                        AND EXISTS (
                            SELECT 1 FROM adi_template_customer atc2 
                            WHERE atc2.template_id = t.id AND atc2.customer_id IN
                            <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                                #{customerId}
                            </foreach>
                        )
                    </when>
                    <!-- 如果是管理员且customerIds为空，查询全部数据 -->
                    <when test="isAdmin">
                        <!-- 不添加额外条件，查询所有模板 -->
                    </when>
                    <!-- 如果不是管理员，只能查询自己的模板和公共模板 -->
                    <otherwise>
                        AND (t.is_public = 1 OR EXISTS (
                            SELECT 1 FROM adi_template_customer atc2 
                            WHERE atc2.template_id = t.id AND atc2.customer_id IN
                            <foreach collection="query.customerIds" item="customerId" open="(" separator="," close=")">
                                #{customerId}
                            </foreach>
                        ))
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>
    
    <!-- 优化版 - 根据条件查询模板列表用于选择 -->
    <select id="listForSelectionOptimized" resultMap="AdiTemplateVOResultMap">
        SELECT
            t.id,
            t.brand,
            t.model,
            t.device_name,
            t.fingerprint,
            t.fingerprint_md5,
            t.android_image_version,
            t.screen_layout_code,
            t.resource_specification_code,
            t.adi_template_download_url,
            t.adi_template_pwd,
            t.is_official,
            d2.dict_label as is_official_desc,
            t.status,
            d1.dict_label as status_desc,
            NULL as customer_id,
            (SELECT GROUP_CONCAT(c.customer_name SEPARATOR ', ') 
             FROM adi_template_customer atc 
             JOIN customer c ON atc.customer_id = c.id 
             WHERE atc.template_id = t.id) as customer_name,
            t.is_public,
            (SELECT COUNT(1) FROM pad WHERE real_phone_template_id = t.id AND status = 1) as instance_count,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.model_code
        FROM
            real_phone_template t
        LEFT JOIN 
            dict d1 ON d1.dict_value =t.status  AND d1.dict_type = 'adi_template_status'
        LEFT JOIN 
            dict d2 ON d2.dict_value =t.is_official  AND d2.dict_type = 'adi_template_official'
        <where>
            AND t.delete_flag = 0
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="isOfficial != null">
                AND t.is_official = #{isOfficial}
            </if>
            
            <choose>
                <!-- 如果是管理员且customerIds不为空，根据customerIds条件查询 -->
                <when test="isAdmin and customerIds != null and !customerIds.isEmpty()">
                    AND EXISTS (
                        SELECT 1 FROM adi_template_customer atc2 
                        WHERE atc2.template_id = t.id AND atc2.customer_id IN
                        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                            #{customerId}
                        </foreach>
                    )
                </when>
                <!-- 如果是管理员且customerIds为空，查询全部数据 -->
                <when test="isAdmin">
                    <!-- 不添加额外条件，查询所有模板 -->
                </when>
                <!-- 如果不是管理员，只能查询自己的模板和公共模板 -->
                <otherwise>
                    AND (t.is_public = 1 OR EXISTS (
                        SELECT 1 FROM adi_template_customer atc2 
                        WHERE atc2.template_id = t.id AND atc2.customer_id IN
                        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                            #{customerId}
                        </foreach>
                    ))
                </otherwise>
            </choose>
        </where>
        ORDER BY t.update_time DESC
    </select>
    
    <!-- 统计模板关联的实例数量 -->
    <select id="countTemplateInstances" resultType="java.lang.Integer">
        SELECT COUNT(1) 
        FROM pad 
        WHERE real_phone_template_id = #{templateId} 
        AND status = 1
    </select>

</mapper>
