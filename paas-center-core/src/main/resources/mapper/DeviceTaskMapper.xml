<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.DeviceTaskMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.DeviceTask">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
        <result column="timeout_time" jdbcType="TIMESTAMP" property="timeoutTime" />
        <result column="file_id" jdbcType="BIGINT" property="fileId" />
        <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
        <result column="result" jdbcType="VARCHAR" property="result" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="delete_flag" jdbcType="BOOLEAN" property="deleteFlag" />
    </resultMap>
    <sql id="Base_Column_List">
        id, unique_id, task_id, `status`, device_code, timeout_time, file_id, task_content,
    start_time, end_time, error_msg, `result`, create_by, create_time, update_by, update_time,
    delete_flag
    </sql>
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        insert into device_task (unique_id, task_id, status, device_code, file_id, task_content, end_time, timeout_time, error_msg, create_by,customer_task_id,customer_id,remarks,start_time,task_mode)
        values
        <foreach collection="deviceTasks" item="item" separator=",">
            (#{item.uniqueId}, #{item.taskId}, #{item.status}, #{item.deviceCode}, #{item.fileId}, #{item.taskContent},#{item.endTime},
            #{item.timeoutTime}, #{item.errorMsg}, #{item.createBy}, #{item.customerTaskId},#{item.customerId},#{item.remarks},#{item.startTime},#{item.taskMode})
        </foreach>
    </insert>
    <insert id="insertBatchDefaultTimeout"  useGeneratedKeys="true" keyProperty="id">
        insert into device_task (unique_id, task_id, status, device_code, file_id, task_content, end_time, error_msg, create_by, customer_task_id, customer_id, remarks,start_time,task_mode)
        values
        <foreach collection="deviceTasks" item="item" separator=",">
            (#{item.uniqueId}, #{item.taskId}, #{item.status}, #{item.deviceCode}, #{item.fileId}, #{item.taskContent},#{item.endTime},
            #{item.errorMsg}, #{item.createBy}, #{item.customerTaskId},#{item.customerId},#{item.remarks},#{item.startTime},#{item.taskMode})
        </foreach>
    </insert>
    <update id="batchUpdateUniqueId" parameterType="java.util.List">
        update device_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unique_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.uniqueId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <update id="updateSetTimeout">
        UPDATE device_task
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="containerTaskId != null">container_task_id = #{containerTaskId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="timeoutTime != null">timeout_time = #{timeoutTime},</if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="getLastestTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_task
        where device_code = #{padCode}
        order by id desc
        limit 1
    </select>
    <select id="listByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_task
        where task_id = #{taskId}
        and delete_flag = 0
    </select>
    <update id="updateDeviceTask">
        UPDATE device_task dt
            JOIN task t ON dt.task_id = t.id
            SET dt.bmc_task_id = #{bmcTaskId}
        WHERE t.type = #{type}
          AND dt.customer_task_id = #{customerTaskId}
    </update>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_task
        where id = #{id}
    </select>

    <update id="updateStatus">
        UPDATE device_task set status = #{status}
        <if test="timeoutTime != null">
            ,timeout_time = #{timeoutTime}
        </if>
        where id = #{id} AND `status` IN (1,2)
    </update>

    <update id="pullModeUpdateTimeout">
        UPDATE device_task
        SET timeout_time = #{timeoutTime},error_msg = #{errorMsg}
        WHERE
            delete_flag = 0
          AND `status` IN (2)
          AND id = #{id}
    </update>

    <select id="countRunDeviceTaskByType" resultType="java.lang.String">
        SELECT
        dt.device_code
        FROM
            device_task dt
                LEFT JOIN task t ON dt.task_id = t.id
        WHERE
            dt.device_code in
        <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
            #{deviceCode}
        </foreach>
          AND t.type = #{type}
          AND dt.STATUS IN ( 1, 2 )
          AND dt.delete_flag = 0
    </select>

    <select id="selectTheTypeDoingDeviceTask" resultType="java.lang.Long">
        select p.id
        from device_task p
                 left join task t on p.task_id = t.id
        where p.delete_flag = 0
          and t.delete_flag = 0
          and p.status = 2
          and p.device_code = #{padCode}
          and t.type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>
</mapper>