<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.job.mapper.PadTaskRestoreMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.PadRestoreTaskInfo">
        <id column="id" property="id" />
        <result column="restore_name" property="restoreName" />
        <result column="sub_task_id" property="subTaskId" />
        <result column="device_id" property="deviceId" />
        <result column="pad_code" property="padCode" />
        <result column="backup_id" property="backupId" />
        <result column="customer_id" property="customerId" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <sql id="Base_Column_List">
        id, restore_name, sub_task_id, device_id, pad_code, backup_id, customer_id, delete_flag,
        create_time, create_by, update_time, update_by
    </sql>


    <select id="getLatestByPadCodeAndSubTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad_restore_task_info
        where pad_code = #{padCode}
          and delete_flag = false
        and sub_task_id = #{subTaskId}
        order by id desc
        limit 1
    </select>
</mapper>