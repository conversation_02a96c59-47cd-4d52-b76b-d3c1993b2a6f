<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailImageSuccMapper">

    <select id="getAssignDataIds" resultType="java.lang.Long">
        select id from task_rel_instance_detail_image_succ
        where instance_name = #{padCode} and task_type in (100,204,209)
        <if test="dcId != null">
            and dc_id = #{dcId}
        </if>
        order by id desc
        limit #{pageIndex},100
    </select>
</mapper>
