<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.NetPadMapper">

    <sql id="NetPadVO">
        id, `name`, ipv4_cidr,bind_flag, remarks, create_time
    </sql>
    <sql id="Base_Column_List">
        id, `name`, ipv4_cidr,bind_flag, remarks, delete_flag, create_by, create_time, update_by,
    update_time
    </sql>
    <select id="selectIpv4CidrsByArmServer" resultType="java.lang.String">
        SELECT
            t2.ipv4_cidr
        FROM
            arm_pad_ip t1
                LEFT JOIN net_pad t2 ON t2.id = t1.net_pad_id
        WHERE
            t1.arm_server_id = #{armServerId}
          AND delete_flag = 0
          and bind_flag = 1
    </select>

    <select id="selectByIds" resultType="net.armcloud.paascenter.common.model.entity.paas.NetPad">
        select
        <include refid="NetPadVO"/>
        from
        net_pad
        where id in
        <foreach collection="netPadIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <update id="updateNetDeviceBindFlag">
        update net_device set bind_flag = #{bindFlag} where ipv4_cidr = #{deviceSubnet} and delete_flag=0
    </update>

    <update id="updateNetPadBindFlag">
        update net_pad set bind_flag = #{bindFlag} where id in
        <foreach collection="netPadIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectNetPadByIpv4OrNameExcludingId" resultType="net.armcloud.paascenter.common.model.entity.paas.NetPad">
        select
        <include refid="Base_Column_List"/>
        from
        net_pad
        where (ipv4_cidr = #{ipv4Cidr} or name = #{name})  and delete_flag = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <insert id="saveNetPad" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.NetPad" useGeneratedKeys="true">
        insert into net_pad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="ipv4Cidr != null">
                ipv4_cidr,
            </if>
            <if test="bindFlag != null">
                bind_flag,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="ipv4Cidr != null">
                #{ipv4Cidr,jdbcType=VARCHAR},
            </if>
            <if test="bindFlag != null">
                #{bindFlag,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

</mapper>