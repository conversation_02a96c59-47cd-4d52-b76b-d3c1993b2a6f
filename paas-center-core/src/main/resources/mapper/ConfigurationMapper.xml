<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.ConfigurationMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.container.Configuration">
        <id column="id" property="id"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <result column="desc" property="desc"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        `key`,
        `value`,
        `desc`,
        delete_flag,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <select id="selectValueByKey" parameterType="java.lang.String" resultType="java.lang.String">
        select value
        from configuration
        where `key` = #{key}
          and delete_flag = false
    </select>
</mapper>