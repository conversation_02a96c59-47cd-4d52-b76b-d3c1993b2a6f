<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.PadRestoreTaskInfoMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.PadRestoreTaskInfo">
    <id column="id" property="id" />
    <result column="restore_name" property="restoreName" />
    <result column="sub_task_id" property="subTaskId" />
    <result column="device_id" property="deviceId" />
    <result column="pad_code" property="padCode" />
    <result column="backup_id" property="backupId" />
    <result column="customer_id" property="customerId" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id, restore_name, sub_task_id, device_id, pad_code, backup_id, customer_id, delete_flag,
    create_time, create_by, update_time, update_by
  </sql>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into pad_restore_task_info
    (restore_name, sub_task_id, device_id, pad_code, backup_id, customer_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.restoreName}, #{item.subTaskId}, #{item.deviceId}, #{item.padCode}, #{item.backupId}, 
        #{item.customerId})
    </foreach>
  </insert>

  <select id="listById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pad_restore_task_info
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and delete_flag = false
  </select>

  <select id="countByPadCodesAndTaskStatusList" resultType="int">
    select count(*)
    from pad_restore_task_info prti
    join pad_task pt on prti.sub_task_id = pt.id
    where prti.pad_code in
    <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
      #{padCode}
    </foreach>

    and pt.status in
    <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
      #{taskStatus}
    </foreach>

    and pt.delete_flag = false
    and prti.delete_flag = false
  </select>
</mapper>