<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.PadBackupTaskInfoMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo">
    <id column="id" property="id" />
    <result column="backup_name" property="backupName" />
    <result column="pad_code" property="padCode" />
    <result column="sub_task_id" property="subTaskId" />
    <result column="backup_size" property="backupSize" />
    <result column="customer_id" property="customerId" />
    <result column="path" property="path" />
    <result column="backup_type" property="backupType" />
    <result column="specification_code" property="specificationCode" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id,
    backup_name,
    pad_code,
    sub_task_id,
    backup_size,
    customer_id,
    `path`,
    backup_type,
    specification_code,
    delete_flag,
    create_time,
    create_by,
    update_time,
    update_by
  </sql>

  <select id="countByPadCodesAndTaskStatusList" resultType="int">
    select count(*)
    from pad_backup_task_info pbti
           join pad_task pt on pbti.sub_task_id = pt.id
    where pbti.pad_code in
    <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
      #{padCode}
    </foreach>

    and pt.status in
    <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
      #{taskStatus}
    </foreach>

    and pt.delete_flag = false
    and pbti.delete_flag = false
  </select>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into pad_backup_task_info
    (backup_name, device_id, pad_code, sub_task_id, backup_size, customer_id, `path`, backup_type, specification_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.backupName}, #{item.deviceId}, #{item.padCode}, #{item.subTaskId}, #{item.backupSize}, #{item.customerId},
      #{item.path}, #{item.backupType}, #{item.specificationCode})
    </foreach>
    </insert>

  <select id="listById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pad_backup_task_info
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and delete_flag = false
  </select>

  <select id="getCustomerLatestPadBackupData" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pad_backup_task_info
    where delete_flag = false
    <if test="customerId != null">
      and customer_id = #{customerId}
    </if>

    <if test="backupId != null">
      and id = #{backupId}
    </if>

    <if test="backupName != null and backupName != ''">
      and backup_name = #{backupName}
    </if>
    order by id desc
    limit 1
  </select>

  <update id="updateBackupSizeBySubTaskId">
    update pad_backup_task_info
    set backup_size = #{backupSize}
    where sub_task_id = #{subTaskId}
    and delete_flag = false
  </update>

  <update id="delPadBackupData">
    UPDATE pad_backup_task_info
    SET delete_flag = 1
    WHERE
            <foreach collection="dataDelDTOS" item="dataDelDTO"  separator="OR">
              (customer_id = #{customerId} AND backup_name = #{dataDelDTO.backupName} AND pad_code = #{dataDelDTO.padCode})
            </foreach>
  </update>

  <select id="hasBackupName" resultType="java.lang.String">
    SELECT
      backup_name
    FROM
      pad_backup_task_info
    WHERE
        <foreach collection="dataDelDTOS" item="dataDelDTO"  separator="OR">
           (customer_id = #{customerId} AND backup_name = #{dataDelDTO.backupName} AND pad_code = #{dataDelDTO.padCode})
        </foreach>
  </select>
</mapper>