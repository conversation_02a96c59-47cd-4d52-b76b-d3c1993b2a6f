<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.rtc.mapper.RtcCustomerConfigMapper">

    <select id="getP2pPushStream" resultType="java.lang.String">
        select p2p_peer_to_peer_push_stream from customer_config where customer_id = #{userId}
    </select>
</mapper>