<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.filecenter.mapper.FileUploadTaskMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="status" property="status"/>
        <result column="file_id" property="fileId"/>
        <result column="original_url" property="originalUrl"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        unique_id,
        task_id,
        `status`,
        file_id,
        original_url,
        error_msg,
        end_time,
        created_time,
        updated_time
    </sql>

    <select id="listByMasterTaskIdAndUniqueIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fc_file_upload_tasks
        where task_id = #{masterTaskId}
        <if test="uniqueIds != null and uniqueIds.size() != 0">
            and unique_id in
            <foreach collection="uniqueIds" item="uniqueId" open="(" separator="," close=")">
                #{uniqueId}
            </foreach>
        </if>
        order by id desc
    </select>

    <update id="updateNotEndFileStatus">
        update fc_file_upload_tasks
        set status = #{status}
        <if test="endDate != null">
            end_time = #{endDate},
        </if>
        <if test="msg != null">
            error_msg = #{msg},
        </if>
        where id = #{id}
        and status in (1, 2)
    </update>

    <update id="batchUpdateStatus">
        update fc_file_upload_tasks
        set status = #{status}
        where status != 3
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="listByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fc_file_upload_tasks
        where task_id = #{taskId} and delete_flag = 0
    </select>

    <update id="update">
        update fc_file_upload_tasks
        <set>
            <if test="uniqueId != null">
                unique_id = #{uniqueId},
            </if>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="fileId != null">
                file_id = #{fileId},
            </if>
            <if test="originalUrl != null">
                original_url = #{originalUrl},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
        </set>

        where id = #{id}
    </update>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fc_file_upload_tasks
        where id = #{id}
    </select>

    <select id="listVOByMasterUniqueId" resultType="net.armcloud.paascenter.common.model.vo.api.FileTaskInfoVo">
        select t2.task_id                as taskId,
               t2.file_id                as customerFileId,
               t2.status                          as taskStatus,
               UNIX_TIMESTAMP(t2.end_time) * 1000 as endTime
        from task t1
                 join fc_file_upload_tasks t2 on t1.id = t2.task_id
        where t1.unique_id = #{taskBatchId}
        and t2.delete_flag = 0
        order by t2.id
    </select>

    <select id="listWaitDownloadTaskByFileCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fc_file_upload_tasks
        where file_id in
        <foreach collection="fileCustomerIds" item="fileCustomerId" open="(" separator="," close=")">
            #{fileCustomerId}
        </foreach>
        and status = 1
        and delete_flag = 0
    </select>

    <select id="listVOByCustomerTaskIdsAndCustomerId"
            resultType="net.armcloud.paascenter.common.model.vo.api.FileTaskInfoVo">
        select
        t1.task_id as taskId,
        t1.file_id as customerFileId,
        t1.status as taskStatus,
        UNIX_TIMESTAMP(t1.end_time) * 1000 as endTime
        from fc_file_upload_tasks t1
        left join task t2 on t1.task_id = t2.id
        where t1.customer_id = #{customerId}
        and t1.delete_flag = 0
        <if test="taskIds != null and taskIds.size() != 0">
            and t1.task_id in
            <foreach collection="taskIds" item="customerTaskId" open="(" separator="," close=")">
                #{customerTaskId}
            </foreach>
        </if>
        <if test="types != null and types.size() != 0">
            and t2.type in
            <foreach collection="types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by t1.id
    </select>

    <update id="cancel">
        update fc_file_upload_tasks
        set status = -3
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status in (1)
    </update>
</mapper>