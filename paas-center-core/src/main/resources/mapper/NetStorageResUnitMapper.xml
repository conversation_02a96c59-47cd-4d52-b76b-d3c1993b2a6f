<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.NetStorageResUnitMapper">


    <select id="getClusterUsedSizeTotal" resultType="java.math.BigDecimal">
        SELECT ROUND(SUM(CAST(net_storage_res_unit_used_size AS DECIMAL(18, 4))), 1) AS storage_capacity_used
        FROM net_storage_res_unit
        WHERE customer_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>