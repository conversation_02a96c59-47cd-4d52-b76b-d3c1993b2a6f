<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.NetStorageResOffLogMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.NetStorageResOffLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="pad_code" property="padCode" jdbcType="VARCHAR"/>
        <result column="device_ip" property="deviceIp" jdbcType="VARCHAR"/>
        <result column="net_storage_res_id" property="netStorageResId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        net_storage_res_off_log_id, pad_code, device_ip, net_storage_res_id, create_time
    </sql>
    <select id="getByPadCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from net_storage_res_off_log
        where pad_code = #{padCode}
    </select>

    <select id="getByPadCodeList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from net_storage_res_off_log
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        order by net_storage_res_off_log_id desc
    </select>

    <insert id="batchInsert">
        insert into net_storage_res_off_log
        (pad_code, device_ip, net_storage_res_id, create_time)
        values
        <foreach collection="netStorageResOffLogList" item="item" separator=",">
            (#{item.padCode}, #{item.deviceIp}, #{item.netStorageResId}, #{item.createTime})
        </foreach>
    </insert>
</mapper>