<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerMapper">

  <select id="getCustomerInfoById" resultType="net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo">
    select id, customer_account as customerAccount,customer_name as customerName, status from customer where id = #{customerId} and status = 1
  </select>


  <select id="queryImageList" resultType="net.armcloud.paascenter.openapi.model.vo.QueryImageVO">
    select
    t1.unique_id as imageId,
    t1.image_tag as imageName,
    t1.image_tag as imageTag,
    t1.server_type as serverType,
    t1.rom_version as romVersion,
    t1.image_desc as imageDesc,
    t1.release_type as releaseType
    from customer_upload_image t1
    where
    t1.delete_flag = 0 and t1.type != 1 and t1.status = 2

    <if test="param.imageType == null">
      and( t1.customer_id = #{param.customerId} or customer_id is null)
    </if>
    <if test="param.imageType != null and param.imageType == 1">
      and customer_id is null
    </if>
    <if test="param.imageType != null and param.imageType == 2">
      and customer_id = #{param.customerId}
    </if>
    <if test="param.releaseType != null and param.releaseType != ''">
      and t1.release_type = #{param.releaseType}
    </if>

    <if test="param.romVersion != null and param.romVersion != ''">
      and t1.rom_version = #{param.romVersion}
    </if>

    <if test="param.createTimeStart != null and param.createTimeStart !=''">
      AND t1.create_time &gt;= #{param.createTimeStart}
    </if>
    <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
      AND t1.create_time &lt;= #{param.createTimeEnd}
    </if>

    order by t1.create_time desc
  </select>
</mapper>