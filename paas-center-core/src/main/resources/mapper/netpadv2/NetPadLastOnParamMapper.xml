<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.netpadv2.mapper.NetPadLastOnParamMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam">
        <id column="pad_code" jdbcType="VARCHAR" property="padCode"/>
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode"/>
        <result column="device_ip" jdbcType="VARCHAR" property="deviceIp"/>
        <result column="cluster_code" jdbcType="VARCHAR" property="clusterCode"/>
        <result column="arm_server_code" jdbcType="VARCHAR" property="armServerCode"/>
        <result column="compute_unit_code" jdbcType="VARCHAR" property="computeUnitCode"/>
        <result column="boot_on_success" jdbcType="INTEGER" property="bootOnSuccess"/>
        <result column="boot_on_run_time" jdbcType="BIGINT" property="bootOnRunTime"/>
        <result column="boot_on_time" jdbcType="TIMESTAMP" property="bootOnTime"/>
        <result column="off_time" jdbcType="TIMESTAMP" property="offTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        pad_code, device_code, device_ip, cluster_code, arm_server_code, compute_unit_code,
        boot_on_success, boot_on_run_time, boot_on_time, off_time,
        create_time, update_time
    </sql>

    <!-- 批量插入或更新开机参数记录 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO net_pad_last_on_param (
            pad_code, device_code, device_ip, cluster_code, arm_server_code, compute_unit_code,
            boot_on_success, boot_on_run_time, boot_on_time, off_time,
            create_time, update_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.padCode}, #{record.deviceCode}, #{record.deviceIp},
                #{record.clusterCode}, #{record.armServerCode}, #{record.computeUnitCode},
                #{record.bootOnSuccess}, #{record.bootOnRunTime},
                #{record.bootOnTime}, #{record.offTime},
                #{record.createTime}, #{record.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            device_code = VALUES(device_code),
            device_ip = VALUES(device_ip),
            cluster_code = VALUES(cluster_code),
            arm_server_code = VALUES(arm_server_code),
            compute_unit_code = VALUES(compute_unit_code),
            boot_on_success = VALUES(boot_on_success),
            boot_on_run_time = VALUES(boot_on_run_time),
            boot_on_time = VALUES(boot_on_time),
            off_time = VALUES(off_time),
            update_time = VALUES(update_time)
    </insert>

    <!-- 更新开机成功状态和耗时 -->
    <update id="updateBootOnSuccess">
        UPDATE net_pad_last_on_param 
        SET boot_on_success = #{bootOnSuccess},
            boot_on_run_time = TIMESTAMPDIFF(SECOND, boot_on_time, now()),
            update_time = NOW()
        WHERE pad_code = #{padCode}
    </update>

    <!-- 更新关机时间 -->
    <update id="updateOffTime">
        UPDATE net_pad_last_on_param 
        SET off_time = #{offTime},
            update_time = NOW()
        WHERE pad_code = #{padCode}
    </update>

    <!-- 根据实例编码删除记录 -->
    <delete id="deleteByPadCode">
        DELETE FROM net_pad_last_on_param WHERE pad_code = #{padCode}
    </delete>

    <!-- 批量删除记录 -->
    <delete id="batchDeleteByPadCodes">
        DELETE FROM net_pad_last_on_param 
        WHERE pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </delete>

    <!-- 根据实例编码查询开机参数记录 -->
    <select id="selectByPadCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM net_pad_last_on_param
        WHERE pad_code = #{padCode}
    </select>

    <!-- 批量查询开机参数记录 -->
    <select id="selectByPadCodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM net_pad_last_on_param
        WHERE pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

</mapper>
