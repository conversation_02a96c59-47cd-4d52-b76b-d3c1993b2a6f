<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.DevicePadMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.DevicePad">
      <id column="id" property="id" />
      <result column="device_id" property="deviceId" />
      <result column="pad_id" property="padId" />
  </resultMap>

    <sql id="Base_Column_List">
        id, device_id, pad_id
    </sql>

    <select id="listByPadCode" resultType="net.armcloud.paascenter.openapi.model.vo.DevicePadVO">
        select p.pad_code   as padCode,
               dp.device_id as deviceId,
                d.device_status as deviceStatus,
               d.device_ip
        from device_pad dp
                 join pad p on dp.pad_id = p.id
                 join device d on dp.device_id = d.id
        where p.pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <!-- 批量插入 batchInsert 方法 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO device_pad (device_id, pad_id) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId}, #{item.padId})
        </foreach>
        ON DUPLICATE KEY UPDATE device_id = VALUES(device_id)
    </insert>
</mapper>