package net.armcloud.paascenter.openapi.netpadv2.service;

import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam;
import net.armcloud.paascenter.openapi.netpadv2.service.impl.NetPadLastOnParamServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 网存实例开机参数记录服务测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@SpringBootTest
@ActiveProfiles("test")
public class NetPadLastOnParamServiceTest {

    @Resource
    private NetPadLastOnParamService netPadLastOnParamService;

    @Test
    public void testRecordBootOnParams() {
        // 测试记录开机参数
        String padCode = "test-pad-001";
        String deviceCode = "device-001";
        String deviceIp = "*************";
        String clusterCode = "cluster-001";
        String armServerCode = "server-001";
        String computeUnitCode = "compute-001";

        netPadLastOnParamService.recordBootOnParams(padCode, deviceCode, deviceIp, clusterCode, armServerCode, computeUnitCode);

        // 验证记录是否成功
        NetPadLastOnParam record = netPadLastOnParamService.getByPadCode(padCode);
        assert record != null;
        assert padCode.equals(record.getPadCode());
        assert deviceCode.equals(record.getDeviceCode());
        assert deviceIp.equals(record.getDeviceIp());
        assert clusterCode.equals(record.getClusterCode());
        assert armServerCode.equals(record.getArmServerCode());
        assert computeUnitCode.equals(record.getComputeUnitCode());
    }

    @Test
    public void testBatchRecordBootOnParams() {
        // 测试批量记录开机参数
        List<NetPadLastOnParam> records = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            NetPadLastOnParam record = NetPadLastOnParam.create(
                    "test-pad-" + String.format("%03d", i),
                    "device-" + String.format("%03d", i),
                    "192.168.1." + (100 + i),
                    "cluster-001",
                    "server-001",
                    "compute-" + String.format("%03d", i)
            );
            records.add(record);
        }

        netPadLastOnParamService.batchRecordBootOnParams(records);
        
        // 验证批量记录是否成功
        List<String> padCodes = new ArrayList<>();
        for (NetPadLastOnParam record : records) {
            padCodes.add(record.getPadCode());
        }
        
        List<NetPadLastOnParam> queryRecords = netPadLastOnParamService.getByPadCodes(padCodes);
        assert queryRecords != null;
        assert queryRecords.size() == 3;
    }

    @Test
    public void testUpdateBootOnResult() {
        // 先记录开机参数
        String padCode = "test-pad-boot-result";
        netPadLastOnParamService.recordBootOnParams(padCode, "device-001", "*************", "cluster-001", "server-001", "compute-001");
        
        // 测试更新开机成功结果
        Date startTime = new Date(System.currentTimeMillis() - 5000); // 5秒前开始开机
        netPadLastOnParamService.updateBootOnResult(padCode, true, startTime);
        
        // 验证更新是否成功
        NetPadLastOnParam record = netPadLastOnParamService.getByPadCode(padCode);
        assert record != null;
        assert record.getBootOnSuccess() != null;
        assert record.getBootOnSuccess() == 1;
        assert record.getBootOnRunTime() != null;
        assert record.getBootOnRunTime() > 0;
    }

    @Test
    public void testUpdateOffTime() {
        // 先记录开机参数
        String padCode = "test-pad-off-time";
        netPadLastOnParamService.recordBootOnParams(padCode, "device-001", "*************", "cluster-001", "server-001", "compute-001");
        
        // 测试更新关机时间
        netPadLastOnParamService.updateOffTime(padCode);
        
        // 验证更新是否成功
        NetPadLastOnParam record = netPadLastOnParamService.getByPadCode(padCode);
        assert record != null;
        assert record.getOffTime() != null;
    }

    @Test
    public void testDeleteByPadCode() {
        // 先记录开机参数
        String padCode = "test-pad-delete";
        netPadLastOnParamService.recordBootOnParams(padCode, "device-001", "*************", "cluster-001", "server-001", "compute-001");
        
        // 验证记录存在
        NetPadLastOnParam record = netPadLastOnParamService.getByPadCode(padCode);
        assert record != null;
        
        // 测试删除记录
        netPadLastOnParamService.deleteByPadCode(padCode);
        
        // 验证记录已删除
        NetPadLastOnParam deletedRecord = netPadLastOnParamService.getByPadCode(padCode);
        assert deletedRecord == null;
    }

    @Test
    public void testBatchUpdateOffTime() {
        // 先批量记录开机参数
        List<String> padCodes = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            String padCode = "test-pad-batch-off-" + String.format("%03d", i);
            padCodes.add(padCode);
            netPadLastOnParamService.recordBootOnParams(padCode, "device-" + String.format("%03d", i),
                    "192.168.1." + (100 + i), "cluster-001", "server-001", "compute-" + String.format("%03d", i));
        }
        
        // 测试批量更新关机时间
        netPadLastOnParamService.batchUpdateOffTime(padCodes);
        
        // 验证批量更新是否成功
        List<NetPadLastOnParam> records = netPadLastOnParamService.getByPadCodes(padCodes);
        assert records != null;
        assert records.size() == 3;
        for (NetPadLastOnParam record : records) {
            assert record.getOffTime() != null;
        }
    }
}
