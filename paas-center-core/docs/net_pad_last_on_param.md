# 网存实例开机参数记录功能

## 功能概述

本功能用于记录网存实例开机时使用的存储、算力、板卡编号、板卡IP、集群、服务器，以及开机是否成功、开机耗时、开机时间、关机时间等信息。

## 数据库表结构

### net_pad_last_on_param 表

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| pad_code | varchar(64) | 实例编码 | 主键 |
| device_code | varchar(64) | 板卡编号 | |
| device_ip | varchar(32) | 板卡IP | |
| cluster_code | varchar(64) | 集群编码 | |
| arm_server_code | varchar(64) | 服务器编码 | |
| compute_unit_code | varchar(64) | 算力编号 | |
| boot_on_success | tinyint(1) | 开机是否成功 | 0:失败 1:成功 |
| boot_on_run_time | bigint(20) | 开机耗时（毫秒） | |
| boot_on_time | datetime | 开机时间 | |
| off_time | datetime | 关机时间 | |
| create_time | datetime | 创建时间 | 默认当前时间 |
| update_time | datetime | 更新时间 | 自动更新 |

## 核心类说明

### 1. NetPadLastOnParam 实体类
- 位置：`net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam`
- 功能：网存实例开机参数记录的实体类
- 主要方法：
  - `create()`: 创建工厂方法
  - `updateBootOnSuccess()`: 更新开机成功状态和耗时
  - `updateOffTime()`: 更新关机时间

### 2. NetPadLastOnParamMapper 接口
- 位置：`net.armcloud.paascenter.openapi.netpadv2.mapper.NetPadLastOnParamMapper`
- 功能：数据库操作接口
- 主要方法：
  - `batchInsertOrUpdate()`: 批量插入或更新
  - `updateBootOnSuccess()`: 更新开机成功状态
  - `updateOffTime()`: 更新关机时间
  - `deleteByPadCode()`: 删除记录

### 3. NetPadLastOnParamService 服务接口
- 位置：`net.armcloud.paascenter.openapi.netpadv2.service.NetPadLastOnParamService`
- 功能：业务逻辑接口
- 主要方法：
  - `recordBootOnParams()`: 记录开机参数
  - `updateBootOnResult()`: 更新开机结果
  - `updateOffTime()`: 更新关机时间
  - `deleteByPadCode()`: 删除记录

## 功能集成点

### 1. 网存实例开机时记录参数

#### NetPadV2ServiceImpl.initData()
- 在V2版本中，开机参数记录逻辑已移入到 `initData()` 方法中实现
- 调用 `recordBootOnParamsInInitData()` 方法，从initData方法的上下文中获取所需字段
- 直接从 `NetPadRelation` 对象中获取设备编号、IP、算力编号、服务器编码等信息

#### PadServiceImpl.netStorageResBootOn()
- 在旧版本开机接口中，记录开机参数的参数都从 `List<NetStorageDTO>` 中获取
- 调用 `recordBootOnParamsForOldApi()` 方法，从NetStorageDTO结构中提取所需信息

### 2. 开机任务状态更新时更新记录

#### TaskService.updateContainerInstanceTaskResult()
- 开机成功时：调用 `updateBootOnResult(padCode, true, startTime)` 更新成功状态和耗时
- 开机失败时：调用 `updateBootOnResult(padCode, false, null)` 更新失败状态

### 3. 关机时更新关机时间

#### TaskService.updateContainerInstanceTaskResult()
- 关机成功或失败时：调用 `updateOffTime(padCode)` 更新关机时间

### 4. 删除实例时清理记录

#### PadServiceImpl.deletePadInformation()
- 在删除实例时调用 `deleteByPadCode(padCode)` 删除对应的开机参数记录

## 使用示例

### 1. 记录开机参数
```java
netPadLastOnParamService.recordBootOnParams(
    "pad-001",
    "device-001",
    "192.168.1.100",
    "cluster-001",
    "server-001",
    "compute-001"
);
```

### 2. 更新开机结果
```java
// 开机成功
Date startTime = new Date(System.currentTimeMillis() - 5000);
netPadLastOnParamService.updateBootOnResult("pad-001", true, startTime);

// 开机失败
netPadLastOnParamService.updateBootOnResult("pad-001", false, null);
```

### 3. 更新关机时间
```java
netPadLastOnParamService.updateOffTime("pad-001");
```

### 4. 删除记录
```java
netPadLastOnParamService.deleteByPadCode("pad-001");
```

## 数据流程

1. **开机流程**：
   - 用户调用开机接口
   - 系统分配设备资源和算力资源
   - **V2版本**：在initData方法中记录开机参数（从NetPadRelation上下文获取）
   - **旧版本**：从List<NetStorageDTO>中获取开机参数并记录
   - 创建开机任务
   - 执行开机任务
   - **更新开机结果**（成功/失败、耗时）

2. **关机流程**：
   - 用户调用关机接口
   - 执行关机任务
   - **更新关机时间**

3. **删除流程**：
   - 用户调用删除接口
   - 删除实例相关数据
   - **删除开机参数记录**

## 注意事项

1. 所有数据库操作都有异常处理，不会影响主流程
2. 支持批量操作，提高性能
3. 开机耗时计算基于任务创建时间和完成时间
4. 关机失败时也会记录关机时间
5. 删除实例时会同步删除开机参数记录，避免数据冗余
6. **V2版本**：开机参数记录已移入initData方法，从上下文直接获取字段信息
7. **旧版本**：开机参数从List<NetStorageDTO>结构中提取，确保数据来源的一致性

## 测试

测试类位置：`net.armcloud.paascenter.openapi.netpadv2.service.NetPadLastOnParamServiceTest`

包含以下测试用例：
- 记录开机参数测试
- 批量记录开机参数测试
- 更新开机结果测试
- 更新关机时间测试
- 删除记录测试
- 批量更新关机时间测试
